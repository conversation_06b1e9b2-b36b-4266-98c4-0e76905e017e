/** @type {import('next').NextConfig} */
const nextConfig = {
  // disable indexing for staging environment
  async headers() {
    const headers = [];
    if (process.env.ENVIRONMENT === "staging") {
      headers.push({
        headers: [
          {
            key: "X-Robots-Tag",
            value: "noindex, nofollow",
          },
        ],
        source: "/:path*",
      });
    }
    return headers;
  },
  async redirects() {
    return [
      // {
      //   source: "/product/:slug",
      //   destination: "/products/:slug",
      //   permanent: true,
      // },
      // {
      //   source: "/lentreprise",
      //   destination: "/about",
      //   permanent: true,
      // },
      // {
      //   source: "/a-propos-de-nous",
      //   destination: "/about",
      //   permanent: true,
      // },
      // {
      //   source: "/centre-d-aide",
      //   destination: "/help-center",
      //   permanent: true,
      // },
      // {
      //   source: "/contactez-nous",
      //   destination: "/contact",
      //   permanent: true,
      // },
      // {
      //   source: "/simulateurs/note-de-calcul",
      //   destination: "/simulators/calculation-notes",
      //   permanent: true,
      // },
      // {
      //   source: "/simulateurs/injection",
      //   destination: "/simulators/injection",
      //   permanent: true,
      // },
      // {
      //   source: "/simulateurs/pompage",
      //   destination: "/simulators/pumping",
      //   permanent: true,
      // },
    ];
  },
};

export default nextConfig;
