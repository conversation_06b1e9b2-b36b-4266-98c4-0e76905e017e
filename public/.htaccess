<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /

  # Redirect to the open graph route
  # To avoid conflict with our client routing we use the share param
  RewriteCond %{QUERY_STRING} ^share=1$
  RewriteRule ^product/(.*)$ https://admin.ecowatt.ma/og/product/$1 [L,R=301]

  RewriteRule ^index\.html$ - [L]
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteCond %{REQUEST_FILENAME} !-l
  RewriteRule . /index.html [L]
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php83” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php83 .php .php8 .phtml
</IfModule>
# php -- <PERSON><PERSON> cPanel-generated handler, do not edit
