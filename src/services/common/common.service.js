import http from "@/lib/http";

export class CommonService {
  static async getSiteSettings() {
    return await http.get(`/settings`).then((res) => res.data);
  }

  static async getSlide(code) {
    return await http.get(`/slide/${code}`).then((res) => res.data);
  }

  static async getFeaturedCategories() {
    return await http.get("/featured-categories").then((res) => res.data);
  }

  static async getStructuredCategories() {
    return await http.get("/structured-categories").then((res) => res.data);
  }

  static async getBestOffers() {
    return await http.get("/best-offers").then((res) => res.data);
  }

  static async getFeaturedBrands() {
    return await http.get("/featured-brands").then((res) => res.data);
  }

  static async getBestSellers() {
    return await http.get("/best-sellers").then((res) => res.data);
  }

  static async getArrivalProducts() {
    return await http.get("/arrival-products").then((res) => res.data);
  }

  static async getOffers() {
    return await http.get("/offers").then((res) => res.data);
  }

  static async getBanner(code) {
    return await http.get(`/banner/${code}`).then((res) => res.data);
  }

  static async getCountryCities(code) {
    // TODO: change api route to [GET]: /countries/{country}/cities
    return await http.get(`/cities/${code}`).then((res) => res.data);
  }

  static async getCountries() {
    return await http.get("/countries").then((res) => res.data);
  }

  static async registerNewsletter(data) {
    return await http.post("/newsletter", data);
  }

  static async sendContact(data) {
    return await http.post("/contact", data).then((res) => res.data);
  }

  static async getFundingProduct(params) {
    // TODO: change api route to [GET]: /funding/product
    return await http
      .get("/simulator/funding/product", {
        params,
      })
      .then((res) => res.data);
  }

  static async getFundingSettings() {
    // TODO: change api route to [GET]: /funding/settings
    return await http
      .get("/simulator/funding/settings")
      .then((res) => res.data);
  }

  static async submitFundingRequest(data) {
    return await http.post("/simulator/funding/wafasalaf", data);
  }

  static async getPumpingSimulatorSolution(data) {
    // TODO: change api route to [POST]: /simulator/pumping/solution
    return await http
      .post("/simulator/pumping-solution", data)
      .then((res) => res.data);
  }

  static async getInjectionSimulatorSolution(data) {
    // TODO: change api route to [POST]: /simulator/injection/solution
    return await http
      .post("/simulator/injection-solution", data)
      .then((res) => res.data);
  }

  static async sendInjectionForm(data) {
    return await http
      .post("/simulator/injection/form", data)
      .then((res) => res.data);
  }
}
