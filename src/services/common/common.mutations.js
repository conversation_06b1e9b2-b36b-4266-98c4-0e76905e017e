"use client";

import { CommonService } from "@/services/common/common.service";
import { useMutation } from "react-query";

export function useRegisterNewsletterMutation() {
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CommonService.registerNewsletter
  );

  return {
    registerNewsletter: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useSendContactMutation() {
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CommonService.sendContact
  );

  return {
    sendContact: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useSubmitFundingRequestMutation() {
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CommonService.submitFundingRequest
  );

  return {
    submitFundingRequest: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useSendInjectionFormMutation() {
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CommonService.sendInjectionForm
  );

  return {
    sendInjectionForm: mutate,
    isLoading,
    isSuccess,
    error,
  };
}
