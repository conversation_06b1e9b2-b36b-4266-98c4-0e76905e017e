"use client";

import { useAuthContext } from "@/providers/auth-provider";
import { useQuery } from "react-query";
import { CustomerService } from "./customer.service";

export function useInvoices(params) {
  const { data, isLoading } = useQuery(
    ["invoices", params],
    () => CustomerService.getInvoices(params),
    {
      staleTime: Infinity,
    }
  );

  return {
    invoices: data,
    isLoading,
  };
}

export function useOrders(params) {
  const { data, isLoading } = useQuery(
    ["orders", params],
    () => CustomerService.getOrders(params),
    {
      staleTime: Infinity,
    }
  );

  return {
    orders: data,
    isLoading,
  };
}

export function useShippings(params) {
  const { data, isLoading } = useQuery(
    ["shippings", params],
    () => CustomerService.getShippings(params),
    {
      staleTime: Infinity,
    }
  );

  return {
    shippings: data,
    isLoading,
  };
}

export function useAddresses() {
  const { authenticated } = useAuthContext();

  const { data, isLoading, isFetching } = useQuery(
    "addresses",
    CustomerService.getAddresses,
    {
      enabled: authenticated,
    }
  );

  return {
    addresses: data,
    isLoading,
    isFetching,
  };
}
