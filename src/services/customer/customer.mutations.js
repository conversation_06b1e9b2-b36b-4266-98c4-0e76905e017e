"use client";

import { useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "react-query";
import { CustomerService } from "./customer.service";

export function useCreateAddressMutation() {
  const queryClient = useQueryClient();

  const { mutate, isLoading, isSuccess, error } = useMutation(
    CustomerService.createAddress,
    {
      onSuccess() {
        queryClient.invalidateQueries("addresses");
      },
    }
  );

  return {
    createAddress: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useDeleteAddressMutation() {
  const queryClient = useQueryClient();

  const { mutate, isLoading, error } = useMutation(
    CustomerService.deleteAddress,
    {
      onSuccess() {
        queryClient.invalidateQueries("addresses");
      },
    }
  );

  return {
    deleteAddress: mutate,
    isLoading,
    error,
  };
}

export function useUpdateAddressMutation() {
  const queryClient = useQueryClient();

  const { mutate, isLoading, isSuccess, error } = useMutation(
    CustomerService.updateAddress,
    {
      onSuccess() {
        queryClient.invalidateQueries("addresses");
      },
    }
  );

  return {
    updateAddress: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useClaimCreditsMutation() {
  const router = useRouter();
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CustomerService.claimCredits,
    {
      onSuccess() {
        router.refresh();
      },
    }
  );

  return {
    claimCredits: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useUpdateProfileMutation() {
  const router = useRouter();
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CustomerService.updateProfile,
    {
      onSuccess() {
        router.refresh();
      },
    }
  );

  return {
    updateProfile: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useUpdatePasswordMutation() {
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CustomerService.updatePassword
  );

  return {
    updatePassword: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useDownloadInvoiceDocumentMutation() {
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CustomerService.downloadInvoiceDocument
  );

  return {
    downloadInvoiceDocument: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useDownloadShippingDocumentMutation() {
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CustomerService.downloadShippingDocument
  );

  return {
    downloadShippingDocument: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useDownloadOrderDocumentMutation() {
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CustomerService.downloadOrderDocument
  );

  return {
    downloadOrderDocument: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useCheckOrder() {
  const { mutate, isLoading, data, isSuccess, error } = useMutation(
    CustomerService.getOrder
  );

  return {
    checkOrder: mutate,
    order: data,
    isLoading,
    isSuccess,
    error,
  };
}

export function useSubmitProductReviewMutation() {
  const { mutate, isLoading, isSuccess, error } = useMutation(
    CustomerService.submitProductReview
  );

  return {
    submitProductReview: mutate,
    isLoading,
    isSuccess,
    error,
  };
}
