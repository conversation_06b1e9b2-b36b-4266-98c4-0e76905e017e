import { toFormData } from "@/lib/helpers";
import http from "@/lib/http";

export class CustomerService {
  static async createAddress(data) {
    // TODO: change api route to [GET]: /addresses
    return await http.post("/add-address", data);
  }

  static async updateAddress({ id, data }) {
    // TODO: change api route to [PUT]: /addresses/{id}
    return await http.post(`/update-address/${id}`, data);
  }

  static async deleteAddress(id) {
    // TODO: change api route to [DELETE]: /addresses/{id}
    return await http.get(`/remove-address/${id}`);
  }

  static async claimCredits() {
    return await http.post("/credits/claim", null).then((res) => res.data);
  }

  static async updateProfile(data) {
    // TODO: change api route to [PUT]: /user
    return await http
      .post("/customer-update", toFormData(data))
      .then((res) => res.data);
  }

  static async getInvoices(params) {
    return await http.get("/invoices", { params });
  }

  static async getOrder(data) {
    return await http.post("/order", data).then((res) => res.data);
  }

  static async getOrders(params) {
    return await http.get("/orders", { params });
  }

  static async getShippings(params) {
    // TODO: change api route to [GET]: /shippings
    return await http.get("/bls", { params });
  }

  static async updatePassword(data) {
    // TODO: change api route to [PUT]: /user/password
    return await http.post("/customer-update-password", data);
  }

  static async getAddresses() {
    // TODO: change api route to [GET]: /user/addresses
    return await http.get("/addresses").then((res) => res.data);
  }

  static async downloadInvoiceDocument(data) {
    // TODO: change api route to [POST]: /document/invoice/{invoice}
    return await http.post("/download-invoice", data).then((res) => res.data);
  }

  static async downloadShippingDocument(data) {
    // TODO: change api route to [POST]: /document/shipping/{shipping}
    return await http.post("/download-bl", data).then((res) => res.data);
  }

  static async downloadOrderDocument(data) {
    // TODO: change api route to [POST]: /document/order/{order}
    return await http.post("/download-bc", data).then((res) => res.data);
  }

  static async submitProductReview({ productId, data }) {
    return await http
      .post(`/products/${productId}/reviews`, data)
      .then((res) => res.data);
  }
}
