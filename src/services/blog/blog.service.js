import { toFormData } from "@/lib/helpers";
import http from "@/lib/http";
import { notFound } from "next/navigation";

export class BlogService {
  static async getBlogsFeed() {
    return await http.get(`/blogs-feed`);
  }

  static async getBlogs(params) {
    return await http
      .get(`/blogs`, {
        params,
      })
      .then((res) => res.data);
  }

  static async getBlog(slug) {
    return await http
      .get(`/blogs/${slug}`)
      .then((res) => res.data)
      .catch((err) => {
        if (err?.statusCode === 404) {
          notFound();
        }
      });
  }

  static async getBlogCategories() {
    return await http.get("/blogs/categories").then((res) => res.data);
  }

  static async submitBlog(data) {
    return await http.post(`/blogs/submit`, toFormData(data));
  }
}
