"use client";

import { useRouter } from "next/navigation";
import { useMutation } from "react-query";
import { BlogService } from "./blog.service";

export function useSubmitBlogMutation() {
  const router = useRouter();
  const { mutate, isLoading, isSuccess, error } = useMutation(
    BlogService.submitBlog,
    {
      onSuccess() {
        router.refresh();
      },
    }
  );

  return {
    submitBlog: mutate,
    isLoading,
    isSuccess,
    error,
  };
}
