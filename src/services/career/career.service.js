import { toFormData } from "@/lib/helpers";
import http from "@/lib/http";
import { notFound } from "next/navigation";

export class CareerService {
  static async getCareers(params) {
    return await http
      .get("/careers", {
        params,
      })
      .then((res) => res.data);
  }

  static async getCareer(slug) {
    return await http
      .get(`/careers/${slug}`)
      .then((res) => res.data)
      .catch((err) => {
        if (err?.statusCode === 404) {
          notFound();
        }
      });
  }

  static async applyCareer({ slug, data }) {
    // TODO: use id instead of slug
    return await http
      .post(`/careers/${slug}/apply`, toFormData(data))
      .then((res) => res.data);
  }
}
