import http from "@/lib/http";
import { notFound } from "next/navigation";

export class GalleryService {
  static async getGalleries(params) {
    return await http
      .get("/galleries", {
        params,
      })
      .then((res) => res.data);
  }

  static async getGallery(slug) {
    return await http
      .get(`/galleries/${slug}`)
      .then((res) => res.data)
      .catch((err) => {
        if (err?.statusCode === 404) {
          notFound();
        }
      });
  }
}
