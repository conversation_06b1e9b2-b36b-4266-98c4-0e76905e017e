"use client";

import { useMutation } from "react-query";
import { AuthService } from "./auth.service";

export function useLoginMutation() {
  const { mutate, error, isLoading } = useMutation(AuthService.login, {});

  return {
    login: mutate,
    isLoading,
    error,
  };
}

export function useRegisterMutation() {
  const { mutate, error, isLoading } = useMutation(AuthService.register, {});

  return {
    register: mutate,
    isLoading,
    error,
  };
}

export function useLogoutMutation() {
  const { mutate, error, isLoading } = useMutation(AuthService.logout, {
    onSuccess() {
      localStorage.removeItem("ecowattAuthToken");
      window.location.href = "/";
    },
  });

  return {
    logout: mutate,
    isLoading,
    error,
  };
}

export function useRequestPasswordResetMutation() {
  const { mutate, error, isLoading, isSuccess } = useMutation(
    AuthService.requestPasswordReset
  );

  return {
    requestPasswordReset: mutate,
    isLoading,
    isSuccess,
    error,
  };
}

export function useResetPasswordMutation() {
  const { mutate, error, isLoading, isSuccess } = useMutation(
    AuthService.resetPassword
  );

  return {
    resetPassword: mutate,
    isLoading,
    isSuccess,
    error,
  };
}
