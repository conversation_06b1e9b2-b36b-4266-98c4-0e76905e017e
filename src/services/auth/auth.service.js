import { toFormData } from "@/lib/helpers";
import http from "@/lib/http";

export class AuthService {
  static async getCurrentUser() {
    return await http
      .get("/customer-informations")
      .then((res) => res.data)
      .catch(() => null);
  }

  static async login(data) {
    return await http.post("/login", data);
  }

  static async register(data) {
    return await http.post("/register", toFormData(data));
  }

  static async logout() {
    return await http.post("/logout", null);
  }

  static async requestPasswordReset(data) {
    return await http.post("/forgot-password", data);
  }

  static async resetPassword(data) {
    return await http.post("/reset-password", data);
  }
}
