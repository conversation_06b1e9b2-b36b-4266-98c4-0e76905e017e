"use client";

import { useQuery } from "react-query";
import { ProductService } from "./product.service";

export function useSearchProducts(params) {
  const { data, isLoading } = useQuery(
    ["products", "search", params],
    () => ProductService.searchProducts(params),
    {
      enabled: params?.title?.length > 0,
    }
  );

  return {
    products: data,
    isLoading,
  };
}

export function useProductReviews(productId, params) {
  const { data, isLoading } = useQuery(
    ["products", productId, "reviews", params],
    () => ProductService.getProductReviews(productId, params),
    {
      keepPreviousData: true,
    }
  );

  return {
    reviews: data,
    isLoading,
  };
}
