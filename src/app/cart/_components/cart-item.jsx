"use client";

import ProductPrice from "@/shared/components/product-price";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Minus, Plus, Trash2 } from "react-feather";
import { TailSpin } from "react-loader-spinner";
import { useAuthContext } from "@/providers/auth-provider";

export default function CartItem({
  newDate,
  item,
  loading,
  handleUpdateToCard,
  RemoveCartItem,
}) {
  const { authenticated } = useAuthContext();
  const [quantity, setQuantity] = useState(item?.quantity);
  const [load, setLoad] = useState(false);
  const [quantityUpdated, setQuantityUpdated] = useState(false);

  useEffect(() => {
    let id = authenticated ? item?.cart_id : item?.id;

    if (loading === id && !load) {
      setLoad(true);
    } else if (load) {
      setLoad(false);
    }
  }, [loading]);

  useEffect(() => {
    if (quantityUpdated) {
      updateItem();
      setQuantityUpdated(false);
    }
  }, [quantityUpdated]);

  const toggleQuantity = async (move) => {
    if (move === "minus") {
      if (quantity > 1) {
        setQuantity(quantity - 1);
        setQuantityUpdated(true);
      }
    } else {
      setQuantity(quantity + 1);
      setQuantityUpdated(true);
    }
  };
  const addQuantity = async (value) => {
    if (value > 1) {
      setQuantity(parseInt(value));
      // setQuantityUpdated(true)
    } else {
      setQuantity(1);
      // setQuantityUpdated(true)
    }
  };
  const blurQuantity = async () => {
    setQuantityUpdated(true);
  };

  const updateItem = async () => {
    // let data = (authenticated) ? item?.cart_id : item?.id
    let data = { cart_id: item?.cart_id, id: item?.id, quantity: quantity };
    // console.log(data)
    handleUpdateToCard(data);
  };
  const RemoveItem = async () => {
    let id = authenticated ? item?.cart_id : item?.id;
    RemoveCartItem(id);
  };

  return (
    <>
      {/* {(item?.is_active === 2) ? 
                <tr>
                    <td colSpan={5} className='pb-2 pt-3 border-0'>
                        <p className={`m-0 text-primary font-bold`}>
                            - Si vous commandez cet article, vous recevrez votre commande le {newDate}
                        </p>
                    </td>
                </tr>
                :
                <div className='pb-2 pt-3'></div>
            } */}
      <div className="pb-2 pt-3"></div>
      <tr className="product-box-contain">
        <td className="product-detail pt-0">
          <div className="product border-0">
            <Link href={`/products/${item?.slug}`} className="product-image">
              <img
                src={item?.image_link}
                className="img-fluid blur-up lazyload"
                alt={item?.name}
              />
            </Link>
            <div className="product-detail">
              <ul>
                <li className="name">
                  <Link href={`/products/${item?.slug}`}>{item?.name}</Link>
                </li>
                <li className="text-content d-inline-block">
                  <span className="text-title">Quantité</span> x{quantity}
                </li>
              </ul>
            </div>
          </div>
        </td>

        <td className="pt-0">
          <ProductPrice
            product={{
              ...item,
              price_ttc: item.price,
            }}
          />
        </td>

        <td className="quantity pt-0">
          <div className="quantity-price">
            <div className="cart_qty">
              <div className="input-group">
                <button
                  type="button"
                  className="btn qty-left-minus"
                  onClick={() => toggleQuantity("minus")}
                >
                  <Minus />
                </button>
                <input
                  type="number"
                  step={1}
                  min={0}
                  onBlur={() => blurQuantity()}
                  onChange={(event) => addQuantity(event?.target?.value)}
                  className="form-control input-number qty-input"
                  value={quantity}
                />
                <button
                  type="button"
                  className="btn qty-right-plus"
                  onClick={() => toggleQuantity("plus")}
                >
                  <Plus />
                </button>
              </div>
            </div>
          </div>
        </td>

        <td className="subtotal pt-0">
          <ProductPrice product={{ price_ttc: item?.total }} />
        </td>

        <td className="save-remove pt-0">
          <button
            type="button"
            className="remove close_button"
            onClick={RemoveItem}
          >
            <Trash2 size={20} />
          </button>
          {loading && (
            <div className="laoding-cart-item">
              <TailSpin
                type="ThreeDots"
                color="#2A3466"
                height={35}
                width={35}
              />
            </div>
          )}
        </td>
      </tr>
    </>
  );
}
