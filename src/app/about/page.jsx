import OurTeam from "@/app/about/_components/our-team";
import Count from "@/components/count";
import PageBanner from "@/components/page-banner";
import SectionTitle from "@/components/section-title";
import { getUrl } from "@/lib/helpers";
import { Check } from "react-feather";

const values = [
  {
    label: "Innovation et qualité",
    icon: (
      <svg
        width="60"
        height="64"
        viewBox="0 0 85 91"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2024_954)">
          <path
            d="M41.0964 90.9998C37.0978 89.9213 34.6566 86.1868 35.5948 82.0509L12.8371 70.0422C12.3124 69.9685 11.9485 70.6788 11.5397 70.9805C8.81252 72.9832 5.28661 73.14 2.61618 70.9117C-2.10079 66.9729 -0.0707953 59.6774 5.72616 58.3518V35.451C-2.99172 33.4341 -1.04444 20.4466 7.90505 21.3161C9.66564 21.4872 11.1119 22.4754 12.4471 23.5349L35.7744 9.54023C32.896 -3.17165 52.1113 -3.1859 49.2447 9.54023L72.572 23.5349C82.2659 15.3201 91.1964 31.5526 79.293 35.4486V58.3494C85.1514 59.6632 87.1199 67.315 82.1926 71.0518C79.5245 73.0758 76.4193 72.9499 73.6921 71.1231C73.1202 70.7406 72.7161 69.8925 72.0521 70.0897L49.6961 81.7729C49.136 82.0794 49.5708 82.6376 49.5874 83.089C49.7221 87.1442 47.7583 89.8191 43.9275 90.9998H41.0964ZM41.94 2.84577C36.6417 3.55845 37.4381 11.6426 42.7719 11.3386C48.4436 11.0155 47.6968 2.07369 41.94 2.84577ZM71.1564 25.9343L47.8291 11.9396C46.0969 13.4101 44.4119 14.3199 42.0629 14.1893C40.0991 14.08 38.6693 13.125 37.1971 11.9396L13.8698 25.9343C15.2003 30.1985 12.8489 34.4413 8.53839 35.451L8.56674 58.4421C12.6102 59.169 14.9521 63.2812 14.0494 67.2318L36.8071 79.2405C37.3365 79.3166 37.691 78.6015 38.1069 78.3022C40.3921 76.663 43.0247 76.2045 45.6408 77.4541C46.6168 77.9197 47.2998 78.7939 48.1836 79.3736L70.8185 67.4409C71.206 67.0941 70.8303 66.6189 70.8185 66.1937C70.6909 62.3618 72.6429 59.2641 76.4595 58.4421L76.4878 35.451C72.1821 34.4556 69.8401 30.1866 71.1564 25.9343ZM6.56038 24.1835C1.26206 24.8962 2.05846 32.9803 7.39223 32.6763C13.0639 32.3532 12.3172 23.4114 6.56038 24.1835ZM77.3173 24.1835C72.019 24.8962 72.8154 32.9803 78.1492 32.6763C83.8209 32.3532 83.0741 23.4114 77.3173 24.1835ZM6.56038 61.1717C1.26206 61.8843 2.05846 69.9685 7.39223 69.6645C13.0639 69.3414 12.3172 60.3996 6.56038 61.1717ZM77.3173 61.1717C72.019 61.8843 72.8154 69.9685 78.1492 69.6645C83.8209 69.3414 83.0741 60.3996 77.3173 61.1717ZM41.94 79.6658C36.6417 80.3784 37.4381 88.4626 42.7719 88.1586C48.4436 87.8355 47.6968 78.8937 41.94 79.6658Z"
            fill="#FFC403"
          />
          <path
            d="M42.111 18.4823C42.6167 18.3683 42.8436 18.4609 43.2831 18.6534C45.4667 19.6131 49.9568 23.8631 51.6276 25.7351C63.3728 38.9007 66.1448 62.7613 47.4613 70.988C44.3182 72.373 42.9617 72.9122 39.5918 71.7957C32.0862 69.3108 26.4925 63.3718 24.7674 55.5774C21.9079 42.6636 27.9884 29.5954 37.8312 21.4732C38.5 20.9221 41.4942 18.6177 42.111 18.48V18.4823ZM41.0971 32.6742V22.7157C40.1495 23.1005 35.0733 27.8185 35.1159 28.3197L41.0971 32.6742ZM43.9283 32.6742L49.9001 28.3197C49.9379 27.79 44.8854 23.1171 43.9283 22.7157V32.6742ZM41.0971 45.8327V36.3184L33.2466 30.731C31.2662 33.1731 29.8577 36.0452 28.7706 38.9909L41.0971 45.8327ZM43.9283 45.8327L56.2548 38.9909C55.1677 36.0476 53.7592 33.1731 51.7789 30.731L43.9283 36.3184V45.8327ZM41.0971 58.813V49.2987L27.8324 41.9201C27.1447 45.1604 26.6414 48.5385 27.1518 51.8501L41.0971 58.8154V58.813ZM43.9283 49.3011V58.8154L57.8736 51.8501C58.3911 48.5409 57.8594 45.1651 57.193 41.9201L43.9283 49.2987V49.3011ZM41.0971 69.1279V62.1032L27.6505 55.347C29.3662 61.906 34.4612 67.5552 41.0971 69.1279ZM43.9283 69.3061C50.5854 67.3842 55.6805 62.1032 57.375 55.3494L43.9283 62.1056V69.3084V69.3061Z"
            fill="#FFC403"
          />
          <path
            d="M18.4541 38.3633V56.8574C19.3474 56.8883 20.7062 56.5842 21.1505 57.6128C21.5948 58.6415 21.0583 59.5798 19.9618 59.7057C18.7897 59.8412 15.8002 60.093 15.6348 58.5346V36.6837C15.7978 35.1277 18.792 35.3748 19.9618 35.5126C21.7602 35.724 21.6893 38.1067 20.1249 38.3514C19.5979 38.4346 18.9929 38.3205 18.4541 38.3609V38.3633Z"
            fill="#FFC403"
          />
          <path
            d="M66.5695 56.8574V38.3633C65.6762 38.3324 64.3173 38.6365 63.873 37.6079C63.4287 36.5792 63.9652 35.6409 65.0617 35.515C66.2339 35.3796 69.2233 35.1277 69.3888 36.6861V58.537C69.2257 60.093 66.2315 59.8459 65.0617 59.7081C63.2633 59.4967 63.3342 57.114 64.8987 56.8693C65.4257 56.7861 66.0306 56.9002 66.5695 56.8598V56.8574Z"
            fill="#FFC403"
          />
        </g>
        <defs>
          <clipPath id="clip0_2024_954">
            <rect width="85" height="91" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
  },
  {
    label: "Responsabilité environnementale",
    icon: (
      <svg
        width="60"
        height="60"
        viewBox="0 0 82 82"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2024_965)">
          <path
            d="M39.8783 57.6863H46.2916C47.7426 57.6863 50.1544 58.3872 51.2475 59.3876C52.3406 60.388 52.6117 61.8203 52.7895 63.2113L73.1637 55.169C77.0937 53.6543 82.6958 56.0219 81.9194 60.8438C81.7654 61.8029 78.6553 64.2963 77.7595 64.9647C75.1049 66.946 71.9405 68.7471 69.0516 70.3833C53.6766 79.0874 42.9147 84.2652 24.5923 81.0253C22.8724 80.7215 13.8131 78.4386 12.926 79.0397C12.4358 80.4047 11.6572 81.2532 10.1802 81.4919C8.62734 81.7436 4.88823 81.711 3.26593 81.57C1.64363 81.4289 0.470279 80.5522 0.121094 78.8704L0.144951 56.1825C0.309784 55.1951 1.52434 53.9907 2.49816 53.8279C3.97515 53.5806 8.34972 53.6413 9.93299 53.7824C11.6334 53.9343 12.8631 55.1669 12.9303 56.8769C15.2554 56.3279 17.6021 55.6443 19.9943 55.4164C24.7701 54.9607 28.3032 56.2888 32.8686 56.9876C34.3868 57.2197 35.9289 57.2827 37.4449 57.5236C37.1629 55.8049 37.3668 54.1947 37.6097 52.489C31.3266 51.7425 27.4465 45.6316 28.9582 39.5294C29.084 39.0195 29.5351 37.7022 29.8387 37.3247C30.8776 36.0313 32.2743 37.878 33.1744 38.49C34.3673 39.3016 35.6946 39.9374 36.7986 40.8727C38.5228 42.3332 39.6853 44.3079 40.2861 46.478C41.325 45.2302 42.4571 44.1039 43.7519 43.1231C43.6261 40.7208 43.9276 38.7265 45.3352 36.7474C46.528 35.07 47.8966 34.5448 49.5146 33.4576C50.4428 32.8326 51.4969 31.2333 52.5184 32.7806C54.6482 36.0096 52.9999 41.1982 49.9331 43.2967C47.632 44.8721 45.7581 44.2254 43.6153 46.2327C40.5529 49.1037 39.1019 53.5567 39.8827 57.6863H39.8783ZM46.0466 42.4178C48.4909 42.0814 50.7508 39.9309 51.0609 37.4462C51.1043 37.0946 51.1932 35.2783 50.8354 35.2739C48.0376 36.8494 45.693 38.8524 46.0466 42.4178ZM31.3547 39.9851C29.6153 44.5054 33.3002 50.0325 38.102 50.1454C38.6963 47.4697 37.3299 44.3122 35.2457 42.5849C34.0441 41.5888 32.4847 41.0897 31.3547 39.9851ZM3.14014 56.0978C2.7389 56.1868 2.34417 56.5058 2.36586 56.9485L2.4461 78.6491C2.57624 79.044 2.7107 79.0918 3.09459 79.1373C5.26778 79.3956 7.98752 78.9486 10.2193 79.1005L10.5316 78.7641L10.5793 56.7011C10.408 56.2302 10.0609 56.113 9.59898 56.0588C8.56444 55.9372 4.04021 55.8982 3.13797 56.0957L3.14014 56.0978ZM12.9455 76.5116C18.1031 76.9065 23.0155 78.5861 28.1297 79.2675C42.893 81.2314 52.0109 76.9846 64.4992 70.2227C68.0821 68.2827 71.8884 66.2277 75.222 63.8949C76.1849 63.2221 78.9871 61.2561 79.5011 60.3707C79.7874 59.8759 79.3254 58.7301 78.9784 58.2787C77.586 56.4646 74.6255 57.1286 72.7929 57.7275C65.6703 60.0604 58.5955 63.8384 51.447 66.245C47.3739 70.1815 40.8804 70.1099 35.5797 70.0231C34.415 70.0036 29.4397 70.5417 30.4178 68.2849C30.9188 67.1282 32.4262 67.7445 33.2937 67.7619C37.2562 67.8444 47.2373 68.0549 49.777 64.5871C50.638 63.4131 50.5252 61.5512 49.2131 60.7678C47.7903 59.9171 41.7934 59.813 39.9499 59.954C39.5226 59.9866 39.2494 60.2926 38.859 60.2557C38.4924 60.2209 38.1823 59.8694 37.8353 59.8043C36.2412 59.5027 34.222 59.5395 32.5129 59.2813C28.3943 58.6585 24.9415 57.3934 20.6363 57.6863C19.7536 57.7471 13.0214 58.9688 12.9152 59.4158L12.9412 76.5094L12.9455 76.5116Z"
            fill="#FFC403"
          />
          <path
            d="M49.9747 7.46327L58.3053 10.8963C60.8797 9.88288 63.2676 6.15906 66.1826 8.38554C67.6639 9.51614 72.6024 14.4574 73.7324 15.9395C76.0227 18.9429 72.0472 21.2844 71.2403 23.9449L74.6541 32.1564C77.0637 32.8182 81.5706 32.3625 81.9285 35.6805C82.0217 36.5355 82.0564 40.9972 81.8569 41.62C81.7983 41.8023 81.6682 41.9781 81.5055 42.08L60.2117 42.1386L59.7194 41.4941C59.7411 28.2003 46.7128 18.8908 34.153 23.7799C26.8938 26.6054 22.3089 33.7297 22.311 41.4941L21.8187 42.1386L0.743943 42.1864C0.273302 42.0149 0.156184 41.6677 0.101963 41.2055C-0.0455191 39.9599 -0.0151552 37.1149 0.0976252 35.8346C0.412109 32.2952 4.84958 32.8573 7.37847 32.1542L10.7922 23.9427C9.98543 21.2822 6.00992 18.9407 8.30023 15.9373C9.24585 14.6982 14.835 9.05175 16.0257 8.23581C18.8799 6.27841 21.0769 9.84816 23.3716 10.7835C23.582 10.8703 23.7403 10.9571 23.968 10.8355L32.0557 7.46327C32.7302 4.94817 32.214 0.493037 35.7341 0.178379C38.3909 -0.0581575 43.6439 -0.0581575 46.3007 0.178379C49.8186 0.493037 49.3024 4.94166 49.9791 7.46327H49.9747ZM79.6533 39.9816V35.839C79.6533 35.1055 74.3245 34.5716 73.4851 34.2136C73.1424 34.0682 72.893 33.8533 72.6978 33.5409C71.9062 30.5983 69.5096 27.0763 68.8589 24.227C68.7505 23.7561 68.705 23.4045 68.8676 22.9314C69.2342 21.8681 71.1818 19.8261 71.8303 18.5892C71.9713 18.3179 72.2228 18.0575 72.1231 17.7233L64.7056 10.178C64.3326 9.83514 63.9226 10.0847 63.5301 10.2843C62.322 10.9028 60.3093 12.8667 59.1924 13.253C58.789 13.3919 58.4311 13.3832 58.019 13.3029L48.3112 9.21016C47.4177 8.44847 47.3287 4.3731 46.8169 3.07107C46.7258 2.84104 46.5718 2.67829 46.3701 2.54374H35.6625C35.4608 2.67829 35.3046 2.84104 35.2157 3.07107C34.6778 4.44254 34.6583 8.19458 33.7994 9.1277C33.6585 9.2796 33.5131 9.37292 33.3375 9.47708C31.7846 10.3863 28.3838 11.5712 26.562 12.283C25.6511 12.6389 23.9832 13.5025 23.0853 13.3376C21.9835 12.9275 18.624 9.95666 17.7174 9.99789C17.4962 10.0066 17.3075 10.1954 17.1491 10.3364L9.91169 17.7255C9.81409 18.0597 10.0635 18.3222 10.2045 18.5913C10.853 19.8283 12.8006 21.8703 13.1671 22.9336C13.2517 23.1767 13.2973 23.4023 13.2712 23.6584L9.33477 33.543C8.8034 34.8906 2.37709 34.8472 2.37709 35.8411V39.9838H20.0728C21.0791 23.513 39.7291 14.3575 53.3365 24.164C58.3986 27.8119 61.6085 33.7188 61.9577 39.9838H79.6533V39.9816Z"
            fill="#FFC403"
          />
          <path
            d="M64.7961 26.2473C65.7569 26.2473 66.1842 27.3649 65.6181 28.1352C65.4381 28.3783 63.1131 29.7932 62.8073 29.8735C61.6361 30.1838 60.71 28.9837 61.4648 28.0376C61.7359 27.6991 64.3775 26.2451 64.7961 26.2451V26.2473Z"
            fill="#FFC403"
          />
          <path
            d="M26.8408 16.2996C27.2745 16.1694 27.6845 16.2171 28.038 16.4949C28.2657 16.675 29.6169 18.8863 29.721 19.1966C30.1526 20.4899 28.8903 21.3515 27.9317 20.6614C27.7625 20.5399 26.4786 18.3546 26.3398 18.0313C26.073 17.4085 26.0925 16.5231 26.8408 16.2996Z"
            fill="#FFC403"
          />
          <path
            d="M54.2775 16.299C55.2252 16.0126 56.0906 16.7309 55.8455 17.69C55.7783 17.9504 54.2558 20.5436 54.0931 20.6608C53.1822 21.314 51.9264 20.535 52.2517 19.3089C52.3602 18.9031 53.9088 16.4075 54.2775 16.2968V16.299Z"
            fill="#FFC403"
          />
          <path
            d="M17.232 26.2471C17.6246 26.2471 20.353 27.7292 20.5721 28.0309C21.2336 28.9531 20.4333 30.1944 19.2209 29.8732C18.9151 29.7929 16.5879 28.3781 16.41 28.135C15.844 27.3646 16.2712 26.2471 17.232 26.2471Z"
            fill="#FFC403"
          />
          <path
            d="M40.8088 12.5542C41.2317 12.4783 41.8932 12.7973 42.0667 13.1814C42.2185 13.5156 42.2185 16.3497 42.1166 16.7989C41.8715 17.8665 40.0431 17.9533 39.8761 16.5124C39.8154 15.9916 39.7916 13.7044 39.913 13.2942C39.9933 13.0208 40.5355 12.6041 40.8109 12.5542H40.8088Z"
            fill="#FFC403"
          />
        </g>
        <defs>
          <clipPath id="clip0_2024_965">
            <rect width="82" height="82" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
  },
  {
    label: "Excellence des services",
    icon: (
      <svg
        width="60"
        height="60"
        viewBox="0 0 87 87"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2024_961)">
          <path
            d="M50.7068 0.62129C50.9007 0.863771 50.9769 1.42263 51.0508 1.74825C51.2908 2.80131 52.1542 9.36678 52.482 9.65083C55.5293 10.533 58.4312 11.8124 61.213 13.3158L68.4664 8.16592L69.7685 8.60932L78.8295 17.6966C79.1689 18.1654 79.2289 18.8351 78.9357 19.3455L73.6053 25.8994C75.3182 28.629 76.5348 31.6058 77.359 34.728L86.2769 36.0051C86.7386 36.3861 86.8263 36.7348 86.8794 37.3029C87.2534 41.3558 86.5863 45.9838 86.8679 50.099L86.0484 51.0089L77.2759 52.6047C76.5902 55.6853 75.3806 58.6228 73.7946 61.3363L78.6564 68.172C78.8849 68.7747 78.7857 69.4514 78.3216 69.8971L69.2375 78.9612C68.7689 79.3007 68.0994 79.3608 67.5892 79.0675L61.0375 73.7352C58.3042 75.4303 55.3331 76.6704 52.2119 77.4902L51.0784 85.8662L50.2289 86.91L36.6731 87.0047C36.2322 86.9216 35.8674 86.4435 35.7312 86.0463C35.1425 84.3259 35.161 80.8203 34.8309 78.8227C34.7755 78.4855 34.6993 77.5733 34.5146 77.4117C31.4581 76.5341 28.5839 75.2201 25.7814 73.749L19.0589 78.8989C18.4633 79.2245 17.7014 79.1183 17.2259 78.631L8.16482 69.5437C7.82546 69.0749 7.76775 68.4075 8.05863 67.8949L13.2113 61.3409C11.4822 58.6251 10.2956 55.6253 9.46223 52.5146L0.68512 51.2676C0.179548 51.032 0.156462 50.6002 0.1126 50.1129C-0.263694 46.0069 0.401169 41.3189 0.131068 37.1505C0.158771 36.975 0.387317 36.5155 0.514287 36.4138C1.15606 35.8965 7.25294 35.2407 8.62422 34.9312C8.89663 34.8689 9.36527 34.8389 9.53841 34.6356C10.4387 31.5989 11.6831 28.6822 13.1928 25.9063L8.22946 19.006C7.90396 18.4102 8.01015 17.6481 8.49725 17.1724L17.5814 8.10819C18.05 7.76872 18.7195 7.70867 19.2297 8.00196L25.7814 13.3342C28.4939 11.5999 31.495 10.4175 34.6069 9.57924C35.1541 7.07129 35.0848 4.2308 35.5927 1.75056C35.8074 0.704426 35.8697 0.332621 37.0078 0.228701C40.0551 -0.0507301 46.6137 -0.0738236 49.6425 0.224082C49.9842 0.258722 50.4875 0.360334 50.7045 0.630527L50.7068 0.62129ZM48.6845 2.99068H38.1413L36.8416 11.6299C36.5138 12.0064 34.8009 12.2165 34.1683 12.4128C32.8455 12.8216 31.5089 13.2742 30.2484 13.8515C29.3042 14.2857 26.6009 16.1678 26.0907 16.2717C25.5367 16.3872 25.2319 16.177 24.7795 15.923C22.741 14.7868 20.6033 12.5144 18.6087 11.1935H18.2693L11.0228 18.4864C10.9466 18.9275 16.0208 24.9341 16.0347 25.8925C16.0439 26.4906 15.4021 27.0679 15.1274 27.5945C14.2409 29.2918 13.3914 31.0054 12.7357 32.8066C12.3595 33.8366 11.8285 36.7094 11.0736 37.1944C10.8381 37.3445 10.4664 37.4531 10.1894 37.52C7.96859 38.0489 5.18218 38.0627 2.88748 38.4276V48.9744L11.5238 50.2745C11.9001 50.6025 12.1101 52.316 12.3064 52.9488C12.715 54.272 13.1674 55.6091 13.7446 56.87C14.1786 57.8146 16.0601 60.5188 16.1639 61.0292C16.2794 61.5834 16.0693 61.8882 15.8154 62.3409C14.6795 64.38 12.4079 66.5185 11.0874 68.5138V68.8532L18.3778 76.1023C18.8372 76.1808 24.6017 70.9293 25.6082 70.9155C26.0007 70.9085 29.5882 73.0147 30.4192 73.3795C32.3792 74.2363 34.4454 74.7836 36.4746 75.4464L37.2318 76.7627L38.139 84.2404H48.6822L49.9819 75.6011C50.3097 75.2247 52.0226 75.0146 52.6552 74.8183C53.978 74.4095 55.3146 73.9569 56.5751 73.3795C57.5193 72.9454 60.2226 71.0633 60.7328 70.9594C61.2869 70.8439 61.5916 71.054 62.0441 71.3081C64.0825 72.4443 66.2202 74.7167 68.2148 76.0376H68.5542L75.8007 68.7447C75.8746 68.3152 70.9758 62.454 70.9597 61.5118C70.9551 61.1262 72.9473 57.7314 73.3006 56.9231C73.7299 55.9417 74.0831 54.9186 74.4341 53.9071C74.995 52.2883 74.7388 50.4616 76.6364 50.0598C78.9773 49.5633 81.7198 49.5517 84.1092 49.1499V38.6031L75.4729 37.3029C75.0966 36.975 74.8865 35.2615 74.6903 34.6287C74.2817 33.3055 73.8292 31.9684 73.2521 30.7075C72.8181 29.7629 70.9366 27.0587 70.8327 26.5483C70.7173 25.9941 70.9274 25.6892 71.1813 25.2366C72.3217 23.1905 74.6003 21.0798 75.9092 19.0637V18.7243L68.6188 11.4752C68.1848 11.4013 62.388 16.475 61.3884 16.4888C60.7859 16.498 60.2157 15.8537 59.687 15.5812C57.7917 14.6044 55.9726 13.7384 53.9618 13.0109C52.2604 12.3943 50.2843 12.6114 49.7695 10.6346C49.1647 8.30679 49.1254 5.3924 48.6845 2.98837V2.99068Z"
            fill="#FFC403"
          />
          <path
            d="M41.5622 15.3175C64.0544 13.7402 79.3624 38.2562 68.002 57.8441C57.7936 75.446 32.8359 76.8085 20.716 60.4421C7.38414 42.4385 19.4394 16.8717 41.5622 15.3175ZM40.872 18.0818C29.9202 19.0078 20.3928 27.8711 18.3498 38.5934C13.8943 61.9732 40.7588 78.5058 59.3796 63.733C79.3509 47.8885 66.5615 15.9064 40.872 18.0795V18.0818Z"
            fill="#FFC403"
          />
          <path
            d="M30.3184 27.4031L56.8621 27.3984L64.1595 38.2524C64.4919 39.3008 63.3099 40.1299 62.7143 40.9658C56.996 48.9978 51.2408 57.734 45.197 65.4495C44.5737 66.2463 44.149 67.2785 42.9301 66.9137L22.8411 39.1276C22.4694 37.9729 23.1896 37.7628 23.6675 36.9984C25.6113 33.8946 27.7029 30.8902 29.6998 27.821L30.3161 27.4007L30.3184 27.4031ZM40.9078 30.1327H33.3034L36.6739 36.1855L40.9055 30.1327H40.9078ZM53.6948 30.1327H46.0905L50.322 36.1855L53.6925 30.1327H53.6948ZM39.181 37.2201H47.6487L43.5857 31.1696L39.181 37.2201ZM34.1691 37.2201L30.884 31.3428L26.911 37.2201H34.1691ZM60.0895 37.2201L56.1165 31.3428L52.8314 37.2201H60.0895ZM35.7251 40.1576H26.911L40.2152 58.4799L35.7251 40.1576ZM48.6853 40.1576H38.3176L43.5857 59.8655L48.6853 40.1576ZM60.0895 40.1576H51.2755L46.6953 58.8286L60.0872 40.1576H60.0895Z"
            fill="#FFC403"
          />
        </g>
        <defs>
          <clipPath id="clip0_2024_961">
            <rect width="87" height="87" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
  },
];

const atots = [
  {
    label: "Projet",
    value: 320,
  },
  {
    label: "Ville",
    value: 40,
  },
  {
    label: "Mégawatt",
    value: 62,
  },
];

export default function About() {
  return (
    <div className="p-0 -mt-[1.5rem] mb-20">
      <PageBanner
        title="À propos de nous"
        background="https://videos.pexels.com/video-files/12299061/12299061-uhd_2560_1440_30fps.mp4"
      />

      <div className="space-y-20  mt-20">
        {/* About */}
        <div className="container-lg">
          <section
            data-aos="fade-up"
            className="bg-[var(--theme-color-light)] rounded-lg overflow-hidden relative flex items-center p-0"
          >
            <div className="flex-1 flex flex-col p-6 lg:p-10 xl:p-12">
              <SectionTitle title="À propos de nous" />

              <div>
                <p className="leading-[2] text-justify">
                  Ecowatt est une entreprise pionnière dans le domaine de
                  l'installation et de la maintenance des systèmes solaires.
                  Depuis notre création, nous plaçons l'innovation, la
                  durabilité et l'énergie renouvelable au cœur de notre mission.
                  Nous nous offrons des solutions solaires intégrées et agissons
                  comme catalyseurs de changement. Nos experts passionnés
                  innovent constamment pour dépasser les attentes de nos
                  clients. Au fil des ans, ECOWATT Maroc a marqué le paysage
                  énergétique avec des projets innovants, et nous créons un
                  monde plus propre avec des solutions solaires fiables et
                  efficaces. Ensemble, nous avançons vers un avenir plus durable
                  et vert.
                </p>
              </div>
              <div className="pt-4 lg:!mt-auto  flex justify-end">
                <img
                  src="/assets/images/Yassine-ALJ-SignatureGenerator.svg"
                  alt="Signature Yassine ALJ"
                  className="w-[170px]"
                />
              </div>
            </div>
            <div className="w-1/3 hidden lg:block">
              <img
                src="/assets/images/about/Yassine ALJ.webp"
                alt="Yassine ALJ"
                className="h-full object-cover"
              />
            </div>
          </section>
        </div>

        {/* Agencies */}
        <div className="container-lg">
          <section
            className="bg-[var(--theme-color-light)] rounded-lg overflow-hidden lg:flex lg:items-center p-0"
            data-aos="fade-up"
          >
            <div className="flex-1">
              <img
                src="/assets/images/our-agencies.webp"
                alt="Agences ecowatt"
                className="h-full w-full object-cover"
              />
            </div>
            <div className="p-6 lg:p-10 xl:p-12 flex-1">
              <SectionTitle title="Nos agences" />
              <p className="leading-[2] text-justify">
                Au cœur de nos opérations, ECOWATT MAROC s'engage pour un avenir
                énergétique durable et lumineux. Fondée en 2018, ECOWATT MAROC
                est spécialisée dans l'électricité industrielle et les solutions
                d'énergie solaire. Présente dans des villes clés du Maroc telles
                qu'Agadir, Marrakech, Beni Mellal, Rabat, Meknès, Fès, Oujda et
                Er-Rachidia. Nous nous engageons à fournir des solutions
                énergétiques innovantes pour un avenir plus propre.
              </p>
            </div>
          </section>
        </div>

        {/* Mission */}
        <div className="container-lg">
          <section
            className="bg-[var(--theme-color-light)] rounded-lg overflow-hidden lg:flex lg:items-center p-0"
            data-aos="fade-up"
          >
            <div className="p-6 lg:p-10 xl:p-12 flex-1">
              <SectionTitle title="Nos missions" />
              <p className="leading-[2] text-justify">
                ECOWATT Maroc propose des solutions énergétiques durables dans
                les domaines de l'énergie solaire, l'électricité industrielle et
                l'hydraulique. Nous aidons les installateurs à chaque étape de
                leurs projets d'énergies renouvelables, offrant l'assistance
                technique, l'installation, le service après-vente et la
                livraison. Nos solutions sont conçues pour réduire l'empreinte
                carbone et réaliser des économies sur la facture énergétique.
              </p>
            </div>
            <div className="flex-1 relative before:absolute before:inset-0 before:bg-[#4D5F7D]/75">
              <img
                src="/assets/images/about/mission.webp"
                alt="Agences ecowatt"
              />
            </div>
          </section>
        </div>

        {/* Values */}
        <div className="container-lg">
          <section
            className="bg-[var(--theme-color-light)] rounded-lg overflow-hidden lg:flex lg:items-center p-0"
            data-aos="fade-up"
          >
            <div className="flex-1 z-10 relative bg-center bg-cover bg-[url('/assets/images/about/panel-with-sun.webp')]">
              <div className="bg-[#1D3D71]/75 h-full p-6 lg:p-10 xl:p-12">
                <ul className="flex lg:!flex-col gap-5 items-center justify-center">
                  {values.map((value, key) => (
                    <li key={key} className="flex flex-col items-center gap-3">
                      {value.icon}
                      <span className="font-semibold text-center text-xs lg:text-sm text-white">
                        {value.label}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="flex-1 p-6 lg:p-10 xl:p-12">
              <SectionTitle title="Nos valeurs" />

              <p className="leading-[2] text-justify">
                Chez ECOWATT Maroc, nous croyons fermement en l'innovation, la
                durabilité et la responsabilité environnementale. Nous nous
                engageons à réduire la dépendance aux énergies non renouvelables
                et à lutter contre le changement climatique grâce à nos
                solutions énergétiques durables. Nos services incluent l'énergie
                solaire, l'électricité industrielle et l'hydraulique, offrant
                des solutions clés en main pour réduire l'empreinte carbone et
                réaliser des économies sur la facture énergétique.
              </p>
            </div>
          </section>
        </div>

        {/* Atots */}
        <div className="container-lg">
          <section
            className="bg-[var(--theme-color-light)] rounded-lg overflow-hidden lg:flex lg:items-center p-0"
            data-aos="fade-up"
          >
            <div className="flex-1 p-6 lg:p-10 xl:p-12">
              <SectionTitle title="Nos atouts" />

              <div>
                <ul className="list-disc space-y-3">
                  {[
                    "ECOWATT MAROC, c'est un ensemble de compétences réunies pour favoriser l'innovation dans l'énergie solaire et les énergies renouvelables au Maroc.",
                    "Une équipe d'experts passionnés spécialisés dans les énergies renouvelables et l'innovation technologique.",
                    "Accompagnement technologique pour des projets innovants.",
                    "Un service client réactif et personnalisé pour répondre aux besoins.",
                    "Des agences stratégiquement situées dans les principales villes du Maroc pour un service de proximité.",
                  ].map((item, key) => (
                    <li key={key} className="flex items-start gap-2">
                      <Check size={16} className="flex-shrink-0" />
                      <span className="block">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="flex-1 relative bg-center bg-cover bg-[url('/assets/images/about/solar-pannels.webp')]">
              <div className="flex items-center justify-center bg-[#1D3D71]/75 h-full p-6 lg:p-10 xl:p-12">
                <ul className="flex lg:flex-col gap-10">
                  {atots.map((atot, key) => (
                    <li
                      key={key}
                      className="relative flex flex-col items-center sm:min-w-[100px] gap-1 p-3 !aspect-square"
                    >
                      <div className="absolute left-0 top-0 w-4 h-4 border-t-4 border-l-4 border-[#FFC403]" />
                      <div className="absolute right-0 bottom-0 w-4 h-4 border-b-4 border-r-4 border-[#FFC403]" />
                      <Count
                        direction="up"
                        prefix="+"
                        value={atot.value}
                        className="text-[#FFC403] font-bold text-xl sm:text-2xl"
                      />
                      <span className="font-medium text-center text-xs lg:text-sm text-white">
                        {atot.label}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </section>
        </div>

        {/* Team */}
        <OurTeam />
      </div>
    </div>
  );
}

/** @type {import("next").Metadata} */
export const metadata = {
  title: "À propos de nous",
  description:
    "Au cœur de nos opérations, nous nous engageons pour un avenir plus lumineux et durable. ECOWATT MAROC, fondée en 2018, est spécialisée dans l'électricité industrielle et les solutions d'énergie solaire. Présente dans des villes clés du Maroc, nous nous engageons à réduire la dépendance aux énergies non renouvelables et à lutter contre le changement climatique.",
  openGraph: {
    url: getUrl("/about"),
    type: "website",
    title: "À propos de nous",
    description:
      "Au cœur de nos opérations, nous nous engageons pour un avenir plus lumineux et durable. ECOWATT MAROC, fondée en 2018, est spécialisée dans l'électricité industrielle et les solutions d'énergie solaire. Présente dans des villes clés du Maroc, nous nous engageons à réduire la dépendance aux énergies non renouvelables et à lutter contre le changement climatique.",
    images: ["/logo192.png"],
  },
  alternates: {
    canonical: "/about",
  },
};
