"use client";

import SectionTitle from "@/components/section-title";
import useMounted from "@/hooks/useMounted";
import { Autoplay, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

const teamMembers = [
  {
    name: "<PERSON><PERSON><PERSON>J",
    job: "Directeur développement",
    image: "/assets/images/about/collaborators/alj yassine.webp",
    linkedin: "https://ma.linkedin.com/in/yassine-alj-55810a79",
    mail: "<EMAIL>",
  },
  {
    name: "<PERSON><PERSON>h ASSIF",
    job: "Service Support",
    image: "/assets/images/about/collaborators/abdellah assif.webp",
    linkedin: "https://www.linkedin.com/in/assif-abdallah-a428951b1",
    mail: "<EMAIL>",
  },
  {
    name: "Oumaima EDOULATI",
    job: "Service Support",
    image: "/assets/images/about/collaborators/oumaima edoulati.webp",
    linkedin: "https://www.linkedin.com/in/omaima-edoulati-b856a8192",
    mail: "<EMAIL>",
  },
  {
    name: "<PERSON><PERSON><PERSON>Y<PERSON>",
    job: "Service Commercial",
    image: "/assets/images/about/collaborators/mohmed haya.webp",
    linkedin: "https://www.linkedin.com/in/mohamed-haya-624ba31a2",
    mail: "<EMAIL>",
  },
  {
    name: "Jassim CHAFAI",
    job: "Service Commercial",
    image: "/assets/images/about/collaborators/jassim chafai.webp",
    linkedin: "https://www.linkedin.com/in/jassim-chafai",
    mail: "<EMAIL>",
  },
  // {
  //   name: "El Mahdi RAIS",
  //   job: "Service Support",
  //   image: "/assets/images/about/collaborators/el mehdi rais.webp",
  //   linkedin: "https://www.linkedin.com/in/el-mahdi-rais-b70b701b0",
  //   mail: "<EMAIL>",
  // },
  {
    name: "Ahmed BOUHIA",
    job: "Service Commercial",
    image: "/assets/images/about/collaborators/ahmed bouhia.webp",
    linkedin: "https://www.linkedin.com/in/ahmed-bouhia-0513b91a4",
    mail: "<EMAIL>",
  },
  {
    name: "Hicham AMGOUR",
    job: "Service Commercial",
    image: "/assets/images/about/collaborators/hicham amgour.webp",
    linkedin: "https://ma.linkedin.com/in/hicham-amgour-b774b7198",
    mail: "<EMAIL>",
  },
  {
    name: "Saad BARKOUKI",
    job: "Service Commercial",
    image: "/assets/images/about/collaborators/saad berkouki.webp",
    linkedin: "https://ma.linkedin.com/in/saad-barkouki-814731105",
    mail: "<EMAIL>",
  },
  {
    name: "Abdellah AZGOUNE",
    job: "Service Support",
    image: "/assets/images/about/collaborators/abdellah azgoune.webp",
    linkedin: "https://ma.linkedin.com/in/abdellah-azgoune-37653b276",
    mail: "<EMAIL>",
  },
  {
    name: "Mohamed AAMIR",
    job: "Service Commercial",
    image: "/assets/images/about/collaborators/mohamed aamir.webp",
    linkedin: "https://ma.linkedin.com/in/aamir-mohamed-02530a152",
    mail: "<EMAIL>",
  },
  {
    name: "Saida SIDKI",
    job: "Service Commercial",
    image: "/assets/images/about/collaborators/saaida sidki.webp",
    linkedin: "https://ma.linkedin.com/in/saida-sidki-7252b216a",
    mail: "<EMAIL>",
  },
  {
    name: "Rida ATIFI",
    job: "Service Commercial",
    image: "/assets/images/about/collaborators/rida atifi.webp",
    linkedin: "https://ma.linkedin.com/in/rida-atifi-862976174",
    mail: "<EMAIL>",
  },
  {
    name: "Abdelilah LAHLOU",
    job: "Service Commercial",
    image: "/assets/images/about/collaborators/abdelilah lahlou.webp",
    linkedin: "https://ma.linkedin.com/in/abdelilahlahlou",
    mail: "<EMAIL>",
  },
  {
    name: "Salma CHAFIA",
    job: "Service Support",
    image: "/assets/images/about/collaborators/salma chafia.webp",
    linkedin: "https://ma.linkedin.com/in/salma-chafai-838b05184",
    mail: "<EMAIL>",
  },
  {
    name: "Yacine  AHMAICH",
    job: "Service Support",
    image: "/assets/images/about/collaborators/yacine ahmaich.webp",
    linkedin: "https://www.linkedin.com/in/yacine-ahmaich-690804360",
    mail: "<EMAIL>",
  },
  {
    name: "Abderrahim  KARMACH",
    job: "Service Support",
    image: "/assets/images/about/collaborators/abderrahim karmach.webp",
    linkedin: "https://ma.linkedin.com/in/abderrahim-karmach-438206298",
    mail: "<EMAIL>",
  },
  {
    name: "Soukaina ESSAHIRI",
    job: "Service Support",
    image: "/assets/images/about/collaborators/soukaina essahiri.webp",
    linkedin: "https://ma.linkedin.com/in/soukaina-essahiri-8412a123b",
    mail: "<EMAIL>",
  },
  {
    name: "Soukaina KORCHI",
    job: "Service Support",
    image: "/assets/images/about/collaborators/soukaina kourchi.webp",
    linkedin: "https://ma.linkedin.com/in/soukaina-korchi-230451189",
    mail: "<EMAIL>",
  },
  {
    name: "Ayoub HOUSSNI",
    job: "Service Support",
    image: "/assets/images/about/collaborators/ayoub houssni.webp",
    linkedin: "https://ma.linkedin.com/in/ayoub-houssni-6803251a3",
    mail: "<EMAIL>",
  },
];

export default function OurTeam() {
  const mounted = useMounted();

  if (!mounted) return null;

  return (
    <div className="container-lg">
      <section className="p-0">
        <SectionTitle title="Notre équipe passionnée" />
        <div>
          <Swiper
            loop
            navigation
            modules={[Navigation, Autoplay]}
            slidesPerView={2}
            spaceBetween={20}
            breakpoints={{
              1100: { slidesPerView: 5 },
              768: { slidesPerView: 4 },
              678: { slidesPerView: 3 },
              0: { navigation: { enabled: false } },
            }}
            autoplay={{
              delay: 3000,
              pauseOnMouseEnter: true,
              disableOnInteraction: true,
            }}
          >
            {teamMembers.map((member) => (
              <SwiperSlide key={member.name}>
                <div className="group">
                  <div className="relative pt-3">
                    <img src={member.image} alt={member.image} />
                    <div className="absolute space-y-1 bottom-0 group-hover:[&>a]:-translate-x-0 group-hover:[&>a]:!opacity-100 z-20 left-0 w-full p-3">
                      <a
                        target="_blank"
                        href={member.linkedin}
                        className="flex items-center gap-1 -translate-x-2 opacity-0 transition-all delay-100 duration-300 text-white"
                      >
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 48 48"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g clipPath="url(#clip0_17_68)">
                            <path
                              d="M44.4469 0H3.54375C1.58437 0 0 1.54688 0 3.45938V44.5312C0 46.4437 1.58437 48 3.54375 48H44.4469C46.4062 48 48 46.4438 48 44.5406V3.45938C48 1.54688 46.4062 0 44.4469 0ZM14.2406 40.9031H7.11563V17.9906H14.2406V40.9031ZM10.6781 14.8688C8.39062 14.8688 6.54375 13.0219 6.54375 10.7437C6.54375 8.46562 8.39062 6.61875 10.6781 6.61875C12.9563 6.61875 14.8031 8.46562 14.8031 10.7437C14.8031 13.0125 12.9563 14.8688 10.6781 14.8688ZM40.9031 40.9031H33.7875V29.7656C33.7875 27.1125 33.7406 23.6906 30.0844 23.6906C26.3812 23.6906 25.8187 26.5875 25.8187 29.5781V40.9031H18.7125V17.9906H25.5375V21.1219H25.6312C26.5781 19.3219 28.9031 17.4188 32.3625 17.4188C39.5719 17.4188 40.9031 22.1625 40.9031 28.3313V40.9031Z"
                              fill="white"
                            />
                          </g>
                          <defs>
                            <clipPath id="clip0_17_68">
                              <rect width="48" height="48" fill="white" />
                            </clipPath>
                          </defs>
                        </svg>

                        <span className="text-xs">{member.name}</span>
                      </a>
                      <a
                        target="_blank"
                        href={`mailto:${member.mail}`}
                        className="flex items-center gap-1 -translate-x-2 opacity-0 transition-all delay-200 duration-300 text-white"
                      >
                        <svg
                          width="16"
                          height="12"
                          viewBox="0 0 16 12"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M0.0270459 3.73041C8.41798e-10 4.36607 0 5.11359 0 6C0 8.82843 0 10.2426 0.87868 11.1213C1.75736 12 3.17157 12 6 12H9.42857C12.257 12 13.6712 12 14.5499 11.1213C15.4286 10.2426 15.4286 8.82843 15.4286 6C15.4286 5.11359 15.4286 4.36607 15.4015 3.73041L8.68557 7.4615C8.08153 7.79708 7.34705 7.79708 6.743 7.4615L0.0270459 3.73041ZM0.373979 1.64388C0.411788 1.65918 0.449077 1.67696 0.485643 1.69727L7.71429 5.71318L14.9429 1.69727C14.9795 1.67696 15.0168 1.65918 15.0546 1.64388C14.9314 1.34702 14.7676 1.09641 14.5499 0.87868C13.6712 0 12.257 0 9.42857 0H6C3.17157 0 1.75736 0 0.87868 0.87868C0.660951 1.09641 0.497174 1.34702 0.373979 1.64388Z"
                            fill="white"
                          />
                        </svg>

                        <span className="text-xs">{member.mail}</span>
                      </a>
                    </div>
                    <div className="absolute inset-0 origin-bottom duration-500 bg-[#1D3D71]/60 scale-y-0 z-10 group-hover:scale-y-100 transition-transform" />
                  </div>
                  <div className="bg-theme px-4 py-3 text-center">
                    <h6 className="text-white font-semibold">{member.name}</h6>
                    <span className="text-white text-xs italic">
                      {member.job}
                    </span>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </section>
    </div>
  );
}
