import SectionTitle from "@/components/section-title";
import { getUrl } from "@/lib/helpers";
import { GalleryService } from "@/services/gallery/gallery.service";
import HtmlContent from "@/shared/components/html-content";
import GalleryBackButton from "./_components/gallery-back-button";
import GalleryBanner from "./_components/gallery-banner";
import GalleryImages from "./_components/gallery-images";

export default async function GalleryPage({ params }) {
  const { slug } = params;

  const gallery = await GalleryService.getGallery(slug);

  return (
    <div className="-mt-[1.5rem] space-y-10 pb-20">
      <GalleryBanner banner={gallery.full_banner} />

      <section className="container-lg space-y-16 pt-0">
        <div className="flex items-start gap-2">
          <GalleryBackButton />
          <h1 className="text-xl md:text-2xl lg:text-3xl normal-case">
            {gallery.title}
          </h1>
        </div>
        <div>
          <SectionTitle title="À propos" />
          <HtmlContent html={gallery?.content} />
        </div>

        <div>
          <SectionTitle title="Galerie photos" />
          <GalleryImages images={gallery.full_images} />
        </div>
      </section>
    </div>
  );
}

/** @return {import('next').Metadata} */
export async function generateMetadata({ params }) {
  const { slug } = params;
  const gallery = await GalleryService.getGallery(slug);

  return {
    title: gallery.title,
    description: gallery.description,
    openGraph: {
      type: "article",
      url: getUrl(`/gallery/${gallery.slug}`),
      images: [gallery.full_thumbnail],
      tags: gallery.hashtages,
    },
    alternates: {
      canonical: `/gallery/${gallery.slug}`,
    },
  };
}
