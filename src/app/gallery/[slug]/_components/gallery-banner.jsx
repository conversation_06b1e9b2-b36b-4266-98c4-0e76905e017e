export default function GalleryBanner({ banner }) {
  const isVideo = banner.endsWith(".mp4");

  return (
    <div className="relative w-full aspect-[16/6] overflow-hidden">
      {isVideo ? (
        <video
          autoPlay
          muted
          loop
          controls
          className="absolute inset-0 h-full w-full object-cover"
        >
          <source src={banner} type="video/mp4" />
        </video>
      ) : (
        <img
          src={banner}
          alt="pag-banner"
          className="absolute inset-0 h-full w-full object-cover"
        />
      )}
    </div>
  );
}
