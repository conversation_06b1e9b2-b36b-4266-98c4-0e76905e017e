"use client";

import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

export default function GalleryImages({ images }) {
  return (
    <div>
      <Swiper
        modules={[Navigation]}
        slidesPerView={1}
        spaceBetween={10}
        loop
        navigation
        breakpoints={{
          1100: { slidesPerView: 3 },
          786: { slidesPerView: 2 },
        }}
      >
        {images.map((image, key) => (
          <SwiperSlide
            key={key}
            className="aspect-video w-full overflow-hidden bg-secondary"
          >
            <img
              src={image}
              alt="Gallery"
              className="w-full h-full object-cover"
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
}
