import PageBanner from "@/components/page-banner";
import SectionTitle from "@/components/section-title";
import { Button } from "@/components/ui/button";
import { GalleryService } from "@/services/gallery/gallery.service";
import { XIcon } from "lucide-react";
import Link from "next/link";
import GalleriesFilter from "./_components/galleries-filter";
import GalleryBox from "./_components/gallery-box";

export default async function GalleriesPage({ searchParams: filters }) {
  const data = await GalleryService.getGalleries(filters);

  return (
    <div className="-mt-[1.5rem] space-y-20 pb-20">
      <PageBanner
        title="Galerie"
        background="/assets/images/gallery-banner.webp"
      />

      <section className="container-lg pt-0">
        <SectionTitle title="Notre galerie" />

        <GalleriesFilter filters={filters} />

        <div>
          {data?.galleries?.length > 0 ? (
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {data.galleries.map((gallery) => (
                <GalleryBox key={gallery.id} gallery={gallery} />
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center py-10 gap-2">
              <h6 className="text-xl font-semibold">Aucune Galerie trouvé!</h6>
              {Object.entries(filters)?.some(([_, value]) => value) && (
                <Button variant="outline" size="sm" asChild>
                  <Link href="/gallery">
                    <XIcon /> Effacer les filtres
                  </Link>
                </Button>
              )}
            </div>
          )}
        </div>
      </section>
    </div>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Galerie",
  alternates: {
    canonical: "/gallery",
  },
};
