"use client";

import { But<PERSON> } from "@/components/ui/button";
import { SlidersHorizontalIcon } from "lucide-react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

const listFilters = [
  {
    label: "Tous",
    value: "",
  },
  {
    label: "Agricole",
    value: "Agricole",
  },
  {
    label: "Industriel",
    value: "Industriel",
  },
  {
    label: "Résidentiel",
    value: "Résidentiel",
  },
  {
    label: "Tertiaire",
    value: "Tertiaire",
  },
  {
    label: "Événement",
    value: "Événement",
  },
  {
    label: "Presse",
    value: "Presse",
  },
];

export default function GalleriesFilter({ filters }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  function onFilterChange(key, value) {
    const current = new URLSearchParams(Array.from(searchParams.entries()));

    if (!value) {
      current.delete(key);
    } else {
      current.set(key, value);
    }

    const search = current.toString();

    const query = search ? `?${search}` : "";

    router.push(`${pathname}${query}`, { scroll: false });
  }

  const activeFilter = filters.category ?? "";

  return (
    <div className="my-6 flex items-center gap-2">
      <SlidersHorizontalIcon className="w-6 h-6" />
      <ul className="flex items-center overflow-x-auto justify gap-2">
        {listFilters.map((filter) => (
          <li key={filter.value} className="flex-1">
            <Button
              variant={
                activeFilter === filter.value ? "brand-primary" : "outline"
              }
              onClick={() => {
                onFilterChange("category", filter.value);
              }}
            >
              <span>{filter.label}</span>
            </Button>
          </li>
        ))}
      </ul>
    </div>
  );
}
