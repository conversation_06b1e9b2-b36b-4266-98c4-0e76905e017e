"use client";

import { formatDate } from "@/lib/helpers";
import { useCheckOrder } from "@/services/customer";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import { Formik } from "formik";
import { Package, Truck } from "react-feather";
import { TailSpin } from "react-loader-spinner";
import { object, string } from "yup";

const formSchema = object({
  ref: string().required("Ce champ est obligatoire"),
  email: string()
    .email("Adresse e-mail non valide")
    .required("Ce champ est obligatoire"),
});

const genInitialValues = () => ({
  email: "",
  password: "",
});

export default function CheckOrderContainer() {
  const { checkOrder, order, isLoading, error } = useCheckOrder();

  const currentStatus = (current) => {
    let status_1 = ["draft", "registred"];
    let status_2 = ["deposit", "shipped"];
    let status_3 = ["arrived", "out_for_delivery"];
    let status_4 = ["Delivered"];
    let status_5 = ["delivery_fail"];
    let status_6 = ["returned", "returned_after", "returned_delivered"];
    let status_7 = ["canceled"];

    if (status_1.includes(current)) {
      return 1;
    } else if (status_2.includes(current)) {
      return 2;
    } else if (status_3.includes(current)) {
      return 3;
    } else if (status_4.includes(current)) {
      return 4;
    } else if (status_5.includes(current)) {
      return 5;
    } else if (status_6.includes(current)) {
      return 6;
    } else if (status_7.includes(current)) {
      return 7;
    }
    return null;
  };

  function OrderStatus(status) {
    switch (status) {
      case "0":
        return "Initiée";
      case "1":
        return "Validée";
      case "2":
        return "En cours";
      case "3":
        return "Livrée";
      default:
        return "Annulée";
    }
  }

  const onSubmit = (values) => {
    checkOrder(values);
  };

  return (
    <section className="section-404 section-lg-space">
      <div className="container-lg">
        <div>
          <Formik
            initialValues={genInitialValues()}
            validationSchema={formSchema}
            onSubmit={onSubmit}
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              handleSubmit,
            }) => (
              <form
                onSubmit={handleSubmit}
                className="row flex-column align-items-center justify-content-center"
              >
                <div className="col-12 col-md-6">
                  <div className="form-floating theme-form-floating">
                    <input
                      type="text"
                      autoFocus
                      className="form-control text-center"
                      id="ref"
                      name="ref"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.ref}
                    />
                    <label htmlFor="ref">
                      Référence<small className="star-mark"> *</small>
                    </label>
                  </div>
                  <span className="error-form">
                    {errors.ref && touched.ref && errors.ref}
                  </span>
                </div>
                <div className="col-12 col-md-6 mt-4">
                  <div className="form-floating theme-form-floating">
                    <input
                      type="text"
                      className="form-control text-center"
                      id="email"
                      name="email"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.email}
                    />
                    <label htmlFor="email">Email</label>
                  </div>
                  <span className="error-form">
                    {errors.email && touched.email && errors.email}
                  </span>
                </div>
                <div className="col-12">
                  <button
                    className="btn theme-bg-color btn-md fw-bold text-light mt-4 mx-auto"
                    type="submit"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <TailSpin
                        type="ThreeDots"
                        color="#fff"
                        height={20}
                        width={20}
                      />
                    ) : (
                      "Chercher"
                    )}
                  </button>
                </div>
                {error && (
                  <div className="col-12 col-md-6">
                    <ErrorSnackbar message={error?.message} />
                  </div>
                )}
              </form>
            )}
          </Formik>

          {order && (
            <div className="col-12 col-md-8 dashboard-order guest-order">
              <div className="order-contain">
                <div className="order-box dashboard-bg-box">
                  <div className="order-container">
                    <div className="order-detail">
                      <h4>
                        <div className="order-icon">
                          <i data-feather="box"></i>
                        </div>
                        &nbsp; Ref
                        <span>{order?.ref}</span>
                      </h4>
                    </div>
                    <div className="order-detail">
                      <h4>
                        Statut <span>{OrderStatus(order?.status)}</span>
                      </h4>
                    </div>
                    <div className="order-detail">
                      {/* <h4>La date <span>{ item?.created_at }</span></h4> */}
                      <h4>
                        La date <span>{formatDate(order?.date_creation)}</span>
                      </h4>
                    </div>
                  </div>

                  {order?.delivery_note ? (
                    <div className="link-to-file">
                      <a href={order?.delivery_note} target="_blank">
                        <img src="/assets/images/pdf.png" alt="Commande" /> Voir
                        la bon de commande
                      </a>
                    </div>
                  ) : (
                    <div />
                  )}

                  {order?.lines &&
                    order?.lines.length &&
                    order?.lines.map((productItem, productKey) => (
                      <div
                        className="product-order-detail"
                        key={`guest-order-${productKey}`}
                      >
                        <div className="order-wrap">
                          <h3>{productItem?.product_label}</h3>
                          <ul className="mt-2 product-size">
                            <li>
                              <div className="size-box">
                                <h6 className="text-content">Prix : </h6>
                                <h5>
                                  {Math.round(productItem?.total_ttc * 100) /
                                    100}{" "}
                                  DH TTC
                                </h5>
                              </div>
                            </li>
                            <li>
                              <div className="size-box">
                                <h6 className="text-content">Quantité : </h6>
                                <h5>x{productItem?.qty}</h5>
                              </div>
                            </li>
                          </ul>
                        </div>
                      </div>
                    ))}
                  {order?.new_tracking && order?.new_status ? (
                    <div className="col-12 order-detail mt-4">
                      <div className="row g-sm-4 g-3">
                        <div className="col-6">
                          <div className="order-details-contain">
                            <div className="order-tracking-icon">
                              <Package className="text-content" />
                            </div>
                            <div className="order-details-name">
                              <h5 className="text-content">Code de suivi</h5>
                              <h2 className="theme-color">
                                {order?.new_tracking}
                              </h2>
                            </div>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="order-details-contain">
                            <div className="order-tracking-icon">
                              <Truck className="text-content" />
                            </div>

                            <div className="order-details-name">
                              <h5 className="text-content">Service</h5>
                              {order?.new_tracking && order?.new_status ? (
                                <img
                                  src="/assets/images/logo-ctm.png"
                                  className="img-fluid blur-up lazyload"
                                  alt="CTM"
                                />
                              ) : order?.shipping_method_id &&
                                order?.shipping_method_id == 16 ? (
                                <img
                                  src="/assets/images/ecowatt-log.jpeg"
                                  className="img-fluid blur-up lazyload"
                                  alt="Ecowatt"
                                />
                              ) : (
                                <img
                                  src="/assets/images/in-place.png"
                                  className="img-fluid blur-up lazyload"
                                  alt="In-place"
                                />
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="col-12 overflow-hidden">
                          <ol className="progtrckr">
                            <li
                              className={
                                currentStatus(order?.new_status) >= 1 &&
                                currentStatus(order?.new_status) < 5
                                  ? "progtrckr-done"
                                  : "progtrckr-todo"
                              }
                            >
                              <h5>En cours de préparation</h5>
                              {/* <h6>05:43 AM</h6> */}
                            </li>
                            <li
                              className={
                                currentStatus(order?.new_status) >= 2 &&
                                currentStatus(order?.new_status) < 5
                                  ? "progtrckr-done"
                                  : "progtrckr-todo"
                              }
                            >
                              <h5>Colis collecté et Expédié</h5>
                              {/* <h6>01:21 PM</h6> */}
                            </li>
                            <li
                              className={
                                currentStatus(order?.new_status) >= 3 &&
                                currentStatus(order?.new_status) < 5
                                  ? "progtrckr-done"
                                  : "progtrckr-todo"
                              }
                            >
                              <h5>En cours de livraison</h5>
                              {/* <h6>Processing</h6> */}
                            </li>
                            <li
                              className={
                                currentStatus(order?.new_status) === 4
                                  ? "progtrckr-done"
                                  : "progtrckr-todo"
                              }
                            >
                              <h5>Livré</h5>
                              {/* <h6>Processing</h6> */}
                            </li>
                            {currentStatus(order?.new_status) === 5 ? (
                              <li className="progtrckr-done">
                                <h5>Echec de livraison</h5>
                                {/* <h6>Pending</h6> */}
                              </li>
                            ) : null}
                            {currentStatus(order?.new_status) === 6 ? (
                              <li className="progtrckr-done">
                                <h5>Retourné</h5>
                                {/* <h6>Pending</h6> */}
                              </li>
                            ) : null}
                            {currentStatus(order?.new_status) === 7 ? (
                              <li className="progtrckr-done">
                                <h5>livraison annulée</h5>
                                {/* <h6>Pending</h6> */}
                              </li>
                            ) : null}
                          </ol>
                        </div>
                      </div>
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <div>
        <p className="explecation-star-mark">
          <span className="star-mark">*</span> La référence de votre commande
          est disponible sur votre boîte email.
        </p>
      </div>
    </section>
  );
}
