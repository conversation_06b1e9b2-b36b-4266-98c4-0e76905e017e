"use client";

import { useState } from "react";
import FormContact from "./form-contact";

const agencies = [
  {
    key: "AGADIR",
    label: "AGENCE MAROC SUD",
    address: "Rue d'Inzegane route de l'hopital, El <PERSON>,Ait <PERSON>.",
    pb: "!1m18!1m12!1m3!1d3443.209193272619!2d-9.50208119096326!3d30.34500409915027!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xdb3c78d508d4211%3A0xbf1aa8a288badc6c!2sECOWATT!5e0!3m2!1sen!2sma!4v1663781258853!5m2!1sen!2sma",
    banner: "/assets/images/agencies/agadir.webp",
    collaborators: [
      {
        gender: "M",
        fullName: "MOHAMED HAYA",
        phone: "+212 662 781 382",
        email: "<EMAIL>",
        role: "Représentant commercial",
        image: "/assets/images/collab/mohamed-haya.jpg",
      },
    ],
  },
  {
    key: "FKIH BEN SALEH",
    label: "AGENCE AZILAL",
    address: "N°422 Avenu Al Massira, Fkih Ben Salah.",
    pb: "!1m18!1m12!1m3!1d3364.8303657926094!2d-6.676777884824889!3d32.50396278105525!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0xf5fabfb9512eedbc!2zMzLCsDMwJzE0LjMiTiA2wrA0MCcyOC41Ilc!5e0!3m2!1sfr!2sma!4v1664274121315!5m2!1sfr!2sma",
    banner: "/assets/images/agencies/fkih-ben-salh.webp",
    collaborators: [
      {
        gender: "M",
        fullName: "RIDA ATIFI",
        phone: "+212 667 911 643",
        email: "<EMAIL>",
        role: "Représentant commercial",
        image: "/assets/images/collab/Rida ATIFI.webp",
      },
    ],
  },
  {
    key: "OUJDA",
    label: "AGENCE MAROC EAST",
    address: "LTS Boubcher RTE Maghnia N°195 Oujda",
    pb: "!1m18!1m12!1m3!1d3280.2119651804105!2d-1.8986944000000001!3d34.6998333!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0!2zMzTCsDQxJzU5LjQiTiAxwrA1Myc1NS4zIlc!5e0!3m2!1sen!2sma!4v1664274085111!5m2!1sen!2sma",
    banner: "/assets/images/agencies/oujda.webp",
    collaborators: [
      {
        gender: "M",
        fullName: "MOHAMMED AAMIR",
        phone: "+212 662 770 985",
        email: "<EMAIL>",
        role: "Représentant commercial",
        image: "/assets/images/collab/Mohammed AAMIR.webp",
      },
    ],
  },
  {
    key: "MEKNES",
    label: "AGENCE SAïS",
    address: "20 Rue Reprise IMM 20 MAG 5 Lot YOUSSR Sidi Bouzekri Meknès",
    pb: "!1m18!1m12!1m3!1d3280.2119651804105!2d-1.8986944000000001!3d34.6998333!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0!2zMzTCsDQxJzU5LjQiTiAxwrA1Myc1NS4zIlc!5e0!3m2!1sen!2sma!4v1664274085111!5m2!1sen!2sma",
    banner: "/assets/images/agencies/meknes.webp",
    collaborators: [
      {
        gender: "Mme",
        fullName: "SAIDA SIDKI",
        phone: "+212 666 955 890",
        email: "<EMAIL>",
        role: "Représentant commercial",
        image: "/assets/images/collab/saida-sidki.jpg",
      },
    ],
  },
  {
    key: "KENITRA",
    label: "AGENCE WEST",
    address: "",
    pb: "!1m18!1m12!1m3!1d3280.2119651804105!2d-1.8986944000000001!3d34.6998333!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0!2zMzTCsDQxJzU5LjQiTiAxwrA1Myc1NS4zIlc!5e0!3m2!1sen!2sma!4v1664274085111!5m2!1sen!2sma",
    banner: "/assets/images/agencies/kenitra.webp",
    collaborators: [
      {
        gender: "M",
        fullName: "MOHAMED HAYA",
        phone: "+212 662 781 382",
        email: "<EMAIL>",
        role: "Représentant commercial",
        image: "/assets/images/collab/mohamed-haya.jpg",
      },
    ],
  },
  {
    key: "MARRAKECH",
    label: "AGENCE ATLAS",
    // address: "",
    pb: "!1m18!1m12!1m3!1d3280.2119651804105!2d-1.8986944000000001!3d34.6998333!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0!2zMzTCsDQxJzU5LjQiTiAxwrA1Myc1NS4zIlc!5e0!3m2!1sen!2sma!4v1664274085111!5m2!1sen!2sma",
    banner: "/assets/images/agencies/marrakech.webp",
    collaborators: [
      {
        gender: "M",
        fullName: "MOHAMED HAYA",
        phone: "+212 662 781 382",
        email: "<EMAIL>",
        role: "Représentant commercial",
        image: "/assets/images/collab/mohamed-haya.jpg",
      },
    ],
  },
];

export default function ContactPage() {
  const [isActive, setIsActive] = useState(agencies[0].key);

  return (
    <section>
      <div className="container-lg mb-5">
        <div className="row">
          <div className="col-12">
            <div className="product-section-box m-0">
              <ul className="nav nav-tabs custom-nav" id="myTab" role="tablist">
                {agencies.map((agency) => (
                  <li key={agency.key} className="nav-item">
                    <button
                      type="button"
                      className={`nav-link ${
                        isActive === agency.key ? "active" : ""
                      }`}
                      onClick={() => setIsActive(agency.key)}
                    >
                      {agency.key}
                    </button>
                  </li>
                ))}
              </ul>

              <div className="tab-content custom-tab">
                {agencies.map((agency) => (
                  <div
                    key={agency.key}
                    className={`tab-pane fade ${
                      isActive === agency.key ? "show active" : ""
                    }`}
                  >
                    <div className="product-description">
                      <div className="contact-box-section">
                        <div className="mb-5">
                          <img src={agency.banner} alt={agency.label} />
                        </div>
                        <div>
                          <div className="row g-lg-5 g-3">
                            <div className="col-lg-6">
                              <div className="left-sidebar-box">
                                <div className="row">
                                  <div className="col-xl-12">
                                    <div className="contact-detail">
                                      <div className="row g-4">
                                        <div className="col-xxl-12 col-lg-12 col-sm-6 mb-5">
                                          <div className="contact-detail-box">
                                            <div className="contact-icon">
                                              <i className="fa-solid fa-location-dot"></i>
                                            </div>
                                            <div className="contact-detail-title">
                                              <h4>{agency.label}</h4>
                                            </div>
                                            <div className="contact-detail-contain">
                                              <p>{agency.address}</p>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  {agency.collaborators.map((collaborator) => (
                                    <div
                                      key={collaborator.fullName}
                                      className="col-xl-12 mb-5"
                                    >
                                      <div className="contact-title">
                                        <h3>{collaborator.role}</h3>
                                      </div>

                                      <div className="contact-detail">
                                        <div className="row g-4">
                                          <div className="col-xxl-6 col-lg-12 col-sm-6">
                                            <div className="contact-detail-box">
                                              <div className="contact-detail-title">
                                                <div className="contact-image">
                                                  {/* <i className="fa-solid fa-user"></i> */}
                                                  <img
                                                    src={collaborator.image}
                                                    alt={collaborator.fullName}
                                                  />
                                                </div>
                                                <h4>{collaborator.gender}</h4>
                                              </div>

                                              <div className="contact-detail-contain">
                                                <p>{collaborator.fullName}</p>
                                              </div>
                                            </div>
                                          </div>
                                          <div className="col-xxl-6 col-lg-12 col-sm-6">
                                            <div className="contact-detail-box">
                                              <div className="contact-icon">
                                                <i className="fa-solid fa-phone"></i>
                                              </div>
                                              <div className="contact-detail-title">
                                                <h4>Téléphone</h4>
                                              </div>

                                              <div className="contact-detail-contain">
                                                <a
                                                  href={`tel:${collaborator.phone}`}
                                                >
                                                  <p>{collaborator.phone}</p>
                                                </a>
                                              </div>
                                            </div>
                                          </div>
                                          <div className="col-xxl-6 col-lg-12 col-sm-6">
                                            <div className="contact-detail-box">
                                              <div className="contact-icon">
                                                <i className="fa-solid fa-message"></i>
                                              </div>
                                              <div className="contact-detail-title">
                                                <h4>Adresse email</h4>
                                              </div>

                                              <div className="contact-detail-contain">
                                                <a
                                                  href={`mailto:${collaborator.email}`}
                                                >
                                                  <p>{collaborator.email}</p>
                                                </a>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>

                            <div className="col-lg-6">
                              <FormContact place={agency.label} />
                            </div>
                          </div>
                        </div>
                      </div>
                      <section className="map-section">
                        <div className="container-fluid p-0">
                          <div className="map-box">
                            <iframe
                              title={agency.label}
                              src={`https://www.google.com/maps/embed?pb=${agency.pb}`}
                              loading="lazy"
                            ></iframe>
                          </div>
                        </div>
                      </section>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
