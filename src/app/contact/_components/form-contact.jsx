"use client";

import { useSendContactMutation } from "@/services/common";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { Formik } from "formik";
import { Tail<PERSON>pin } from "react-loader-spinner";
import { object, string } from "yup";

const formSchema = object({
  fname: string()
    .min(1, "Trop court!")
    .max(100, "Trop long!")
    .required("Ce champ est obligatoire"),
  lname: string()
    .min(1, "Trop court!")
    .max(100, "Trop long!")
    .required("Ce champ est obligatoire"),
  phone: string()
    .required("Ce champ est obligatoire")
    .matches(
      /^0(5|6|7)[0-9]{8}$/,
      "Le numéro de téléphone doit être comme ça 0601020304"
    )
    .min(1, "Trop court!")
    .max(10, "Trop long!"),
  email: string()
    .email("Adresse e-mail invalide")
    .required("Ce champ est obligatoire"),
  message: string().required("Ce champ est obligatoire"),
});

const genInitialValues = (place) => ({
  by: place,
  fname: "",
  lname: "",
  phone: "",
  email: "",
  message: "",
});

export default function FormContact({ place = "world" }) {
  const { sendContact, isLoading, isSuccess, error } = useSendContactMutation();

  function onSubmit(data, actions) {
    sendContact(data, {
      onSuccess() {
        actions.resetForm({
          values: genInitialValues(place),
        });
      },
    });
  }

  return (
    <>
      <div className="title d-xxl-none d-block">
        <h2>Contactez-nous</h2>
      </div>
      <div className="right-sidebar-box">
        {error && <ErrorSnackbar message={error.message} />}

        <Formik
          initialValues={genInitialValues(place)}
          validationSchema={formSchema}
          onSubmit={onSubmit}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            handleSubmit,
          }) => (
            <form onSubmit={handleSubmit}>
              <div className="row">
                <div className="col-xxl-6 col-lg-12 col-sm-6">
                  <div className="mb-md-4 mb-3 custom-form">
                    <label htmlFor="fname" className="form-label">
                      Prénom
                    </label>
                    <div className="custom-input">
                      <input
                        type="text"
                        className="form-control"
                        id="fname"
                        placeholder="Entrez votre prénom"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.fname}
                      />
                      <i className="fa-solid fa-user"></i>
                    </div>
                    <span className="error-form">
                      {errors.fname && touched.fname && errors.fname}
                    </span>
                  </div>
                </div>
                <div className="col-xxl-6 col-lg-12 col-sm-6">
                  <div className="mb-md-4 mb-3 custom-form">
                    <label htmlFor="lname" className="form-label">
                      Nom
                    </label>
                    <div className="custom-input">
                      <input
                        type="text"
                        className="form-control"
                        id="lname"
                        placeholder="Entrer le nom de famille"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.lname}
                      />
                      <i className="fa-solid fa-user"></i>
                    </div>
                    <span className="error-form">
                      {errors.lname && touched.lname && errors.lname}
                    </span>
                  </div>
                </div>

                <div className="col-xxl-6 col-lg-12 col-sm-6">
                  <div className="mb-md-4 mb-3 custom-form">
                    <label htmlFor="email" className="form-label">
                      Adresse e-mail
                    </label>
                    <div className="custom-input">
                      <input
                        type="email"
                        className="form-control"
                        id="email"
                        placeholder="Entrer l'adresse e-mail"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.email}
                      />
                      <i className="fa-solid fa-envelope"></i>
                    </div>
                    <span className="error-form">
                      {errors.email && touched.email && errors.email}
                    </span>
                  </div>
                </div>
                <div className="col-xxl-6 col-lg-12 col-sm-6">
                  <div className="mb-md-4 mb-3 custom-form">
                    <label htmlFor="phone" className="form-label">
                      Numéro de téléphone
                    </label>
                    <div className="custom-input">
                      <input
                        type="tel"
                        className="form-control"
                        id="phone"
                        placeholder="Entrez votre numéro de téléphone"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.phone}
                      />
                      <i className="fa-solid fa-mobile-screen-button"></i>
                    </div>
                    <span className="error-form">
                      {errors.phone && touched.phone && errors.phone}
                    </span>
                  </div>
                </div>

                <div className="col-12">
                  <div className="mb-md-4 mb-3 custom-form">
                    <label htmlFor="message" className="form-label">
                      Message
                    </label>
                    <div className="custom-textarea">
                      <textarea
                        className="form-control"
                        id="message"
                        placeholder="Entrez votre message"
                        rows="6"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.message}
                      ></textarea>
                      <i className="fa-solid fa-message"></i>
                    </div>
                    <span className="error-form">
                      {errors.message && touched.message && errors.message}
                    </span>
                  </div>
                </div>
              </div>
              <button
                type="submit"
                className="btn btn-animation btn-md fw-bold ms-auto"
                disabled={isLoading}
              >
                {isLoading ? (
                  <TailSpin
                    type="ThreeDots"
                    color="#fff"
                    height={20}
                    width={20}
                  />
                ) : (
                  "Envoyer le message"
                )}
              </button>
            </form>
          )}
        </Formik>

        {isSuccess && (
          <SuccessSnackbar message="Votre e-mail a été envoyé avec succès" />
        )}
      </div>
    </>
  );
}
