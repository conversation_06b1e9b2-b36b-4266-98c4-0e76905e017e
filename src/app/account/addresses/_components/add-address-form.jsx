"use client";

import { useCountries, useCountryCities } from "@/services/common";
import { useCreateAddressMutation } from "@/services/customer";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessAnimation from "@/shared/components/success-animation";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { useFormik } from "formik";
import { TailSpin } from "react-loader-spinner";
import { number, object, string } from "yup";

const formSchema = object({
  line_1: string()
    .min(1, "Trop court!")
    .max(10000, "Trop long!")
    .required("Ce champ est obligatoire"),
  line_2: string().min(1, "Trop court!").max(10000, "Trop long!").notRequired(),
  country: number().required("Ce champ est obligatoire"),
  city: number().required("Ce champ est obligatoire"),
  type: string()
    .oneOf(["delivery", "billing"])
    .defined()
    .required("Ce champ est obligatoire"),
});

const genInitialValues = {
  line_1: "",
  line_2: "",
  type: "",
  country: "",
  city: "",
  is_default: false,
};

export default function AddAddressForm() {
  const { countries } = useCountries();

  const {
    createAddress,
    isLoading: isCreating,
    isSuccess,
    error,
  } = useCreateAddressMutation();

  async function onSubmit(values, actions) {
    createAddress(values, {
      onSuccess() {
        actions.resetForm({
          values: genInitialValues,
        });
      },
    });
  }

  const addressFromik = useFormik({
    initialValues: genInitialValues,
    validationSchema: formSchema,
    onSubmit,
  });

  const { values, errors, touched, handleChange, handleBlur, handleSubmit } =
    addressFromik;

  const { cities } = useCountryCities(values.country);

  return (
    <>
      {error && <ErrorSnackbar message={error.message} />}

      {isSuccess && <SuccessSnackbar message={`L'adresse a été créée`} />}

      {isSuccess ? (
        <SuccessAnimation />
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            <div className="row g-4">
              <div className="col-12">
                <div className="d-flex align-items-center justify-content-start">
                  <div className="d-flex align-items-center">
                    <input
                      type="radio"
                      id="delivery"
                      name="type"
                      checked={values.type === "delivery"}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={`delivery`}
                    />
                    &nbsp;
                    <label htmlFor="delivery">Livraison</label>
                  </div>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <div className="d-flex align-items-center">
                    <input
                      type="radio"
                      id="billing"
                      name="type"
                      checked={values.type === "billing"}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={`billing`}
                    />
                    &nbsp;
                    <label htmlFor="billing">Facture</label>
                  </div>
                </div>
                <span className="error-form">
                  {errors.type && touched.type && errors.type}
                </span>
              </div>

              <div className="col-12 col-md-6">
                <div className="form-floating theme-form-floating">
                  <input
                    type="text"
                    className="form-control"
                    id="line_1"
                    name="line_1"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={values.line_1}
                  />
                  <label htmlFor="line_1">Ligne 1</label>
                </div>
                <span className="error-form">
                  {errors.line_1 && touched.line_1 && errors.line_1}
                </span>
              </div>
              <div className="col-12 col-md-6">
                <div className="form-floating theme-form-floating">
                  <input
                    type="text"
                    className="form-control"
                    id="line_2"
                    name="line_2"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={values.line_2}
                  />
                  <label htmlFor="line_2">Ligne 2</label>
                </div>
                <span className="error-form">
                  {errors.line_2 && touched.line_2 && errors.line_2}
                </span>
              </div>

              <div className="col-12 col-md-6">
                <div className="form-floating theme-form-floating">
                  <select
                    value={values.country}
                    className="form-control"
                    id="country"
                    name="country"
                    onChange={handleChange}
                    onBlur={handleBlur}
                  >
                    <option value="0" label="Choisissez un pays">
                      Choisissez un pays
                    </option>
                    {countries?.map((item) => (
                      <option key={item.id} value={item.id} label={item.name}>
                        {item.name}
                      </option>
                    ))}
                  </select>
                </div>
                <span className="error-form">
                  {errors.country && touched.country && errors.country}
                </span>
              </div>

              <div className="col-12 col-md-6">
                <div className="form-floating theme-form-floating">
                  <select
                    value={values.city}
                    className="form-control"
                    id="city"
                    name="city"
                    onChange={handleChange}
                    onBlur={handleBlur}
                  >
                    <option value="0" label="Sélectionnez une ville">
                      Sélectionnez une ville
                    </option>
                    {cities?.map((item) => (
                      <option key={item.id} value={item.id} label={item.name}>
                        {item.name}
                      </option>
                    ))}
                  </select>
                </div>
                <span className="error-form">
                  {errors.city && touched.city && errors.city}
                </span>
              </div>

              <div className="col-12">
                <div className="d-flex align-items-center">
                  <input
                    type="checkbox"
                    id="is_default"
                    name="is_default"
                    checked={values.is_default == 1}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={1}
                  />
                  &nbsp;
                  <label htmlFor="is_default">Définir par défaut</label>
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer">
            <button
              disabled={isCreating}
              className="btn theme-bg-color btn-md fw-bold text-light"
              type="submit"
            >
              {isCreating ? (
                <TailSpin
                  type="ThreeDots"
                  color="#fff"
                  height={20}
                  width={20}
                />
              ) : (
                "Ajouter"
              )}
            </button>
          </div>
        </form>
      )}
    </>
  );
}
