"use client";

import { useAddresses } from "@/services/customer";
import Modal from "@/shared/components/modal";
import { useState } from "react";
import { InfinitySpin } from "react-loader-spinner";
import AddAddressForm from "./_components/add-address-form";
import AddressBox from "./_components/address-box";
import EditAddressForm from "./_components/edit-address-form";

export default function MyAddresses() {
  const [addAddressOpen, setAddAddressOpen] = useState(false);
  const [editAddress, setEditAddress] = useState(null);

  const { addresses, isLoading } = useAddresses();

  return (
    <div className="dashboard-address">
      <div className="title title-flex">
        <div>
          <h2>Mes adresses</h2>
          <span className="title-leaf">
            <img
              src="/assets/svg/leaf.png"
              alt=""
              className="icon-width bg-gray"
            />
          </span>
        </div>
        <button
          type="button"
          onClick={() => setAddAddressOpen(true)}
          className="btn theme-bg-color text-white btn-sm fw-bold mt-lg-0 mt-3"
        >
          <i data-feather="plus" className="me-2"></i>
          Ajouter une nouvelle adresse
        </button>
      </div>

      <Modal
        open={addAddressOpen}
        onClose={() => setAddAddressOpen(false)}
        title="Ajouter une nouvelle adresse"
      >
        <AddAddressForm />
      </Modal>

      <Modal
        open={editAddress !== null}
        onClose={() => setEditAddress(null)}
        title="Modifier l'adresse"
      >
        <EditAddressForm address={editAddress} />
      </Modal>

      {isLoading ? (
        <div className="min-vh-100 px-4 py-2 d-flex align-items-center justify-content-center">
          <InfinitySpin
            type="ThreeDots"
            color="#2A3466"
            height={220}
            width={220}
          />
        </div>
      ) : !addresses?.length ? (
        <h2 className="text-center my-5">Aucune adresse trouvée</h2>
      ) : (
        <div className="row g-sm-4 g-3">
          {addresses.map((item, key) => (
            <AddressBox
              key={key}
              changeCurrentAddress={setEditAddress}
              address={item}
            />
          ))}
        </div>
      )}
    </div>
  );
}
