"use client";

import { But<PERSON> } from "@/components/ui/button";
import { formatDate, formatPrice } from "@/lib/helpers";
import {
  useDownloadOrderDocumentMutation,
  useOrders,
} from "@/services/customer";
import { MoveLeftIcon, MoveRightIcon } from "lucide-react";
import { parseAsIndex, useQueryState } from "nuqs";
import { useState } from "react";
import { InfinitySpin, TailSpin } from "react-loader-spinner";

export default function Orders() {
  const [page, setPage] = useQueryState("page", parseAsIndex.withDefault(0));
  const [code, setCode] = useQueryState("code", { defaultValue: "" });
  const [status, setStatus] = useQueryState("status", { defaultValue: "" });

  const { orders, isLoading } = useOrders({ v2: true, page, code, status });

  return (
    <div className="dashboard-order">
      <div className="title">
        <h2>Historique de mes commandes</h2>
        <span className="title-leaf title-leaf-gray">
          <img
            src="/assets/svg/leaf.png"
            alt="leaf"
            className="icon-width bg-gray"
          />
        </span>
      </div>

      <div className="filter">
        <div className="form-group">
          <label>Code</label>
          <input
            type="text"
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                setPage(0);
                setCode(e.target.value);
              }
            }}
            className="form-control"
          />
        </div>
        <div className="form-group">
          <label>Status</label>
          <select
            className="form-control form-select"
            onChange={(event) => {
              setPage(0);
              setStatus(event.target.value);
            }}
            value={status}
          >
            <option></option>
            <option value={`1`}>Validée</option>
            <option value={`2`}>En cours</option>
            <option value={`3`}>Livrée</option>
            <option value={`0`}>Initiée</option>
            <option value={`-1`}>Annulée</option>
          </select>
        </div>
      </div>

      {isLoading && (
        <div className="px-4 py-2 d-flex align-items-center justify-content-center">
          <InfinitySpin
            type="ThreeDots"
            color="#2A3466"
            height={220}
            width={220}
          />
        </div>
      )}

      {!isLoading && !orders?.data?.length > 0 && (
        <h2 className="text-center my-5">Aucune commande trouvée</h2>
      )}

      {!isLoading && orders?.data?.length > 0 && (
        <>
          <div className="order-contain">
            {orders?.data?.map((item) => (
              <OrderRow key={item.id} order={item} />
            ))}
          </div>
          <div className="w-full mt-5 flex items-center gap-2 justify-center">
            <Button
              onClick={() => setPage((p) => p - 1)}
              disabled={isLoading || page < 1}
            >
              <MoveLeftIcon /> Précédent
            </Button>
            <Button
              onClick={() => setPage((p) => p + 1)}
              disabled={isLoading || !orders?.has_more}
            >
              <MoveRightIcon /> Suivant
            </Button>
          </div>
        </>
      )}

      {/* {!orders?.data?.length ? (
            <h2 className="text-center my-5">
              Vous n'avez pas encore passer aucune commande.
            </h2>
          )} */}
    </div>
  );
}

function OrderRow({ order }) {
  const [collapsed, setCollapsed] = useState(true);

  const { downloadOrderDocument, isLoading } =
    useDownloadOrderDocumentMutation();

  const downloadFile = async (id) => {
    downloadOrderDocument(
      { id },
      {
        onSuccess(data) {
          if (data) {
            window.open(data, "_blank");
          }
        },
      }
    );
  };

  function OrderStatus(status) {
    switch (status) {
      case "0":
        return "Initiée";
      case "1":
        return "Validée";
      case "2":
        return "En cours";
      case "3":
        return "Livrée";
      default:
        return "Annulée";
    }
  }

  return (
    <div className="order-box dashboard-bg-box">
      <div className="order-container" onClick={() => setCollapsed((c) => !c)}>
        <div className="order-detail">
          <h4>
            <div className="order-icon">
              <i data-feather="box"></i>
            </div>
            &nbsp; Ref
            <span>{order?.ref}</span>
          </h4>
        </div>
        <div className="order-detail">
          <h4>
            Statut <span>{OrderStatus(order?.status)}</span>
          </h4>
        </div>
        <div className="order-detail">
          {/* <h4>La date <span>{ order?.created_at }</span></h4> */}
          <h4>
            La date <span>{formatDate(order?.date_creation)}</span>
          </h4>
        </div>
      </div>
      <div className={`order-items ${!collapsed && "show"}`}>
        <div className="order-detail">
          <button
            type="button"
            className="btn download-btn"
            onClick={() => downloadFile(order?.id)}
            disabled={isLoading}
          >
            Télécharger le bon de commande
            {isLoading ? <TailSpin color="#fff" height={16} width={16} /> : ""}
          </button>
        </div>

        {order?.lines &&
          order?.lines.length &&
          order?.lines.map((productItem) => (
            <div className="product-order-detail" key={productItem.id}>
              <div className="order-wrap">
                <h3>{productItem?.product_label || productItem?.desc}</h3>
                <ul className="product-size mt-2">
                  <li>
                    <div className="size-box">
                      <h6 className="text-content">Prix : </h6>
                      {/* <h5>{productItem?.price} DH<sub><small>/ TTC</small></sub></h5> */}
                      <h5>
                        {Math.round(productItem?.total_ttc * 100) / 100} DH TTC
                      </h5>
                    </div>
                  </li>
                  <li>
                    <div className="size-box">
                      <h6 className="text-content">Quantité : </h6>
                      <h5>x{productItem?.qty}</h5>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          ))}
        <div className="col-12 order-detail mt-4">
          <div className="row g-sm-4 g-3">
            <div className="col-6">
              <div className="order-details-contain">
                <div className="order-tracking-icon">
                  {/* <Package className="text-content" /> */}
                </div>
                <div className="order-details-name">
                  <h5 className="text-content">Prix Total</h5>
                  <h3 className="theme-color">
                    {formatPrice(order.total_ttc)} DH TTC
                  </h3>
                </div>
              </div>
            </div>
            {/* <div className="col-6">
              <div className="order-details-contain">
                <div className="order-tracking-icon">
                  <Package className="text-content" />
                </div>
                <div className="order-details-name">
                  <h5 className="text-content">Code de suivi</h5>
                  <h2 className="theme-color">{order?.new_tracking}</h2>
                </div>
              </div>
            </div> */}
            {/* <div className="col-6">
              <div className="order-details-contain">
                <div className="order-tracking-icon">
                  <Truck className="text-content" />
                </div>

                <div className="order-details-name">
                  <h5 className="text-content">Service</h5>
                  {order?.new_tracking && order?.new_status ? (
                    <img
                      src="/assets/images/logo-ctm.png"
                      className="img-fluid blur-up lazyload"
                      alt="CTM"
                    />
                  ) : order?.shipping_method_id &&
                    order?.shipping_method_id == 16 ? (
                    <img
                      src="/assets/images/ecowatt-log.jpeg"
                      className="img-fluid blur-up lazyload"
                      alt="Ecowatt"
                    />
                  ) : (
                    <img
                      src="/assets/images/in-place.png"
                      className="img-fluid blur-up lazyload"
                      alt="In-place"
                    />
                  )}
                </div>
              </div>
            </div> */}

            {order?.new_tracking && order?.new_status ? (
              <div className="col-12 overflow-hidden">
                <ol className="progtrckr">
                  <li
                    className={
                      currentStatus(order?.new_status) >= 1 &&
                      currentStatus(order?.new_status) < 5
                        ? "progtrckr-done"
                        : "progtrckr-todo"
                    }
                  >
                    <h5>En cours de préparation</h5>
                    {/* <h6>05:43 AM</h6> */}
                  </li>
                  <li
                    className={
                      currentStatus(order?.new_status) >= 2 &&
                      currentStatus(order?.new_status) < 5
                        ? "progtrckr-done"
                        : "progtrckr-todo"
                    }
                  >
                    <h5>Colis collecté et Expédié</h5>
                    {/* <h6>01:21 PM</h6> */}
                  </li>
                  <li
                    className={
                      currentStatus(order?.new_status) >= 3 &&
                      currentStatus(order?.new_status) < 5
                        ? "progtrckr-done"
                        : "progtrckr-todo"
                    }
                  >
                    <h5>En cours de livraison</h5>
                    {/* <h6>Processing</h6> */}
                  </li>
                  <li
                    className={
                      currentStatus(order?.new_status) === 4
                        ? "progtrckr-done"
                        : "progtrckr-todo"
                    }
                  >
                    <h5>Livré</h5>
                    {/* <h6>Processing</h6> */}
                  </li>
                  {currentStatus(order?.new_status) === 5 ? (
                    <li className="progtrckr-done">
                      <h5>Echec de livraison</h5>
                      {/* <h6>Pending</h6> */}
                    </li>
                  ) : null}
                  {currentStatus(order?.new_status) === 6 ? (
                    <li className="progtrckr-done">
                      <h5>Retourné</h5>
                      {/* <h6>Pending</h6> */}
                    </li>
                  ) : null}
                  {currentStatus(order?.new_status) === 7 ? (
                    <li className="progtrckr-done">
                      <h5>livraison annulée</h5>
                      {/* <h6>Pending</h6> */}
                    </li>
                  ) : null}
                </ol>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
}
