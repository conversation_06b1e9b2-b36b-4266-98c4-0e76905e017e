"use client";

import { useAuthContext } from "@/providers/auth-provider";
import { useUpdateProfileMutation } from "@/services/customer";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessAnimation from "@/shared/components/success-animation";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { Formik } from "formik";
import React from "react";
import { TailSpin } from "react-loader-spinner";
import { mixed, object, string } from "yup";

const SUPPORTED_FORMATS = ["image/jpeg", "image/jpg", "image/png"];
const FILE_SIZE = 1024 * 2048;

const formSchema = object({
  avatar: mixed()
    .notRequired()
    .test(
      "FILE_SIZE",
      "Le fichier téléchargé est trop volumineux.",
      (value) => !value || (value && value.size <= FILE_SIZE)
    )
    .test(
      "FILE_FORMAT",
      "Le fichier téléchargé a un format non pris en charge.",
      (value) => !value || (value && SUPPORTED_FORMATS.includes(value.type))
    ),
  fname: string()
    .min(1, "Trop court!")
    .max(191, "Trop long!")
    .required("Ce champ est obligatoire"),
  lname: string()
    .min(1, "Trop court!")
    .max(191, "Trop long!")
    .required("Ce champ est obligatoire"),
  email: string()
    .email("Adresse e-mail non valide")
    .required("Ce champ est obligatoire"),
  mobile: string()
    .required("Ce champ est obligatoire")
    .matches(
      /^[0-9]{9}?$/,
      "Le numéro de téléphone doit être composé de 9 chiffres et respecter ce format: 601020304"
    ),
});

export default function UpdateProfileForm({ modelClose }) {
  const { currentUser } = useAuthContext();

  const [avatarPreview, setAvatarPreview] = React.useState(
    currentUser?.full_avatar
      ? currentUser?.full_avatar
      : "/assets/images/avatar.jpeg"
  );

  const { updateProfile, isLoading, isSuccess, error } =
    useUpdateProfileMutation();

  const onSubmit = async (data) => {
    updateProfile(data);
  };

  return (
    <>
      {error && <ErrorSnackbar message={error.message} />}
      {isSuccess && (
        <SuccessSnackbar message={`Mise à jour du profil réussie`} />
      )}
      {isSuccess ? (
        <SuccessAnimation />
      ) : (
        <Formik
          initialValues={{
            avatar: "",
            fname: currentUser?.fname,
            lname: currentUser?.lname,
            email: currentUser?.email,
            mobile: currentUser?.mobile,
          }}
          validationSchema={formSchema}
          onSubmit={onSubmit}
        >
          {({
            setFieldValue,
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            handleSubmit,
          }) => (
            <form onSubmit={handleSubmit}>
              <div className="modal-body">
                <div className="col-12 d-flex align-items-center flex-column justify-content-center  mb-4">
                  <div className="image-upload">
                    <input
                      accept="image/*"
                      type="file"
                      className="form-control"
                      id="avatar"
                      name="avatar"
                      style={{ display: "none" }}
                      onChange={(event) => {
                        setFieldValue("avatar", event.currentTarget.files[0]);
                        const fileReader = new FileReader();
                        fileReader.onload = () => {
                          if (fileReader.readyState === 2) {
                            setAvatarPreview(fileReader.result);
                          }
                        };
                        fileReader.readAsDataURL(event.target.files[0]);
                      }}
                      onBlur={handleBlur}
                    />
                    <label htmlFor="avatar">
                      <img
                        src={avatarPreview}
                        className="blur-up lazyload update_img"
                        alt=""
                      />
                      <strong>Changer l'avatar</strong>
                    </label>
                  </div>
                  <span className="error-form">
                    {errors.avatar && touched.avatar && errors.avatar}
                  </span>
                </div>
                <div className="row g-4">
                  <div className="col-12 col-md-6">
                    <div className="form-floating theme-form-floating">
                      <input
                        type="text"
                        className="form-control"
                        id="fname"
                        name="fname"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.fname}
                      />
                      <label htmlFor="fname">Nom</label>
                    </div>
                    <span className="error-form">
                      {errors.fname && touched.fname && errors.fname}
                    </span>
                  </div>
                  <div className="col-12 col-md-6">
                    <div className="form-floating theme-form-floating">
                      <input
                        type="text"
                        className="form-control"
                        id="lname"
                        name="lname"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.lname}
                      />
                      <label htmlFor="lname">Prénom</label>
                    </div>
                    <span className="error-form">
                      {errors.lname && touched.lname && errors.lname}
                    </span>
                  </div>
                  <div className="col-12 col-md-6">
                    <div className="form-floating theme-form-floating">
                      <input
                        type="email"
                        className="form-control"
                        id="email"
                        name="email"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.email}
                      />
                      <label htmlFor="email">Adresse mail</label>
                    </div>
                    <span className="error-form">
                      {errors.email && touched.email && errors.email}
                    </span>
                  </div>
                  <div className="col-12 col-md-6">
                    <div className="form-floating theme-form-floating group-mobile">
                      <input
                        type="text"
                        className="form-control"
                        id="mobile"
                        name="mobile"
                        onChange={handleChange}
                        onBlur={handleBlur}
                        value={values.mobile}
                      />
                      <label htmlFor="mobile">Téléphone</label>
                      <span>+212-</span>
                    </div>
                    <span className="error-form">
                      {errors.mobile && touched.mobile && errors.mobile}
                    </span>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  disabled={isLoading}
                  className="btn theme-bg-color btn-md fw-bold text-light"
                  type="submit"
                >
                  {isLoading ? (
                    <TailSpin
                      type="ThreeDots"
                      color="#fff"
                      height={20}
                      width={20}
                      visible={isLoading}
                    />
                  ) : (
                    "Enregistrer"
                  )}
                </button>
              </div>
            </form>
          )}
        </Formik>
      )}
    </>
  );
}
