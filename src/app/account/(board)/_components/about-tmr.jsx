import useOnClickOutside from "@/hooks/useOnClickOutside";
import { useRef } from "react";

export default function AboutTMR({ isOpen, onClose, code }) {
  const modalRef = useRef();
  useOnClickOutside(modalRef, onClose);

  return (
    <div>
      {isOpen && <div className={`modal-backdrop fade show pe-auto`}></div>}
      <div
        className={`modal fade theme-modal ${isOpen ? "show d-block" : "d-none"}`}
        aria-labelledby="inviation-code"
        aria-modal={isOpen}
        aria-hidden={!isOpen}
      >
        <div className="modal-dialog mt-5">
          <div
            ref={modalRef}
            className="modal-content"
            style={{ minHeight: "0" }}
          >
            <div className="modal-header">
              <h5 className="modal-title" id="inviation-code">
                Découvrir notre système de fidélité.
              </h5>
              <button type="button" className="btn-close" onClick={onClose}>
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
            <div className="p-3">
              <div className="mb-3">
                <h3 className="mb-1">Accumulation de TMR</h3>
                <ul style={{ paddingLeft: "10px" }} className="list-dots">
                  <li>
                    <strong>Invitez un ami: </strong>
                    Recevez des TMR en invitant des amis à utiliser votre code
                    personnel. De plus, vous gagnez 10 crédits pour chaque
                    commande passée par l'un de vos amis invités sur notre
                    plateforme. Plus vous invitez, plus vous gagnez !
                  </li>
                  <li>
                    <strong>Soumettez un blog: </strong>
                    Partagez vos idées sur notre plateforme en soumettant un
                    blog. Recevez des TMR une fois que votre blog est accepté
                    par notre équipe.
                  </li>
                  <li>
                    <strong>Passez une commande: </strong>
                    Gagnez des TMR à chaque achat par rapport au montant.
                  </li>
                </ul>
              </div>
              <div className="mb-3">
                <h3 className="mb-1">Réclamation des TMR Accumulés</h3>
                <ul style={{ paddingLeft: "10px" }}>
                  <li>
                    <strong>Convertir vos TMR: </strong>
                    Lorsque vous accumulez les TMR requis, vous pouvez les
                    convertir en un budget équivalent. Ce montant sera
                    automatiquement transféré dans votre Ce montant sera
                    automatiquement transféré dans votre EcoWallet.
                  </li>
                  <li>
                    <strong>Comment réclamer: </strong>
                    Une fois que vous avez accumulé le seuil nécessaire, un
                    bouton de réclamation apparaîtra dans votre compte. Cliquez
                    sur ce bouton pour valider la conversion de vos TMR.
                  </li>
                </ul>
              </div>
              <div className="mb-3">
                <h3 className="mb-1">Utilisation de l'EcoWallet</h3>
                <ul style={{ paddingLeft: "10px" }}>
                  <li>
                    <strong>Payer avec votre EcoWallet: </strong>
                    Utilisez les fonds de votre EcoWallet pour régler vos
                    commandes en totalité ou pour réduire le prix de vos achats.
                    Vous pouvez ainsi payer moins ou même rien du tout !
                  </li>
                  <li>
                    <strong>Comment réclamer: </strong>
                    Une fois que vous avez accumulé le seuil nécessaire, un
                    bouton de réclamation apparaîtra dans votre compte. Cliquez
                    sur ce bouton pour valider la conversion de vos TMR.
                  </li>
                </ul>
              </div>
              <div className="mb-3">
                <h3 className="mb-1">Suivi de vos Crédits</h3>
                <ul style={{ paddingLeft: "10px" }}>
                  <li>
                    <strong>Vérifiez votre solde: </strong>
                    Consultez régulièrement votre solde de crédits et le montant
                    disponible dans votre EcoWallet depuis votre compte pour
                    suivre vos gains et vos dépenses.
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
