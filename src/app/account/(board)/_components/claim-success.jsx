import useOnClickOutside from "@/hooks/useOnClickOutside";
import { useRef } from "react";
import ReactConfetti from "react-confetti";

export default function ClaimSuccess({ isOpen, onClose, claimed }) {
  const modalRef = useRef();
  useOnClickOutside(modalRef, onClose);

  return (
    <div>
      {isOpen && <div className={`modal-backdrop fade show pe-auto`}></div>}
      <div
        className={`modal fade theme-modal ${isOpen ? "show d-block" : "d-none"}`}
        aria-labelledby="inviation-code"
        aria-modal={isOpen}
        aria-hidden={!isOpen}
      >
        <ReactConfetti
          width={window.screen.width}
          height={window.screen.height}
        />
        <div className="modal-dialog mt-5">
          <div
            ref={modalRef}
            className="modal-content"
            style={{ minHeight: "0" }}
          >
            <div className="modal-header">
              <h5 className="modal-title" id="inviation-code">
                Félicitations ! 🎉
              </h5>
              <button type="button" className="btn-close" onClick={onClose}>
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
            <div className="p-3">
              <h2 className="text-center fs-4 mb-3 w-fit">+ {claimed} DH</h2>
              <p>
                Félicitations ! Vous avez réclamé vos TMR et ils ont été
                crédités sur votre solde. Vous pouvez maintenant les utiliser
                pour profiter de nos offres et services sur la plateforme. Merci
                pour votre fidélité !
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
