import useOnClickOutside from "@/hooks/useOnClickOutside";
import { useSettingsContext } from "@/providers/settings-provider";
import CopyToClipboardField from "@/shared/components/copy-to-clipboard-field";
import { useRef } from "react";

export default function InvitationCode({ isOpen, onClose, code }) {
  const settings = useSettingsContext();
  const modalRef = useRef();
  useOnClickOutside(modalRef, onClose);

  return (
    <div>
      {isOpen && <div className={`modal-backdrop fade show pe-auto`}></div>}
      <div
        className={`modal fade theme-modal ${isOpen ? "show d-block" : "d-none"}`}
        aria-labelledby="inviation-code"
        aria-modal={isOpen}
        aria-hidden={!isOpen}
      >
        <div className="modal-dialog mt-5">
          <div
            ref={modalRef}
            className="modal-content"
            style={{ minHeight: "0" }}
          >
            <div className="modal-header">
              <h5 className="modal-title" id="inviation-code">
                Partager votre code
              </h5>
              <button type="button" className="btn-close" onClick={onClose}>
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
            <div className="p-3">
              <div className="share-invitation-code">
                <img
                  src="/assets/images/tanmirt-logo.png"
                  alt="Code Invitation"
                />
                <h6 className="my-3 text-center fs-6 fw-bold">
                  {settings["credits_per_invitation"]} TMR pour chaque
                  invitation acceptée
                </h6>
                <CopyToClipboardField value={code} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
