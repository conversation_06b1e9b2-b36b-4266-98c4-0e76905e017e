"use client";

import useOnClickOutside from "@/hooks/useOnClickOutside";
import { useRouter } from "next/navigation";
import { useContext, useRef } from "react";
import { BookOpen, ShoppingCart, UserPlus } from "react-feather";
import { useSettingsContext } from "@/providers/settings-provider";

export default function BoostCredits({ isOpen, onClose, openInvitationCode }) {
  const settings = useSettingsContext();
  const router = useRouter();
  const modalRef = useRef();
  useOnClickOutside(modalRef, onClose);

  const options = [
    {
      icon: ShoppingCart,
      title: "Passer une commande",
      description: `${settings["credits_per_order"]} TMR pour chaque 10 MAD dépensés`,
      action: () => {
        router.push("/checkout");
      },
    },
    {
      icon: BookOpen,
      title: "Rédiger un blog",
      description: `${settings["credits_per_blog"]} TMR pour chaque blog rédigé`,
      action: () => {
        router.push("/blogs/submit");
      },
    },
    {
      icon: UserPlus,
      title: "Inviter un(e) ami(e)",
      description: `${settings["credits_per_invitation"]} TMR pour chaque invitation acceptée`,
      action: () => {
        onClose();
        openInvitationCode();
      },
    },
  ];

  return (
    <div>
      {isOpen && <div className={`modal-backdrop fade show pe-auto`}></div>}
      <div
        className={`modal fade theme-modal ${isOpen ? "show d-block" : "d-none"}`}
        aria-labelledby="boost-credits"
        aria-modal={isOpen}
        aria-hidden={!isOpen}
      >
        <div className="modal-dialog modal-xl mt-5">
          <div
            ref={modalRef}
            className="modal-content"
            style={{ minHeight: "0" }}
          >
            <div className="modal-header">
              <h5 className="modal-title" id="boost-credits">
                Booster
              </h5>
              <button type="button" className="btn-close" onClick={onClose}>
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
            <div className="p-3 pb-5">
              <div className="boost-credits-methods row">
                {options.map((option, key) => (
                  <div key={key} className="boost-credits-item col-lg">
                    <div className="boost-credits-content shadow">
                      <div className="boost-credits-method-icon">
                        <option.icon />
                      </div>
                      <h5>{option.title}</h5>
                      <p>{option.description}</p>
                    </div>
                    <button onClick={option.action}>Allons-y</button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
