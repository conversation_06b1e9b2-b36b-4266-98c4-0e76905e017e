"use client";

import { useAuthContext } from "@/providers/auth-provider";
import { useSettingsContext } from "@/providers/settings-provider";
import { useClaimCreditsMutation } from "@/services/customer";
import Modal from "@/shared/components/modal";
import { useState } from "react";
import { BookOpen, Edit2, ShoppingCart, Star } from "react-feather";
import AboutTMR from "./_components/about-tmr";
import BoostCredits from "./_components/boost-credits";
import ClaimSuccess from "./_components/claim-success";
import InvitationCode from "./_components/invitation-code";
import UpdateProfileForm from "./_components/update-profile-form";

export default function Dashboard() {
  const { currentUser } = useAuthContext();
  const [boostCreditsOpen, setBoostCreditsOpen] = useState(false);
  const [invitationCodeOpen, setInvitationCodeOpen] = useState(false);
  const [claimSuccessOpen, setClaimSuccessOpen] = useState(false);
  const [aboutOpen, setAboutOpen] = useState(false);
  const [updateProfileOpen, setUpdateProfileOpen] = useState(false);
  const [claimed, setClaimed] = useState(null);

  const { claimCredits, isLoading } = useClaimCreditsMutation();

  const settings = useSettingsContext();

  const stats = currentUser?.stats;

  return (
    <div className="dashboard-home">
      <div className="customer-statistics mb-4">
        <div className="customer-statistics-item">
          <div>
            <h4>TMR accumulés</h4>
            <div className="customer-statistics-value">
              <p>{stats?.credits}</p>
            </div>
          </div>
          <div>
            <Star size={30} />
          </div>
        </div>
        <div className="customer-statistics-item">
          <div>
            <h4>Blogs rédigés</h4>
            <div className="customer-statistics-value">
              <p>{stats?.blogs_count}</p>
            </div>
          </div>
          <div>
            <BookOpen size={30} />
          </div>
        </div>
        <div className="customer-statistics-item">
          <div>
            <h4>Articles achetés</h4>
            <div className="customer-statistics-value">
              <p>{stats?.orders_count}</p>
            </div>
          </div>
          <div>
            <ShoppingCart size={30} />
          </div>
        </div>
      </div>
      <div className="customer-progress">
        <div className="customer-card">
          <button
            className="edit-profile-button"
            onClick={() => setUpdateProfileOpen(true)}
          >
            <Edit2 size={14} />
          </button>

          <small>Content de vous revoir,</small>
          <h2>{currentUser?.full_name}</h2>
          <div className="mt-2 customer-card-info">
            <small>{currentUser?.email}</small>
            {currentUser?.nickname && (
              <small className="text-content">
                Pour rester anonyme, votre nom sur Ecowatt Eshop sera,{" "}
                <strong>{currentUser?.nickname}</strong>
              </small>
            )}
          </div>
        </div>

        <Modal
          title="Modifier le profil"
          open={updateProfileOpen}
          onClose={() => setUpdateProfileOpen(false)}
        >
          <UpdateProfileForm />
        </Modal>

        <div className="customer-stat-cards">
          <div className="customer-category-card position-relative">
            <div>
              <h5 className="mb-3">Proche de la récompense</h5>
              <button
                className="about-tanmirts"
                onClick={() => setAboutOpen(true)}
              >
                !
              </button>
            </div>
            {stats?.credits >= settings?.credits_target ? (
              <div className="flex-grow-1 w-100">
                <h5 className="mb-2">Félicitations ! 🎉</h5>
                <p className="text-white">
                  Vous avez accumulé suffisamment de TMR pour réclamer une somme
                  à dépenser lors de votre prochain achat. Merci d'être un
                  client précieux, et profitez-en !
                </p>
                <button
                  className="rounded w-100"
                  onClick={() => {
                    claimCredits(null, {
                      onSuccess(data) {
                        setClaimSuccessOpen(true);
                        setClaimed(data.claim);
                      },
                    });
                  }}
                >
                  {isLoading ? "Réclamer..." : "Réclamer"}
                </button>
              </div>
            ) : (
              <>
                <div className="mb-3 w-100">
                  <div className="d-flex w-100 align-items-center justify-content-stretch flex-column">
                    <div className="multigraph">
                      <svg
                        width="40"
                        height="40"
                        viewBox="0 0 49 48"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <circle
                          cx="24.1304"
                          cy="23.9999"
                          r="24"
                          fill="#E83741"
                        />
                        <g clipPath="url(#clip0_1028_911)">
                          <path
                            d="M24.1428 11.4999C17.2428 11.4999 11.6553 17.0999 11.6553 23.9999C11.6553 30.8999 17.2428 36.4999 24.1428 36.4999C31.0553 36.4999 36.6553 30.8999 36.6553 23.9999C36.6553 17.0999 31.0553 11.4999 24.1428 11.4999ZM24.1553 33.9999C18.6303 33.9999 14.1553 29.5249 14.1553 23.9999C14.1553 18.4749 18.6303 13.9999 24.1553 13.9999C29.6803 13.9999 34.1553 18.4749 34.1553 23.9999C34.1553 29.5249 29.6803 33.9999 24.1553 33.9999ZM28.5303 22.7499C29.5678 22.7499 30.4053 21.9124 30.4053 20.8749C30.4053 19.8374 29.5678 18.9999 28.5303 18.9999C27.4928 18.9999 26.6553 19.8374 26.6553 20.8749C26.6553 21.9124 27.4928 22.7499 28.5303 22.7499ZM19.7803 22.7499C20.8178 22.7499 21.6553 21.9124 21.6553 20.8749C21.6553 19.8374 20.8178 18.9999 19.7803 18.9999C18.7428 18.9999 17.9053 19.8374 17.9053 20.8749C17.9053 21.9124 18.7428 22.7499 19.7803 22.7499ZM24.1553 30.8749C26.6928 30.8749 28.9053 29.4874 30.0928 27.4374C30.3303 27.0249 30.0303 26.4999 29.5428 26.4999H18.7678C18.2928 26.4999 17.9803 27.0249 18.2178 27.4374C19.4053 29.4874 21.6178 30.8749 24.1553 30.8749Z"
                            fill="white"
                          />
                        </g>
                        <defs>
                          <clipPath id="clip0_1028_911">
                            <rect
                              width="30"
                              height="30"
                              fill="white"
                              transform="translate(9.13037 8.99988)"
                            />
                          </clipPath>
                        </defs>
                      </svg>
                      <span className="graph"></span>
                      <span
                        className="graph-percentage"
                        style={{
                          transform: `rotate(${(stats?.credits_progress ?? 0) * 1.8}deg)`,
                        }}
                      ></span>
                    </div>
                    <div className="customer-category-stats w-100">
                      <div className="customer-category-stats-min">
                        <small>0</small>
                      </div>
                      <div className="customer-category-stats-score">
                        <h5>{stats?.credits ?? 0}</h5>
                        <span>TMR</span>
                      </div>
                      <div className="customer-category-stats-max">
                        <small>{settings?.credits_target}</small>
                      </div>
                    </div>
                  </div>
                </div>
                <button onClick={() => setBoostCreditsOpen(true)}>
                  Booster
                </button>
              </>
            )}
          </div>

          <div className="customer-credits-card">
            <h5 className="mb-3">Références</h5>
            <div className="w-100 flex-grow-1 d-flex flex-column">
              <div className="customer-invitation-stat">
                <small>Vous avez invité</small>
                <h6>{stats?.referrer_count ?? 0} ami(e)s</h6>
              </div>
              <div className="customer-invitation-stat">
                <small>Bonus</small>
                <h6>{stats?.referrer_gain ?? 0}</h6>
              </div>
            </div>
            <button onClick={() => setInvitationCodeOpen(true)}>Inviter</button>
          </div>
        </div>
        {boostCreditsOpen && (
          <BoostCredits
            isOpen={boostCreditsOpen}
            onClose={() => setBoostCreditsOpen(false)}
            openInvitationCode={() => setInvitationCodeOpen(true)}
          />
        )}
        {currentUser?.code && invitationCodeOpen && (
          <InvitationCode
            code={currentUser?.code}
            isOpen={invitationCodeOpen}
            onClose={() => setInvitationCodeOpen(false)}
          />
        )}
        {claimSuccessOpen && claimed && (
          <ClaimSuccess
            isOpen={claimSuccessOpen}
            onClose={() => setClaimSuccessOpen(false)}
            claimed={claimed}
          />
        )}
        {aboutOpen && (
          <AboutTMR isOpen={aboutOpen} onClose={() => setAboutOpen(false)} />
        )}
      </div>
    </div>
  );
}
