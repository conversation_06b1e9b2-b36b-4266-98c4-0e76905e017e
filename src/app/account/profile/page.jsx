"use client";

import Modal from "@/shared/components/modal";
import Link from "next/link";
import { useState } from "react";
import { Eye, Mail } from "react-feather";
import UpdateProfileForm from "../(board)/_components/update-profile-form";
import UpdatePasswordForm from "./_components/update-password-form";
import { useAuthContext } from "@/providers/auth-provider";

export default function Profile() {
  const { currentUser, authenticated } = useAuthContext();
  const [updateProfileOpen, setUpdateProfileOpen] = useState(false);
  const [updatePasswordOpen, setUpdatePasswordOpen] = useState(false);

  return (
    <div className="dashboard-profile">
      <div className="title">
        <h2>Mon Profil</h2>
        <span className="title-leaf">
          <img
            src="/assets/svg/leaf.png"
            alt="leaf"
            className="icon-width bg-gray"
          />
        </span>
      </div>

      <div className="profile-detail dashboard-bg-box">
        <div className="dashboard-title">
          <h3>Nom de profil</h3>
        </div>

        {authenticated &&
        currentUser?.type === "seller" &&
        currentUser?.status === 1 ? (
          <div
            className="btn btn-animation proceed-btn fw-bold mb-3"
            style={{ cursor: "default" }}
          >
            Votre Compte n'est pas encore validé.
            <br />
            Vous ne pouvez pas encore faire des commandes.
          </div>
        ) : null}
        <div className="profile-name-detail">
          <div className="d-sm-flex align-items-center d-block">
            <h3>{currentUser?.full_name}</h3>
          </div>
          <button type="button" onClick={() => setUpdateProfileOpen(true)}>
            Modifier
          </button>
        </div>

        <div className="location-profile">
          <ul>
            {currentUser?.email && (
              <li>
                <div className="location-box">
                  <Mail />
                  <h6>{currentUser?.email}</h6>
                </div>
              </li>
            )}

            <li>
              <div className="location-box">
                <i data-feather="check-square"></i>
                <h6>
                  {currentUser.type === "professional"
                    ? "Professionnel"
                    : currentUser.type === "seller"
                      ? "Privilégié"
                      : "Particulier"}
                </h6>
              </div>
            </li>

            {(currentUser.type === "professional" ||
              currentUser.type === "seller") && (
              <li>
                <div className="location-box">
                  <i data-feather="map-pin"></i>
                  <h6>
                    {currentUser?.status === 1
                      ? "Non activé"
                      : currentUser?.status === 2
                        ? "Approuvé"
                        : ""}
                  </h6>
                </div>
              </li>
            )}
          </ul>
        </div>
      </div>

      <Modal
        title="Modifier le profil"
        open={updateProfileOpen}
        onClose={() => setUpdateProfileOpen(false)}
      >
        <UpdateProfileForm />
      </Modal>

      <Modal
        title="Changer le mot de pass"
        open={updatePasswordOpen}
        onClose={() => setUpdatePasswordOpen(false)}
      >
        <UpdatePasswordForm />
      </Modal>

      <div className="profile-about dashboard-bg-box">
        <div className="row">
          <div className="col-xxl-7">
            <div className="dashboard-title mb-3">
              <h3>Profil À propos</h3>
            </div>

            <div className="table-responsive">
              <table className="table">
                <tbody>
                  {currentUser?.mobile && (
                    <tr>
                      <td>Numéro de téléphone:</td>
                      <td>
                        <Link href={`tel:${currentUser?.mobile}`}>
                          +212-{currentUser?.mobile ?? "-"}
                        </Link>
                      </td>
                    </tr>
                  )}
                  {(currentUser.type === "professional" ||
                    currentUser.type === "seller") && (
                    <tr>
                      <td>
                        {currentUser.type === "seller" ? "Raison sociale" : "RC"} :
                      </td>
                      <td>{currentUser?.rc}</td>
                    </tr>
                  )}
                  {(currentUser.type === "professional" ||
                    currentUser.type === "seller") && (
                    <tr>
                      <td>ICE :</td>
                      <td>{currentUser?.ice}</td>
                    </tr>
                  )}
                  {currentUser.type === "seller" && (
                    <tr>
                      <td>RC (PDF) :</td>
                      <td>
                        <a
                          rel="noreferrer"
                          href={currentUser?.full_rc_file}
                          target="_blank"
                        >
                          <Eye />
                        </a>
                      </td>
                    </tr>
                  )}
                  {/* {(currentUser.type === 'seller') &&
                                        <tr>
                                            <td>ICE (PDF) :</td>
                                            <td>
                                                <a rel="noreferrer" href={currentUser?.full_ice_file} target="_blank">
                                                    <Eye />
                                                </a>
                                            </td>
                                        </tr>
                                    } */}
                  {currentUser.type === "seller" && (
                    <tr>
                      <td>CIN (PDF) :</td>
                      <td>
                        <a
                          rel="noreferrer"
                          href={currentUser?.full_cin_file}
                          target="_blank"
                        >
                          <Eye />
                        </a>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            <div className="dashboard-title mb-3">
              <h3>Détails de connexion</h3>
            </div>

            <div className="table-responsive">
              <table className="table">
                <tbody>
                  {currentUser?.email && (
                    <tr>
                      <td>Email :</td>
                      <td>{currentUser?.email}</td>
                    </tr>
                  )}
                  <tr>
                    <td>Mot de passe :</td>
                    <td>
                      ●●●●●● &nbsp;&nbsp;
                      <button
                        type="button"
                        onClick={() => setUpdatePasswordOpen(true)}
                      >
                        Modifier
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div className="col-xxl-5">
            <div className="profile-image">
              <img
                src="/assets/images/reset-password.svg"
                className="img-fluid blur-up lazyload"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
