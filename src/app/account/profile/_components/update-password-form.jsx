"use client";

import { useUpdatePasswordMutation } from "@/services/customer";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessAnimation from "@/shared/components/success-animation";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { Formik } from "formik";
import { Tail<PERSON>pin } from "react-loader-spinner";
import { object, ref, string } from "yup";

const formSchema = object({
  old_password: string().required("Ce champ est obligatoire"),
  password: string()
    .required("Ce champ est obligatoire")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!+@#_\$%\^&\*])(?=.{8,})/,
      "Doit contenir 8 caractères, une majuscule, une minuscule, un chiffre et une casse spéciale"
    ),
  password_confirmation: string()
    .required("Ce champ est obligatoire")
    .oneOf([ref("password"), null], "Les mots de passe ne correspondent pas."),
});

const genInitialValues = () => ({
  old_password: "",
  password: "",
  password_confirmation: "",
});

export default function UpdatePasswordForm() {
  const { updatePassword, isLoading, isSuccess, error } =
    useUpdatePasswordMutation();

  async function onSubmit(data, actions) {
    updatePassword(data, {
      onSuccess() {
        actions.resetForm({
          values: genInitialValues(),
        });
      },
    });
  }

  if (isSuccess) {
    return (
      <>
        <SuccessSnackbar message={`Mot de passe mis à jour avec succès`} />
        <SuccessAnimation />
      </>
    );
  }

  return (
    <>
      {error && <ErrorSnackbar message={error.message} />}
      <Formik
        initialValues={genInitialValues()}
        validationSchema={formSchema}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
        }) => (
          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              <div className="row g-4">
                <div className="col-12">
                  <div className="form-floating theme-form-floating">
                    <input
                      type="password"
                      className="form-control"
                      id="old_password"
                      name="old_password"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.old_password}
                    />
                    <label htmlFor="old_password">Ancien mot de passe</label>
                  </div>
                  <span className="error-form">
                    {errors.old_password &&
                      touched.old_password &&
                      errors.old_password}
                  </span>
                </div>
                <div className="col-12 col-xxl-6">
                  <div className="form-floating theme-form-floating">
                    <input
                      type="password"
                      className="form-control"
                      id="password"
                      name="password"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.password}
                    />
                    <label htmlFor="password">Mot de passe</label>
                  </div>
                  <span className="error-form">
                    {errors.password && touched.password && errors.password}
                  </span>
                </div>
                <div className="col-12 col-xxl-6">
                  <div className="form-floating theme-form-floating">
                    <input
                      type="password"
                      className="form-control"
                      id="password_confirmation"
                      name="password_confirmation"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      value={values.password_confirmation}
                    />
                    <label htmlFor="password_confirmation">
                      Confirmation mot de passe
                    </label>
                  </div>
                  <span className="error-form">
                    {errors.password_confirmation &&
                      touched.password_confirmation &&
                      errors.password_confirmation}
                  </span>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button
                disabled={isLoading}
                className="btn theme-bg-color btn-md fw-bold text-light"
                type="submit"
              >
                {isLoading ? (
                  <TailSpin
                    type="ThreeDots"
                    color="#fff"
                    height={20}
                    width={20}
                  />
                ) : (
                  "Enregistrer"
                )}
              </button>
            </div>
          </form>
        )}
      </Formik>
    </>
  );
}
