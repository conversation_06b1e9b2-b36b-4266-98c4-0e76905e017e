"use client";

import ProductBox from "@/components/product-box";
import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";
import { useContext } from "react";
import { InfinitySpin } from "react-loader-spinner";

export default function MyWishlist() {
  const { wishlistItems, wishlistItemsLoading } = useContext(
    CartAndWishlistProvider
  );

  return (
    <div className="dashboard-wishlist">
      <div className="title">
        <h2>Favoris</h2>
        <span className="title-leaf title-leaf-gray">
          <img
            src="/assets/svg/leaf.png"
            alt="leaf"
            className="icon-width bg-gray"
          />
        </span>
      </div>
      <div>
        {wishlistItemsLoading ? (
          <div className="min-vh-100 px-4 py-2 d-flex align-items-center justify-content-center">
            <InfinitySpin
              type="ThreeDots"
              color="#2A3466"
              height={220}
              width={220}
            />
          </div>
        ) : wishlistItems && wishlistItems.length ? (
          <div className="row g-3 row-cols-2 row-cols-sm-3 row-cols-md-4 row-cols-lg-3 row-cols-xxl-4 product-list-section">
            {wishlistItems.map((item, key) => (
              <div key={`wishlist-${key}`}>
                <ProductBox product={item} isWishlist={true} />
              </div>
            ))}
          </div>
        ) : (
          <h2 className="text-center my-5">Aucun produit trouvé</h2>
        )}
      </div>
    </div>
  );
}
