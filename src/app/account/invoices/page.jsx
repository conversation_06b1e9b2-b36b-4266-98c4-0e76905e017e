"use client";

import { But<PERSON> } from "@/components/ui/button";
import { formatDate } from "@/lib/helpers";
import {
  useDownloadInvoiceDocumentMutation,
  useInvoices,
} from "@/services/customer";
import { MoveLeftIcon, MoveRightIcon } from "lucide-react";
import { parseAsIndex, useQueryState } from "nuqs";
import { InfinitySpin, TailSpin } from "react-loader-spinner";

export default function Invoices() {
  const [page, setPage] = useQueryState("page", parseAsIndex.withDefault(0));
  const [code, setCode] = useQueryState("code", { defaultValue: "" });
  const [date, setDate] = useQueryState("date", { defaultValue: "" });

  const { invoices, isLoading } = useInvoices({ v2: true, page, code, date });

  return (
    <div className="dashboard-order">
      <div className="title">
        <h2>Historique de mes factures</h2>
        <span className="title-leaf title-leaf-gray">
          <img
            src="/assets/svg/leaf.png"
            alt="leaf"
            className="icon-width bg-gray"
          />
        </span>
      </div>

      <div className="filter">
        <div className="form-group">
          <label>Code</label>
          <input
            type="text"
            onChange={(event) => setCode(event.target.value)}
            className="form-control"
            value={code}
          />
        </div>
        <div className="form-group">
          <label>Date</label>
          <input
            type="date"
            onChange={(event) => setDate(event.target.value)}
            className="form-control"
            value={date}
          />
        </div>
      </div>

      {isLoading && (
        <div className="px-4 py-2 d-flex align-items-center justify-content-center">
          <InfinitySpin
            type="ThreeDots"
            color="#2A3466"
            height={220}
            width={220}
          />
        </div>
      )}

      {!isLoading && !invoices?.data?.length > 0 && (
        <h2 className="text-center my-5">Aucune factures trouvée</h2>
      )}

      {!isLoading && invoices?.data?.length > 0 && (
        <>
          <div className="order-contain">
            {invoices?.data?.map((item) => (
              <InvoiceRow key={item.id} invoice={item} />
            ))}
          </div>
          <div className="w-full mt-5 flex items-center gap-2 justify-center">
            <Button
              onClick={() => setPage((p) => p - 1)}
              disabled={isLoading || page < 1}
            >
              <MoveLeftIcon /> Précédent
            </Button>
            <Button
              onClick={() => setPage((p) => p + 1)}
              disabled={isLoading || !invoices?.has_more}
            >
              <MoveRightIcon /> Suivant
            </Button>
          </div>
        </>
      )}
    </div>
  );
}

function InvoiceRow({ invoice }) {
  const { downloadInvoiceDocument, isLoading } =
    useDownloadInvoiceDocumentMutation();

  const downloadFile = async (file) => {
    downloadInvoiceDocument(
      { file },
      {
        onSuccess(data) {
          if (data) {
            window.open(data, "_blank");
          }
        },
      }
    );
  };

  return (
    <div className="order-box dashboard-bg-box">
      <div className="order-container">
        <div className="order-detail">
          <h4>
            Ref
            <span>{invoice?.ref}</span>
          </h4>
        </div>
        <div className="order-detail">
          <h4>
            La date <span>{formatDate(invoice?.date_creation)}</span>
          </h4>
        </div>
        <div className="order-detail">
          <h4>
            Total{" "}
            <span>{`${Math.round(invoice?.total_ttc * 10) / 10} dhs`}</span>
          </h4>
        </div>
        <div className="order-detail">
          <button
            type="button"
            className="btn download-btn"
            onClick={() => downloadFile(invoice?.last_main_doc)}
            disabled={isLoading}
          >
            Télécharger
            {isLoading ? <TailSpin color="#fff" height={16} width={16} /> : ""}
          </button>
        </div>
      </div>
    </div>
  );
}
