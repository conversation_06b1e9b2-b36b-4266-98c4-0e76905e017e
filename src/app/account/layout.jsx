"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  File,
  Heart,
  HelpCircle,
  Home,
  MapPin,
  Package,
  ShoppingBag,
  User,
} from "react-feather";
import { useSettingsContext } from "@/providers/settings-provider";
import { useAuthContext } from "@/providers/auth-provider";

export default function LayoutAccount({ children }) {
  const router = useRouter();
  const pathname = usePathname();

  const settings = useSettingsContext();

  const { currentUser, authenticated } = useAuthContext();

  const [menu, setMenu] = useState(false);

  const toggleMenu = (status) => {
    setMenu(status);
  };

  const active = pathname?.split("/")[2];

  useEffect(() => {
    setMenu(false);
  }, [pathname]);

  useEffect(() => {
    if (!authenticated) {
      router.replace("/login");
    }
  }, [authenticated]);

  if(!authenticated) {
    return null;
  }

  return (
    <section className="user-dashboard-section section-b-space">
      {authenticated &&
        currentUser &&
        currentUser?.type === "seller" &&
        currentUser?.status != 2 && (
          <div
            className="btn row btn-animation proceed-btn fw-bold mb-3"
            style={{ cursor: "default" }}
          >
            Votre Compte n'est pas encore validé.
            <br />
            Vous ne pouvez pas encore faire des commandes.
          </div>
        )}
      <div className="container-lg">
        <div className="row">
          <div className="col-xxl-3 col-lg-4">
            <div className={`dashboard-left-sidebar ${menu ? "show" : ""}`}>
              <div className="close-button d-flex d-lg-none">
                <button
                  onClick={() => toggleMenu(false)}
                  className={`close-sidebar`}
                >
                  <i className="fa-solid fa-xmark"></i>
                </button>
              </div>
              <div className="profile-box">
                <div className="cover-image">
                  <img
                    src="/assets/images/profile-banner.jpeg"
                    className="img-fluid blur-up lazyload"
                    alt=""
                  />
                </div>

                <div className="profile-contain">
                  <div className="profile-image">
                    <div className="position-relative">
                      <img
                        src={currentUser?.full_avatar}
                        className="blur-up lazyload update_img"
                        alt=""
                      />
                    </div>
                  </div>

                  <div className="profile-name">
                    <h3>{currentUser?.full_name}</h3>
                    {currentUser?.email && (
                      <h6 className="text-content">{currentUser?.email}</h6>
                    )}
                  </div>
                </div>
              </div>

              <ul className="nav nav-pills user-nav-pills">
                <li className="nav-item">
                  <Link
                    className={`nav-link ${active === undefined && "active"}`}
                    href={`/account`}
                  >
                    <Home />
                    Tableau de bord
                  </Link>
                </li>

                <li className="nav-item">
                  <Link
                    className={`nav-link ${active === "orders" && "active"}`}
                    href={`/account/orders`}
                  >
                    <ShoppingBag />
                    Commandes
                  </Link>
                </li>

                {["individual", "professional", "seller"].includes(
                  currentUser?.type
                ) ? (
                  <>
                    <li className="nav-item">
                      <Link
                        className={`nav-link ${active === "invoices" && "active"}`}
                        href={`/account/invoices`}
                      >
                        <File />
                        Factures
                      </Link>
                    </li>

                    <li className="nav-item">
                      <Link
                        className={`nav-link ${active === "shippings" && "active"}`}
                        href={`/account/shippings`}
                      >
                        <Package />
                        Bon de livraison
                      </Link>
                    </li>
                  </>
                ) : (
                  <div />
                )}

                <li className="nav-item">
                  <Link
                    className={`nav-link ${active === "wishlist" && "active"}`}
                    href={`/account/wishlist`}
                  >
                    <Heart />
                    Favoris
                  </Link>
                </li>

                <li className="nav-item">
                  <Link
                    className={`nav-link ${active === "addresses" && "active"}`}
                    href={`/account/addresses`}
                  >
                    <MapPin />
                    Address
                  </Link>
                </li>

                <li className="nav-item">
                  <Link
                    className={`nav-link ${active === "profile" && "active"}`}
                    href={`/account/profile`}
                  >
                    <User />
                    Profile
                  </Link>
                </li>

                {/* <li className="nav-item">
                  <button 
                    className={`nav-link ${type === 'privacy' && 'active'}`} 
                    type="button"
                    onClick={() => ChooseType('privacy')}
                  >
                    <i data-feather="user"></i>
                    Privacy
                  </button>
                </li> */}
              </ul>
              <div className="help-panel">
                <div>
                  <div className="help-panel-header">
                    <HelpCircle />
                  </div>
                  <div className="help-panel-body">
                    <h3>Besoin d'aide?</h3>
                    <span>Contactez-nous</span>
                  </div>
                  <div className="help-panel-footer">
                    {settings?.store_phone && (
                      <a href={`tel:${settings?.store_phone}`}>
                        {settings?.store_phone}
                      </a>
                    )}
                    {settings?.store_email && (
                      <a href={`mail:${settings.store_email}`}>
                        {settings.store_email}
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="col-xxl-9 col-lg-8">
            <button
              onClick={() => toggleMenu(true)}
              className="btn left-dashboard-show btn-animation btn-md fw-bold d-block mb-4 d-lg-none"
            >
              Show Menu
            </button>
            <div className="dashboard-right-sidebar">
              <div className="tab-content h-100" id="pills-tabContent">
                {children}
              </div>
            </div>
          </div>

          <div className={`bg-overlay ${menu ? "show" : ""}`}></div>
        </div>
      </div>
    </section>
  );
}
