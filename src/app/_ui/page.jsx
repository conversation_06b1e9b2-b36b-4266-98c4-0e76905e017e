import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetCloseButton,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { BoltIcon, ClockIcon, MoonIcon, SunIcon } from "lucide-react";

export default function UiPage() {
  return (
    <div className="container-lg space-y-4">
      <div className="space-y-4">
        <div>
          <h2>Button</h2>
        </div>
        <div className="p-4 space-x-4 border">
          <Button>
            <BoltIcon />
            CLICK ME!
          </Button>
          <Button variant="brand-secondary">
            <ClockIcon /> CLICK ME!
          </Button>
          <Button variant="brand-ternary">
            <SunIcon /> CLICK ME!
          </Button>
          <Button variant="outline">
            <MoonIcon /> CLICK ME!
          </Button>
          <Button variant="secondary">
            <SunIcon /> CLICK ME!
          </Button>
          <Button variant="ghost">
            <MoonIcon /> CLICK ME!
          </Button>
          <Button variant="link">
            <MoonIcon /> CLICK ME!
          </Button>
          <Button variant="danger">
            <MoonIcon /> CLICK ME!
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h2>Label</h2>
        </div>
        <div className="p-4 space-x-4 border">
          <Label>Label</Label>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h2>Sheet</h2>
        </div>
        <div className="p-4 space-x-4 border">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline">Open</Button>
            </SheetTrigger>
            <SheetContent>
              <SheetCloseButton />
              <SheetHeader>
                <SheetTitle>Sheet</SheetTitle>
              </SheetHeader>
              <SheetDescription>
                <p>Sheet Description</p>
              </SheetDescription>
              <div>
                <p>Sheet Content</p>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h2>Select</h2>
        </div>
        <div className="p-4 space-x-4 border">
          <Select>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Theme" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 50 }).map((_, index) => (
                <SelectItem key={index} value={index}>
                  {index} item
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h2>Dialog</h2>
        </div>
        <div className="p-4 space-x-4 border">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">Open</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Are you absolutely sure?</DialogTitle>
                <DialogDescription>
                  This action cannot be undone. This will permanently delete
                  your account and remove your data from our servers.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline">Cancel</Button>
                <Button variant="danger">Delete</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h2>Card</h2>
        </div>
        <div className="p-4 space-x-4 border">
          <Card>
            <p>
              Lorem ipsum dolor sit, amet consectetur adipisicing elit.
              Consequatur, inventore?
            </p>
          </Card>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h2>Accordion</h2>
        </div>
        <div className="p-4 space-x-4 border">
          <Accordion type="single" collapsible>
            <AccordionItem value="item-1">
              <AccordionTrigger>Is it accessible?</AccordionTrigger>
              <AccordionContent>
                Yes. It adheres to the WAI-ARIA design pattern.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-2">
              <AccordionTrigger>Is it accessible?</AccordionTrigger>
              <AccordionContent>
                Yes. It adheres to the WAI-ARIA design pattern.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-3">
              <AccordionTrigger>Is it accessible?</AccordionTrigger>
              <AccordionContent>
                Yes. It adheres to the WAI-ARIA design pattern.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <h2>Badge</h2>
        </div>
        <div className="p-4 space-x-4 border">
          <Badge>Badge</Badge>
          <Badge variant="outline">Badge</Badge>
          <Badge variant="secondary">Badge</Badge>
          <Badge variant="danger">Badge</Badge>
        </div>
      </div>
    </div>
  );
}
