import { getUrl } from "@/lib/helpers";

/** @return {import('next').MetadataRoute.Robots} */
export default function robots() {
  // disable indexing for staging environment
  if (process.env.ENVIRONMENT === "staging") {
    return {
      rules: {
        userAgent: "*",
        disallow: "/",
      },
    };
  }

  return {
    rules: {
      userAgent: "*",
      allow: "/",
      disallow: "/account/",
    },
    sitemap: getUrl("/sitemap.xml"),
  };
}
