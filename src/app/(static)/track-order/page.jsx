import { Home } from "react-feather";
import Link from "next/link";
import StepItem from "./_components/step-item";

const TrackOrder = () => {
  const steps = [
    {
      title: "Connexion au compte",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <circle cx="7" cy="22" r="2" />
          <circle cx="17" cy="22" r="2" />
          <path d="M22.813,9.583A6,6,0,0,1,12.8,3H4.242L4.2,2.648A3,3,0,0,0,1.222,0H0V2H1.222a1,1,0,0,1,.993.883L3.8,16.351A3,3,0,0,0,6.778,19H20V17H6.778a1,1,0,0,1-.993-.884L5.654,15H21.836Z" />
          <path d="M17.111,9.542h-.033a1.872,1.872,0,0,1-1.345-.6l-2.306-2.4L14.868,5.16,17.112,7.5,21.3,3.3l1.414,1.414L18.446,8.989A1.873,1.873,0,0,1,17.111,9.542Z" />
        </svg>
      ),
      description:
        "Connectez-vous à votre compte Eshop en saisissant votre adresse e-mail et votre mot de passe sur la page de suivi de commande pour accéder à toutes les informations relatives à vos achats.",
    },
    {
      title: "Accès à l'historique des commandes",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <circle cx="7" cy="22" r="2" />
          <circle cx="17" cy="22" r="2" />
          <path d="M22.813,9.583A6,6,0,0,1,12.8,3H4.242L4.2,2.648A3,3,0,0,0,1.222,0H0V2H1.222a1,1,0,0,1,.993.883L3.8,16.351A3,3,0,0,0,6.778,19H20V17H6.778a1,1,0,0,1-.993-.884L5.654,15H21.836Z" />
          <path d="M17.111,9.542h-.033a1.872,1.872,0,0,1-1.345-.6l-2.306-2.4L14.868,5.16,17.112,7.5,21.3,3.3l1.414,1.414L18.446,8.989A1.873,1.873,0,0,1,17.111,9.542Z" />
        </svg>
      ),
      description:
        "Une fois connecté, vous serez automatiquement redirigé vers la section 'Historique des commandes' qui vous offre une vue d'ensemble complète et organisée de toutes vos commandes passées et en cours.",
    },
    {
      title: "Sélection de la commande à suivre",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <circle cx="7" cy="22" r="2" />
          <circle cx="17" cy="22" r="2" />
          <path d="M22.813,9.583A6,6,0,0,1,12.8,3H4.242L4.2,2.648A3,3,0,0,0,1.222,0H0V2H1.222a1,1,0,0,1,.993.883L3.8,16.351A3,3,0,0,0,6.778,19H20V17H6.778a1,1,0,0,1-.993-.884L5.654,15H21.836Z" />
          <path d="M17.111,9.542h-.033a1.872,1.872,0,0,1-1.345-.6l-2.306-2.4L14.868,5.16,17.112,7.5,21.3,3.3l1.414,1.414L18.446,8.989A1.873,1.873,0,0,1,17.111,9.542Z" />
        </svg>
      ),
      description:
        "Sur la page détaillée de la commande, vous trouverez toutes les informations essentielles, y compris la liste des articles, les quantités, les prix, les dates et les informations de livraison.",
    },
    {
      title: "Détails de la commande",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <circle cx="7" cy="22" r="2" />
          <circle cx="17" cy="22" r="2" />
          <path d="M22.813,9.583A6,6,0,0,1,12.8,3H4.242L4.2,2.648A3,3,0,0,0,1.222,0H0V2H1.222a1,1,0,0,1,.993.883L3.8,16.351A3,3,0,0,0,6.778,19H20V17H6.778a1,1,0,0,1-.993-.884L5.654,15H21.836Z" />
          <path d="M17.111,9.542h-.033a1.872,1.872,0,0,1-1.345-.6l-2.306-2.4L14.868,5.16,17.112,7.5,21.3,3.3l1.414,1.414L18.446,8.989A1.873,1.873,0,0,1,17.111,9.542Z" />
        </svg>
      ),
      description:
        "Sur la page détaillée de la commande, vous trouverez toutes les informations essentielles, y compris la liste des articles, les quantités, les prix, les dates et les informations de livraison.",
    },
    {
      title: "Vérification du statut",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <circle cx="7" cy="22" r="2" />
          <circle cx="17" cy="22" r="2" />
          <path d="M22.813,9.583A6,6,0,0,1,12.8,3H4.242L4.2,2.648A3,3,0,0,0,1.222,0H0V2H1.222a1,1,0,0,1,.993.883L3.8,16.351A3,3,0,0,0,6.778,19H20V17H6.778a1,1,0,0,1-.993-.884L5.654,15H21.836Z" />
          <path d="M17.111,9.542h-.033a1.872,1.872,0,0,1-1.345-.6l-2.306-2.4L14.868,5.16,17.112,7.5,21.3,3.3l1.414,1.414L18.446,8.989A1.873,1.873,0,0,1,17.111,9.542Z" />
        </svg>
      ),
      description:
        "Le statut actuel de votre commande est mis en évidence en haut de la page, accompagné d'une brève description vous permettant de comprendre immédiatement où en est le traitement de votre commande.",
    },
    {
      title: "Suivi de la livraison",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <circle cx="7" cy="22" r="2" />
          <circle cx="17" cy="22" r="2" />
          <path d="M22.813,9.583A6,6,0,0,1,12.8,3H4.242L4.2,2.648A3,3,0,0,0,1.222,0H0V2H1.222a1,1,0,0,1,.993.883L3.8,16.351A3,3,0,0,0,6.778,19H20V17H6.778a1,1,0,0,1-.993-.884L5.654,15H21.836Z" />
          <path d="M17.111,9.542h-.033a1.872,1.872,0,0,1-1.345-.6l-2.306-2.4L14.868,5.16,17.112,7.5,21.3,3.3l1.414,1.414L18.446,8.989A1.873,1.873,0,0,1,17.111,9.542Z" />
        </svg>
      ),
      description:
        "vous trouverez un numéro de suivi dans la section 'Informations de livraison'. Cliquez simplement sur ce numéro pour être redirigé vers le site du transporteur et suivre pas à pas le trajet de votre colis jusqu'à sa destination finale.",
    },
    {
      title: "Notifications par e-mail",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <circle cx="7" cy="22" r="2" />
          <circle cx="17" cy="22" r="2" />
          <path d="M22.813,9.583A6,6,0,0,1,12.8,3H4.242L4.2,2.648A3,3,0,0,0,1.222,0H0V2H1.222a1,1,0,0,1,.993.883L3.8,16.351A3,3,0,0,0,6.778,19H20V17H6.778a1,1,0,0,1-.993-.884L5.654,15H21.836Z" />
          <path d="M17.111,9.542h-.033a1.872,1.872,0,0,1-1.345-.6l-2.306-2.4L14.868,5.16,17.112,7.5,21.3,3.3l1.414,1.414L18.446,8.989A1.873,1.873,0,0,1,17.111,9.542Z" />
        </svg>
      ),
      description:
        "Personnalisez vos préférences de notification par e-mail directement depuis la page de suivi de commande pour recevoir des mises à jour sur les étapes clés du traitement de votre commande, telles que l'expédition et la livraison.",
    },
    {
      title: "Service client",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <circle cx="7" cy="22" r="2" />
          <circle cx="17" cy="22" r="2" />
          <path d="M22.813,9.583A6,6,0,0,1,12.8,3H4.242L4.2,2.648A3,3,0,0,0,1.222,0H0V2H1.222a1,1,0,0,1,.993.883L3.8,16.351A3,3,0,0,0,6.778,19H20V17H6.778a1,1,0,0,1-.993-.884L5.654,15H21.836Z" />
          <path d="M17.111,9.542h-.033a1.872,1.872,0,0,1-1.345-.6l-2.306-2.4L14.868,5.16,17.112,7.5,21.3,3.3l1.414,1.414L18.446,8.989A1.873,1.873,0,0,1,17.111,9.542Z" />
        </svg>
      ),
      description:
        "Si vous avez des questions ou rencontrez un problème avec votre commande, notre service client dédié est à votre écoute.",
    },
    {
      title: "Réception de la commande",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <circle cx="7" cy="22" r="2" />
          <circle cx="17" cy="22" r="2" />
          <path d="M22.813,9.583A6,6,0,0,1,12.8,3H4.242L4.2,2.648A3,3,0,0,0,1.222,0H0V2H1.222a1,1,0,0,1,.993.883L3.8,16.351A3,3,0,0,0,6.778,19H20V17H6.778a1,1,0,0,1-.993-.884L5.654,15H21.836Z" />
          <path d="M17.111,9.542h-.033a1.872,1.872,0,0,1-1.345-.6l-2.306-2.4L14.868,5.16,17.112,7.5,21.3,3.3l1.414,1.414L18.446,8.989A1.873,1.873,0,0,1,17.111,9.542Z" />
        </svg>
      ),
      description:
        "Une fois votre commande livrée, le statut sera mis à jour pour confirmer la réception.",
    },
    {
      title: "Commentaires",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <circle cx="7" cy="22" r="2" />
          <circle cx="17" cy="22" r="2" />
          <path d="M22.813,9.583A6,6,0,0,1,12.8,3H4.242L4.2,2.648A3,3,0,0,0,1.222,0H0V2H1.222a1,1,0,0,1,.993.883L3.8,16.351A3,3,0,0,0,6.778,19H20V17H6.778a1,1,0,0,1-.993-.884L5.654,15H21.836Z" />
          <path d="M17.111,9.542h-.033a1.872,1.872,0,0,1-1.345-.6l-2.306-2.4L14.868,5.16,17.112,7.5,21.3,3.3l1.414,1.414L18.446,8.989A1.873,1.873,0,0,1,17.111,9.542Z" />
        </svg>
      ),
      description:
        "Vos retours sont précieux et nous aident à améliorer continuellement la qualité de nos services.",
      dot: true,
    },
  ];

  return (
    <>
      <div className="static-page track-order-page">
        {/* Banner */}
        <div className="bg-gray border-bottom static-banner">
          {/* <img src={banner} alt='Order Guide' /> */}
          <div className="content-wrapper">
            <h2>Suivi La Commande</h2>
            <div className="breadscrumb-section rounded mt-4">
              <div className="breadscrumb-contain py-2 px-4">
                <nav>
                  <ol className="breadcrumb mb-0">
                    <li className="breadcrumb-item">
                      <Link href={`/`}>
                        <Home />
                      </Link>
                    </li>
                    <li className="breadcrumb-item active" aria-current="page">
                      Suivi La Commande
                    </li>
                  </ol>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
      <section className="track-order-section">
        <div className="container-sm">
          <div className="section-b-space">
            <div className="title-group">
              <h2>Suivi La Commande</h2>
            </div>
            {/* Steps */}
            <div>
              <div className="d-flex justify-content-center steps">
                <div className="steps-wrapper">
                  {steps.map((step, i) => (
                    <StepItem key={i} step={step} number={i + 1} />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Suivi La Commande",
};

export default TrackOrder;
