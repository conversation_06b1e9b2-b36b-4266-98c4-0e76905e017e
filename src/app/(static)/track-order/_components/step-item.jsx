"use client";
import { motion, useInView } from "framer-motion";
import { useRef } from "react";

export default function StepItem({ step, number }) {
  const ref = useRef();
  const inView = useInView(ref, {
    amount: "some",
    once: true,
  });

  const container = {
    hidden: { color: "var(--theme-color)" },
    visible: {
      color: "var(--theme-color)",
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const divider = {
    hidden: { scaleY: 0 },
    visible: {
      scaleY: 1,
      transition: {
        duration: 0.3,
        delay: 0.1 * number,
      },
    },
  };

  const icon = {
    hidden: { scale: 0, rotate: "90deg" },
    visible: {
      scale: 1,
      rotate: 0,
      borderRadius: "5px",
      transition: {
        type: "spring",
        stiffness: 100,
        duration: 0.3,
        delay: 0.1 * number,
      },
    },
  };

  const stepNumber = {
    hidden: { scale: 0, opacity: 0 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        delay: 0.1 * number,
      },
    },
  };

  const title = {
    hidden: { rotateY: 90 },
    visible: {
      opacity: 1,
      rotateY: 0,
      transition: {
        duration: 0.3,
        delay: 0.1 * number,
      },
    },
  };

  const description = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.3,
        delay: 0.1 * number,
      },
    },
  };

  const endDot = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      color: "var(--theme-color)",
      transition: {
        duration: 0.3,
        delay: 1,
      },
    },
  };

  return (
    <motion.div
      ref={ref}
      variants={container}
      initial="hidden"
      whileInView="visible"
      // animate={inView ? "visible" : "hidden"}
      className={`step ${number % 2 === 0 ? "step-left" : ""} ${
        inView ? "active" : ""
      }`}
    >
      {/* Icon */}
      <div className="step-icon d-none d-md-block">
        <motion.div
          variants={icon}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          {step.icon}
        </motion.div>
      </div>
      {/* Divider */}
      <motion.div
        variants={divider}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="step-divider"
      ></motion.div>
      {/* Text Content */}
      <div className="step-content">
        <motion.div
          variants={stepNumber}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="step-number"
        >
          {number}
        </motion.div>
        <div>
          <motion.h2
            variants={title}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
          >
            {step.title}
          </motion.h2>
          <motion.p
            variants={description}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="text-justify lh-lg"
          >
            {step.description}
          </motion.p>
        </div>
      </div>
      {step.dot && (
        <motion.div
          variants={endDot}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="end-dot"
        />
      )}
    </motion.div>
  );
}
