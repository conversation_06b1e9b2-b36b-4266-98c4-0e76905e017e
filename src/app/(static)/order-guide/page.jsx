import { Home } from "react-feather";
import Link from "next/link";
import StepItem from "./_components/step-item";

export default function OrderGuide() {
  const steps = [
    {
      icon: "/assets/svg/track-order/en-ligne.png",
      title: "Navigation sur le site",
      description:
        "Parcourez facilement nos catégories de produits pour découvrir nos dernières offres.",
    },
    {
      icon: "/assets/svg/track-order/produit.png",
      title: "Informations détaillées",
      description:
        "Consultez les détails et avis sur les produits pour faire un choix éclairé.",
    },
    {
      icon: "/assets/svg/track-order/ajouter-au-panier.png",
      title: "Ajout au panier",
      description:
        "Sélectionnez vos options et ajoutez vos produits au panier.",
    },
    {
      icon: "/assets/svg/track-order/paiement-securise.png",
      title: "Validation de la commande",
      description:
        "Procédez à la validation de votre panier et préparez-vous à finaliser votre commande.",
    },
    {
      icon: "/assets/svg/track-order/colis-livre.png",
      title: "Coordonnées de Livraison",
      description:
        "Saisissez vos coordonnées et sélectionnez l'adresse de livraison pour une expédition rapide et sans erreur.",
    },
    {
      icon: "/assets/svg/track-order/sac-de-courses.png",
      title: "Mode de Paiement",
      description:
        "Optez pour votre mode de paiement préféré et renseignez les informations requises pour un paiement sécurisé.",
    },
  ];

  return (
    <div className="static-page order-guide-page">
      {/* Banner */}
      <div className="bg-gray border-bottom static-banner">
        <div className="content-wrapper">
          <h2>Comment Commander</h2>
          <div className="breadscrumb-section rounded mt-4">
            <div className="breadscrumb-contain py-2 px-4">
              <nav>
                <ol className="breadcrumb mb-0">
                  <li className="breadcrumb-item">
                    <Link href={`/`}>
                      <Home />
                    </Link>
                  </li>
                  <li className="breadcrumb-item active" aria-current="page">
                    Comment Commander
                  </li>
                </ol>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <section className="order-guide-section container-sm">
        <div>
          <div className="section-b-space">
            <div className="title-group">
              <h2>Comment Commander</h2>
            </div>
          </div>
        </div>

        <div className="steps mt-5">
          {steps.map((step, idx) => (
            <StepItem key={idx} step={step} number={idx + 1} />
          ))}
        </div>

        <div className="py-5 mt-5">
          <a href="/products" className="btn cta-btn">
            Découvrir nos produits
          </a>
        </div>
      </section>
    </div>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Comment Commander",
};
