"use client";
import { motion } from "framer-motion";

export default function StepItem({ step, number }) {
  return (
    <div className={`step ${number % 2 === 0 ? "reversed" : ""}`}>
      {/* Demi circle */}
      <div className="step-circle">
        {/* used to hide the right border of the circle */}
        <div></div>
        <div className="step-number">{number}</div>
        <motion.div
          initial={{ scale: 0, rotate: 90 }}
          whileInView={{ scale: 1, rotate: 0 }}
          viewport={{ once: true }}
          transition={{
            type: "spring",
            duration: 0.6,
          }}
          className="step-image"
        >
          <img src={step.icon} alt="Navigation sur le site" />
        </motion.div>
      </div>
      {/* text content */}
      <div className="step-text">
        <div className="d-flex gap-2 align-items-center">
          <motion.div
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{
              delay: 0.3,
            }}
            className="step-number"
          >
            {number}
          </motion.div>
          <motion.h2
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{
              delay: 0.4,
            }}
          >
            {step.title}
          </motion.h2>
        </div>
        <motion.p
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{
            delay: 0.5,
          }}
        >
          {step.description}
        </motion.p>
      </div>
    </div>
  );
}
