import { Home } from "react-feather";
import Link from "next/link";

export default function CGV() {
  const firstRow = [
    {
      number: 1,
      title: "Ouverture de compte",
      conditions: [
        "Tout utilisateur souhaitant effectuer des achats sur le site web Ecowatt E-Shop doit créer un compte. L'utilisateur est tenu de fournir des informations exactes lors de l'inscription.",
        "Ecowatt se réserve le droit d'accepter ou de refuser l'ouverture d'un compte sans avoir à fournir de justification.",
      ],
      // "",
    },
    {
      number: 3,
      title: "Livraisons",
      conditions: [
        "Les délais de livraison estimés sont indiqués lors du processus de commande. Ces délais sont donnés à titre indicatif et peuvent varier en fonction de la disponibilité des produits et des conditions logistiques.",
        "Les frais de livraison sont précisés avant la confirmation définitive de la commande. Les risques liés aux produits sont transférés à l'acheteur lors de la remise des produits au transporteur.",
      ],
    },
    {
      number: 5,
      title: "Annulation de Commande",
      conditions: [
        "L'utilisateur peut annuler une commande avant son expédition en contactant le service client d'Ecowatt E-Shop.",
        "En cas d'annulation post-expédition, l'utilisateur est soumis aux conditions de retour spécifiées à l'article 4.",
      ],
    },
    {
      number: 7,
      title: "Exonération de Responsabilité - Assurances",
      conditions: [
        "Ecowatt E-Shop décline toute responsabilité quant à l'utilisation inappropriée des produits par l'utilisateur.",
        "L'utilisateur est responsable de souscrire toute assurance nécessaire pour couvrir les risques liés à l'utilisation des produits achetés sur le site.",
      ],
    },
    {
      number: 9,
      title: "Garantie",
      conditions: [
        "Les produits vendus sur le site Ecowatt E-Shop sont couverts par la garantie légale de conformité et la garantie des vices cachés.",
      ],
    },
    {
      number: 11,
      title: "Retours et Remboursements",
      conditions: [
        "En cas de retour conforme des produits, Ecowatt E-Shop procédera au remboursement dans les meilleurs délais, déduisant éventuellement les frais de retour. Les remboursements seront effectués selon le mode de paiement initial.",
      ],
    },
  ];
  const secondRow = [
    {
      number: 2,
      title: "Commandes",
      conditions: [
        "Les commandes sont effectuées en ajoutant des produits au panier et en suivant le processus de commande en ligne sur le site Ecowatt E-Shop.",
        "L'utilisateur a la possibilité de vérifier le contenu de sa commande, de corriger d'éventuelles erreurs, et de confirmer son acceptation avant la validation finale.",
      ],
    },
    {
      number: 4,
      title: "Retours",
      conditions: [
        "Les utilisateurs ont le droit de retourner les produits dans un délai de [nombre de jours] jours à compter de la réception, sous réserve de certaines conditions.",
        "Les produits retournés doivent être dans leur état d'origine et accompagnés du bon de retour dûment rempli.",
      ],
    },
    {
      number: 6,
      title: "Prix et Paiement",
      conditions: [
        "Les prix des produits sont affichés sur le site Ecowatt E-Shop. Les prix sont exprimés en euros et incluent la TVA, sauf indication contraire.",
        "Les paiements peuvent être effectués en ligne via les méthodes de paiement proposées sur le site. Les produits demeurent la propriété d'Ecowatt jusqu'au paiement complet.",
      ],
    },
    {
      number: 8,
      title: "Confidentialité",
      conditions: [
        "Les données personnelles fournies par les utilisateurs sont traitées conformément à la politique de confidentialité d'Ecowatt E-Shop, disponible sur le site.",
      ],
    },
    {
      number: 10,
      title: "Force Majeure",
      conditions: [
        "Aucune des parties ne peut être tenue responsable en cas d'inexécution due à un cas de force majeure, incluant notamment les catastrophes naturelles, les grèves, et les événements indépendants de la volonté des parties.",
      ],
    },
  ];

  return (
    <div className="static-page cgv-page">
      {/* Banner */}
      <div className="bg-gray border-bottom static-banner">
        <div className="content-wrapper">
          <h2>Conditions générales de vente (CGV)</h2>
          <div className="breadscrumb-section rounded mt-4">
            <div className="breadscrumb-contain py-2 px-4">
              <nav>
                <ol className="breadcrumb mb-0">
                  <li className="breadcrumb-item">
                    <Link href={`/`}>
                      <Home />
                    </Link>
                  </li>
                  <li className="breadcrumb-item active" aria-current="page">
                    Conditions générales de vente (CGV)
                  </li>
                </ol>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <section className="cgv-section section-b-space container-sm">
        <div className="row d-none d-lg-flex justify-content-center cards-wrapper align-items-start gap-4">
          <div className="row flex-grow-0 g-4 col-lg-6">
            {firstRow.map((card, idx) => (
              <div
                key={idx}
                className="cgv-card"
                style={{
                  backgroundColor:
                    idx % 2 !== 0
                      ? "rgba(42, 52, 102, .6)"
                      : "rgba(255, 79, 79, .6)",
                }}
              >
                <div>
                  <span>{card.number}</span>
                </div>
                <div>
                  <h1>{card.title}</h1>
                  <ul>
                    {card.conditions.map((condition, idx) => (
                      <li key={idx}>{condition}</li>
                    ))}
                  </ul>
                  <p>{card.description}</p>
                </div>
              </div>
            ))}
          </div>
          <div
            className="row g-4 ml-4 col-lg-6"
            style={{ paddingTop: "100px" }}
          >
            {secondRow.map((card, idx) => (
              <div
                key={idx}
                className="cgv-card"
                style={{
                  backgroundColor:
                    idx % 2 === 0
                      ? "rgba(42, 52, 102, .6)"
                      : "rgba(255, 79, 79, .6)",
                }}
              >
                <div>
                  <span>{card.number}</span>
                </div>
                <div>
                  <h1>{card.title}</h1>
                  <ul>
                    {card.conditions.map((condition, idx) => (
                      <li key={idx}>{condition}</li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="row d-lg-none justify-content-center cards-wrapper align-items-start gap-4">
          <div className="row g-4 col-lg-6">
            {[...firstRow, ...secondRow]
              .sort((a, b) => a.number - b.number)
              .map((card, idx) => (
                <div
                  key={idx}
                  className="cgv-card"
                  style={{
                    backgroundColor:
                      idx % 2 !== 0
                        ? "rgba(42, 52, 102, .6)"
                        : "rgba(255, 79, 79, .6)",
                  }}
                >
                  <div>
                    <span>{card.number}</span>
                  </div>
                  <div>
                    <h1>{card.title}</h1>
                    <ul>
                      {card.conditions.map((condition, idx) => (
                        <li key={idx}>{condition}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </section>
    </div>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "CGV",
};
