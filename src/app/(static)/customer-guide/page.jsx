import Link from "next/link";
import { Home } from "react-feather";

export default function CustomerGuidePage() {
  const items = [
    {
      title: "Comment acheter sur notre e-shop pour le Black Friday",
      description:
        "Suivez notre processus d'achat détaillé pour profiter au maximum des offres du Black Friday. Ne manquez pas les meilleures offres, achetez rapidement et facilement en suivant les étapes de notre vidéo explicative.",
      videoId: "fNf4FRWm03k",
      link: "/products?search=remise",
      action: "Bénéficiez maintenant",
    },
    {
      title: "Catalogue",
      description:
        "Découvrez votre produit idéal sur Eshop ! Explorez nos catégories, parcourez notre vaste catalogue et trouvez l'article qui répond parfaitement à vos besoins. Commencez votre expérience d'achat en toute simplicité.",
      videoId: "7f0eJq9XX3E",
      link: "/products",
      action: "Visiter catalogue",
    },
    {
      title: "Ajouter au panier - passer commande / Commande Guest",
      description:
        "Apprenez à ajouter facilement des produits à votre panier et à compléter votre achat sur Eshop. Suivez les étapes de sélection des articles, de saisie de vos informations, de choix des options d'expédition et de finalisation de votre commande. Achetez en toute confiance !",
      videoId: "6BpkbIJ8tbc",
      link: "/comment-commander",
      action: "Comment commander",
    },
    {
      title: "Inscription",
      description:
        "Rejoignez la communauté Eshop en quelques clics ! Suivez le processus d'inscription, du choix de votre type de profil (personnel ou professionnel) à la saisie de vos informations et à la validation de votre compte. Devenez membre dès aujourd'hui !",
      videoId: "2YyoubqbGGA",
      link: "/register",
      action: "Inscrivez-vous",
    },
    {
      title: "Devenir un revendeur",
      description:
        "Développez votre entreprise avec Eshop ! Découvrez les étapes simples pour devenir revendeur, en visitant notre page « Rejoignez-nous », en complétant le formulaire, en signant et en téléchargeant les documents requis, et en confirmant vos coordonnées. Rejoignez notre réseau de revendeurs et ouvrez de nouvelles opportunités !",
      videoId: "apGq4KMoq8o",
      link: "/rejoignez-nous",
      action: "Devenir un revendeur",
    },
    {
      title: "Profile",
      description:
        "Prenez le contrôle de votre compte Eshop ! Accédez à votre profil, consultez et modifiez vos informations, et mettez à jour votre mot de passe. Assurez la sécurité et l'actualité de votre compte avec facilité.",
      videoId: "dQppeGKhXqA",
      link: "/account",
      action: "Mon profile",
    },
  ];

  return (
    <div>
      <div className="static-page guide-page">
        {/* Banner */}
        <div className="bg-gray border-bottom static-banner">
          <div className="content-wrapper">
            <h2>Guide d'utilisation</h2>
            <div className="breadscrumb-section rounded mt-4">
              <div className="breadscrumb-contain py-2 px-4">
                <nav>
                  <ol className="breadcrumb mb-0">
                    <li className="breadcrumb-item">
                      <Link href={`/`}>
                        <Home />
                      </Link>
                    </li>
                    <li className="breadcrumb-item active" aria-current="page">
                      Guide d'utilisation
                    </li>
                  </ol>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
      <section className="guide-section">
        <div className="container-sm">
          <div className="section-b-space">
            <div className="title-group">
              <h2>Guide d'utilisation</h2>
            </div>
            <div className="row gap-5 mt-5">
              {items.map((item, idx) => (
                <div
                  key={idx}
                  className={`row m-0 gap-5 guide-row ${
                    idx % 2 !== 0 ? "flex-row-reverse" : ""
                  }`}
                >
                  <div className="col-lg-5 p-0 mx-auto bg-gray">
                    <iframe
                      title={item.title}
                      id={item.title}
                      width="100%"
                      height="300px"
                      src={`https://www.youtube.com/embed/${item.videoId}`}
                      frameborder="0"
                      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                      allowfullscreen
                      loading="lazy"
                    >
                      Your browser does not support this, try viewing it on
                      YouTube: YOUR URL
                    </iframe>
                  </div>
                  <div className="col p-l-lg-5">
                    <div>
                      <h2
                        className={`mb-3 ${idx % 2 !== 0 ? "text-lg-end" : ""}`}
                      >
                        {item.title}
                      </h2>
                      <p className="text-justify lh-lg">{item.description}</p>
                    </div>
                    <div
                      className={`d-flex ${
                        idx % 2 !== 0
                          ? "justify-content-start"
                          : "justify-content-lg-end"
                      }`}
                    >
                      <a
                        href={item.link}
                        className="btn btn-md guide-action-link"
                      >
                        {item.action}
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Guide d'utilisation",
};
