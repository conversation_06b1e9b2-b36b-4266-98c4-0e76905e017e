import { Home } from "react-feather";
import Link from "next/link";

export default function DeliveryMethod() {
  const deliveryMethods = [
    {
      title: "CTM Messagerie",
      icon: "/assets/images/ctm.png",
      pros: [
        "<PERSON><PERSON><PERSON> de livraison moyen de 3 à 5 jours ouvrables.",
        "Frais de livraison fixes ou gratuits pour les commandes supérieures à un certain montant.",
      ],
    },
    {
      title: "ECOWATT Logistics",
      icon: "/assets/images/ecowatt-log.jpeg",
      pros: [
        "Permet aux clients de choisir une date et une heure de livraison spécifiques.",
        "Utile pour les personnes qui veulent recevoir leur commande à un moment précis.",
      ],
    },
    {
      title: "Point de retrait en magasin",
      icon: "/assets/images/in-place.png",
      pros: [
        "Les clients peuvent choisir de récupérer leur commande dans un magasin physique.",
        "Option gratuite ou à des frais réduits.",
      ],
    },
  ];

  return (
    <div>
      <div className="static-page delivery-method-page">
        {/* Banner */}
        <div className="bg-gray border-bottom static-banner">
          <div className="content-wrapper">
            <h2>Mode de livraison</h2>
            <div className="breadscrumb-section rounded mt-4">
              <div className="breadscrumb-contain py-2 px-4">
                <nav>
                  <ol className="breadcrumb mb-0">
                    <li className="breadcrumb-item">
                      <Link href={`/`}>
                        <Home />
                      </Link>
                    </li>
                    <li className="breadcrumb-item active" aria-current="page">
                      Mode de livraison
                    </li>
                  </ol>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
      <section className="delivery-method-section">
        <div className="container-sm">
          <div className="section-b-space">
            <div className="title-group">
              <h2>Mode de livraison</h2>
            </div>
            <div className="mt-5">
              {deliveryMethods.map((method, key) => (
                <div key={key} className="delivery-method-card">
                  <div>
                    <img src={method.icon} alt={method.title} />
                  </div>
                  <div>
                    <h3>{method.title}</h3>
                    <ul>
                      {method.pros.map((pro, idx) => (
                        <li key={idx} className="d-block">
                          {pro}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Mode de livraison",
};
