import { Home } from "react-feather";
import Link from "next/link";

export default function PaymentPage() {
  const paymentMethods = [
    {
      title: "Paiement À La Livraison",
      icon: "/assets/images/paiement-a-la-livraison.png",
      pros: [
        "Option permettant aux clients de payer en espèces lors de la réception de leur commande.",
        "Disponible pour certaines zones géographiques.",
      ],
    },
    {
      title: "Cartes De Crédit/Débit",
      icon: "/assets/images/credit-card.png",
      pros: [
        "Acceptation des principales cartes de crédit (Visa, MasterCard,Maestro, CMI etc.).",
        "Processus de paiement sécurisé avec une interface conviviale.",
      ],
    },
    {
      title: "Virement Bancaire",
      icon: "/assets/images/virement-bancaire.png",
      pros: [
        "Les clients peuvent effectuer un virement direct depuis leur compte bancaire.",
        "Fournir les informations nécessaires pour faciliter le processus.",
      ],
    },
    {
      title: "Financement En Ligne",
      icon: "/assets/images/finance-online.png",
      pros: [
        "Ecowatt dispose un système de financement pour faciliter l'opération en Collaboration avec des sociétés de financement en ligne pour offrir des options de paiement échelonné ou à crédit.",
      ],
    },
    {
      title: "Programme De Fidélité",
      icon: "/assets/images/programme-fidelite.png",
      pros: [
        "Permet aux clients d'utiliser des points defidélité ou des récompenses pour payer une partie ou la totalité de leur commande.",
      ],
    },
  ];

  return (
    <div>
      <div className="static-page payment-method-page">
        {/* Banner */}
        <div className="bg-gray border-bottom static-banner">
          <div className="content-wrapper">
            <h2>Mode de paiment</h2>
            <div className="breadscrumb-section rounded mt-4">
              <div className="breadscrumb-contain py-2 px-4">
                <nav>
                  <ol className="breadcrumb mb-0">
                    <li className="breadcrumb-item">
                      <Link href={`/`}>
                        <Home />
                      </Link>
                    </li>
                    <li className="breadcrumb-item active" aria-current="page">
                      Mode de paiment
                    </li>
                  </ol>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
      <section className="payment-method-section">
        <div className="container-sm">
          <div className="section-b-space">
            <div className="title-group">
              <h2>Mode de paiment</h2>
            </div>
            <div className="mt-5">
              {paymentMethods.map((method, key) => (
                <div key={key} className="payment-method-card">
                  <div>
                    <img src={method.icon} alt={method.title} />
                  </div>
                  <div>
                    <h3>{method.title}</h3>
                    <ul>
                      {method.pros.map((pro, idx) => (
                        <li key={idx} className="d-block">
                          {pro}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Mode de paiment",
};
