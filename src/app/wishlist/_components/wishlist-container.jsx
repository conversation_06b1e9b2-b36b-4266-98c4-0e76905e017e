"use client";

import ProductBox from "@/components/product-box";
import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";
import { useContext } from "react";
import { InfinitySpin } from "react-loader-spinner";

export default function GuestWishlistContainer() {
  const { wishlistItems, getWishlistItemsGuestLoading } = useContext(
    CartAndWishlistProvider
  );

  //   if (isLoggedIn) {
  //     return <Redirect to={`/account/wishlist`} />;
  //   }

  return (
    <section className="user-dashboard-section section-b-space">
      <div className="container-lg">
        <div className="dashboard-right-sidebar">
          <div className="dashboard-wishlist">
            <div className="title">
              <h2>Liste d'envies</h2>
              <span className="title-leaf title-leaf-gray">
                <img
                  src="/assets/svg/leaf.png"
                  alt=""
                  className="icon-width bg-gray"
                />
              </span>
            </div>
            <div className="row g-sm-4 g-3">
              {getWishlistItemsGuestLoading ? (
                <div className="min-vh-100 px-4 py-2 d-flex align-items-center justify-content-center">
                  <InfinitySpin
                    type="ThreeDots"
                    color="#2A3466"
                    height={220}
                    width={220}
                    visible={getWishlistItemsGuestLoading}
                  />
                </div>
              ) : wishlistItems && wishlistItems.length ? (
                <div className="row g-3 row-cols-2 row-cols-sm-3 row-cols-md-4 row-cols-xl-5">
                  {wishlistItems.map((item, key) => (
                    <div key={`wishlist-${key}`}>
                      <ProductBox product={item} isWishlist={true} />
                    </div>
                  ))}
                </div>
              ) : (
                <h2 className="text-center my-5">Aucun produit trouvé</h2>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
