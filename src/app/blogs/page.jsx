import BlogCard from "@/app/blogs/_components/blog-card";
import ExploreBlogs from "@/app/blogs/_components/explore-blogs";
import Count from "@/components/count";
import PageBanner from "@/components/page-banner";
import SectionTitle from "@/components/section-title";
import { BlogService } from "@/services/blog";
import Link from "next/link";
import { Edit3, ExternalLink } from "react-feather";

export default async function BlogsPage() {
  const data = await BlogService.getBlogsFeed();

  return (
    <div className="-mt-[1.5rem] space-y-20 pb-20">
      <PageBanner
        title="Blogs"
        background="/assets/images/blogs-banner-bg.webp"
      />

      {/* Tendance actuel */}
      {data?.thismonthblogs?.length > 0 && (
        <section className="container-lg pt-0">
          <SectionTitle title="Tendances Actuelles" />

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {data?.thismonthblogs?.map((blog) => (
              <BlogCard key={blog.id} blog={blog} />
            ))}
          </div>
        </section>
      )}

      {/* Popular */}
      {data?.blogs?.length > 0 && (
        <section className="container-lg pt-0">
          <SectionTitle title="Blogs Populaires" />

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {data?.blogs?.map((blog) => (
              <BlogCard key={blog.id} blog={blog} />
            ))}
          </div>
        </section>
      )}

      {/* Submit blog cta */}
      <div className="container-lg pt-0">
        <div className="bg-[url('/assets/images/submit-blog-cta.webp')] bg-center bg-cover">
          <section className="flex flex-col pt-0 space-y-5 h-[230px] justify-center items-center bg-[#F0F0F0]/70">
            <h4 className="font-medium text-lg md:text-xl lg:text-2xl text-center">
              Partagez vos connaissances avec nos lecteurs
            </h4>
            <Link
              href="/blogs/submit"
              className="flex bg-theme px-6 py-2 rounded text-white items-center gap-2"
            >
              <Edit3 size={18} /> <span>Exprimez-vous</span>
            </Link>
          </section>
        </div>
      </div>

      {/* Stats */}
      <section className="container-lg pt-0">
        <SectionTitle title="Mise à Jour Aujourd'hui" />

        <div className="grid sm:grid-cols-3 gap-3">
          <div className="p-8 bg-theme flex flex-col gap-2 text-white text-center">
            <Count
              direction="up"
              value={data?.totalblogs ?? 0}
              className="text-4xl font-semibold"
            />
            <span>Total Blogs</span>
          </div>
          <div className="p-8 bg-theme flex flex-col gap-2 text-white text-center">
            <Count
              direction="up"
              value={data?.tyotalcategories ?? 0}
              className="text-4xl font-semibold"
            />
            <span>Catégories</span>
          </div>
          <div className="p-8 bg-theme flex flex-col gap-2 text-white text-center">
            <Count
              direction="up"
              value={data?.authorscount ?? 0}
              className="text-4xl font-semibold"
            />
            <span>Auteurs</span>
          </div>
        </div>
      </section>

      {/* Explore blogs */}
      <ExploreBlogs categories={data?.categories ?? []} />

      {/* LinkedIn news */}
      <section className="container-lg pt-0">
        <SectionTitle title="LinkedIn Nouveautés" />

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-10">
          {[
            {
              link: "https://fr.linkedin.com/posts/ecowatt-maroc_nous-sommes-des-visionnaires-des-perturbateurs-activity-7291181311602352128-i_C5",
              image: "/assets/images/linkedIn/2.webp",
            },
            {
              link: "https://www.linkedin.com/posts/ecowatt-maroc_formation-shakti-activity-7288239056906342400-MKl9",
              image: "/assets/images/linkedIn/3.webp",
            },
            {
              link: "https://fr.linkedin.com/posts/ecowatt-maroc_day-2-activity-7282781235427368962-TThV",
              image: "/assets/images/linkedIn/4.webp",
            },
            {
              link: "https://fr.linkedin.com/posts/ecowatt-maroc_day-1-activity-7282116866431406081-oN2E",
              image: "/assets/images/linkedIn/5.webp",
            },
          ].map((post, key) => (
            <Link
              key={key}
              target="_blank"
              href={post.link}
              className="relative group border"
            >
              <ExternalLink
                size={16}
                className="absolute top-2 right-2 text-white"
              />
              <img src={post.image} alt={post.link} />
            </Link>
          ))}
        </div>
      </section>
    </div>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Conseils et Actualités en Énergie Durable",
  alternates: {
    canonical: "/blogs",
  },
};
