"use client";

import BlogCard from "@/app/blogs/_components/blog-card";
import SectionTitle from "@/components/section-title";
import { useBlogs } from "@/services/blog";
import Loading from "@/shared/components/loading";
import Pagination from "@/shared/components/pagination";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function ExploreBlogs({ categories }) {
  const sp = useSearchParams();
  const [page, setPage] = useState(sp.get("page") ?? 1);
  const [selectedCategories, setSelectedCategories] = useState([]);

  const handleSearchByCategory = (category) => {
    setSelectedCategories((categories) =>
      categories.includes(category)
        ? categories.filter((c) => c !== category)
        : [...categories, category]
    );
  };

  const { blogs, isLoading } = useBlogs({
    page,
    categories: selectedCategories,
  });

  useEffect(() => {
    setPage(1);
  }, [selectedCategories]);

  const [view, setView] = useState("grid");

  const handlePageChange = (page) => {
    setPage(page);
  };

  return (
    <section className="container-lg pt-0">
      <SectionTitle title="Tous les Blogs" />

      <div className="flex flex-wrap items-center gap-2 mb-6">
        {categories.map((category, index) => (
          <button
            key={index}
            className={`
            border-1 border-[var(--theme-color)] rounded px-3 py-1 text-xs font-medium
            ${selectedCategories.includes(category.slug) ? "bg-theme text-white" : ""}
            `}
            onClick={() => handleSearchByCategory(category.slug)}
          >
            <span className="checkbox-label">{category.name}</span>
          </button>
        ))}
      </div>

      {isLoading ? (
        <Loading />
      ) : (
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {blogs?.data?.map((blog) => (
            <BlogCard key={blog.id} blog={blog} />
          ))}
        </div>
      )}
      {blogs && (
        <Pagination
          className="pagination-bar"
          currentPage={blogs?.current_page}
          totalCount={blogs?.total}
          pageSize={blogs?.per_page}
          onPageChange={handlePageChange}
        />
      )}
    </section>
  );
}
