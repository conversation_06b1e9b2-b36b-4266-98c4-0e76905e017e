import moment from "moment";
import Link from "next/link";
import { Calendar, Clock } from "react-feather";

export default function BlogCard({ blog }) {
  return (
    <div className="p-2 flex flex-col space-y-2 shadow-[0_0_11px_2px_var(--theme-color-light)]">
      <div className="block w-full bg-[var(--theme-color-light)]">
        <Link href={`/blogs/${blog.slug}`} className="block">
          <img
            src={blog.full_thumbnail}
            alt={blog.title}
            className="object-cover hover:scale-125 aspect-[1.7/1] object-center w-full"
          />
        </Link>
      </div>

      <div className="flex flex-col space-y-2 p-2 flex-1">
        <ul className="flex flex-wrap items-center gap-1">
          {blog.categories?.slice(0, 3)?.map((category) => (
            <li
              key={category.id}
              className="px-2 py-1 text-xs rounded bg-[var(--theme-color-light)]"
            >
              {category.name}
            </li>
          ))}
          {blog?.categories?.length > 3 && (
            <span
              className="px-2 py-0.5 text-xs font-medium rounded bg-[var(--theme-color-light)]"
              title={blog.categories
                ?.slice(0, 3)
                ?.map((c) => c.name)
                .join(" | ")}
            >
              +{blog?.categories?.length - 3}
            </span>
          )}
        </ul>

        <div className="flex items-center gap-1">
          <Clock size={14} className="text-[#FF0000]" />{" "}
          <span className="text-xs">{blog.read_time} min. Lecture</span>
        </div>

        <div className="mt-3">
          <Link href={`/blogs/${blog.slug}`}>
            <h2 className="text-lg leading-5 line-clamp-2">{blog.title}</h2>
          </Link>
        </div>

        <div>
          <p className="line-clamp-3 text-xs text-justify">
            {blog.description}
          </p>
        </div>

        <div className="flex items-center gap-1 mt-auto">
          <Calendar size={14} className="text-[#FF0000]" />{" "}
          <span className="text-xs">
            {moment(blog.updated_at).format("MMM Do YY")}
          </span>
        </div>
      </div>
    </div>
  );
}
