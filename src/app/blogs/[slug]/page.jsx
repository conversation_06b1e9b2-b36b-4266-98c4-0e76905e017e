import { getUrl } from "@/lib/helpers";
import { BlogService } from "@/services/blog";
import { CommonService } from "@/services/common";
import HtmlContent from "@/shared/components/html-content";
import moment from "moment";
import { Calendar, Clock } from "react-feather";

export default async function BlogPage({ params }) {
  const { slug } = params;
  const blog = await BlogService.getBlog(slug);

  return (
    <div className="container-lg">
      <div className="blog-details-container my-5">
        {/* Blog title */}
        <h1 className="fs-1">{blog.title}</h1>

        {/* About blog author */}
        <div className="d-flex align-items-center gap-4 my-5">
          <img
            src={blog.author?.full_avatar ?? "/logo192.png"}
            alt={blog.author?.nickname ?? blog.author?.full_name}
            width={55}
            height={55}
            className="rounded-circle flex-shrink-0"
          />
          <div>
            <div className="mb-1 d-flex align-items-center gap-2">
              <h2 className="fs-6 text-black">
                {blog.author?.nickname ?? blog.author?.full_name}
              </h2>
              <span>·</span>
              <small className="text-muted">Auteur</small>
            </div>
            <div className={`my-1 text-muted d-flex flex-wrap gap-2`}>
              <div className="d-flex align-items-center gap-2">
                <Calendar size={14} />
                <span>{moment(blog?.created_at).format("MMM Do YY")}</span>
              </div>
              <span>|</span>
              <div className="d-flex align-items-center gap-2">
                <Clock size={14} />
                <span>{blog?.read_time} min. Lecture</span>
              </div>
            </div>
          </div>
        </div>

        <div>
          {/* Blog thumbnail */}
          <img src={blog.full_thumbnail} alt="thumbnail" />

          {/* Blog content */}
          <div className="my-5">
            <HtmlContent html={blog.content} />
          </div>

          {/* Blog categories */}
          <div>
            {blog?.categories?.map((category, idx) => (
              <span key={idx} className="category-badge mx-1">
                {category.name}
              </span>
            ))}
          </div>
        </div>
      </div>
      {blog && <BlogJsonLD blog={blog} />}
    </div>
  );
}

async function BlogJsonLD({ blog }) {
  const settings = await CommonService.getSiteSettings();

  /** @type {import('schema-dts').Article} */
  const jsonLD = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": getUrl(`/blogs/${blog.slug}`),
    },
    headline: blog.title,
    description: blog.description,
    image: blog.full_thumbnail,
    author: {
      "@type": "Person",
      name: blog.author.nickname ?? blog.author.full_name,
    },
    publisher: {
      "@type": "Organization",
      name: settings?.store_name,
      logo: {
        "@type": "ImageObject",
        url: settings?.store_white,
      },
    },
    datePublished: blog.created_at,
    dateModified: blog.updated_at,
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLD) }}
    />
  );
}

/** @return {import('next').Metadata} */
export async function generateMetadata({ params }) {
  const { slug } = params;
  const blog = await BlogService.getBlog(slug);

  return {
    title: blog.title,
    description: blog.description,
    openGraph: {
      type: "article",
      url: getUrl(`/blogs/${blog.slug}`),
      images: [blog.full_thumbnail],
      tags: blog.categories?.map((category) => category.name),
    },
    alternates: {
      canonical: `/blogs/${blog.slug}`,
    },
  };
}
