import { BlogService } from "@/services/blog";
import SubmitBlogForm from "./_components/submit-blog-form";

export default async function SubmitBlog() {
  const categories = await BlogService.getBlogCategories();

  return (
    <div className="submit-blog">
      <div className="container-lg mt-5">
        <SubmitBlogForm categories={categories} />
      </div>
    </div>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Exprimez-Vous",
};
