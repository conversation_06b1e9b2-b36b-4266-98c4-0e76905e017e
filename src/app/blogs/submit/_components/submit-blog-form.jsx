"use client";

import { useSubmitBlogMutation } from "@/services/blog";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import { Editor } from "@tinymce/tinymce-react";
import { Formik } from "formik";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import { useRef, useState } from "react";
import { array, mixed, object, string } from "yup";
import SubmitBlogSuccess from "./submit-blog-success";

const SUPPORTED_FORMATS = [
  "image/jpg",
  "image/gif",
  "image/png",
  "image/jpeg",
  "image/svg",
  "image/webp",
];
const FILE_SIZE = 1024 * 2048;

const formSchema = object({
  title: string().required("Ce champ est obligatoire"),
  description: string().required("Ce champ est obligatoire"),
  categories: array()
    .min(1, "Ce champ est obligatoire")
    .required("Ce champ est obligatoire"),
  thumbnail: mixed()
    .required("Ce champ est obligatoire")
    .test(
      "FILE_SIZE",
      "Le fichier téléchargé est trop volumineux.",
      (value) => !value || (value && value.size <= FILE_SIZE)
    )
    .test(
      "FILE_FORMAT",
      "Le fichier téléchargé a un format non pris en charge.",
      (value) => !value || (value && SUPPORTED_FORMATS.includes(value.type))
    ),
  content: string().required("Ce champ est obligatoire"),
});

function genInitialValues() {
  return {
    title: "",
    description: "",
    categories: [],
    thumbnail: "",
    content: "",
  };
}

export default function SubmitBlogForm({ categories }) {
  const editorRef = useRef(null);
  const [selectedCategories, setSelectedCategories] = useState([]);

  const { submitBlog, isLoading, isSuccess, error } = useSubmitBlogMutation();

  function handleCategoriesChange(category) {
    setSelectedCategories((categories) => [
      ...new Set([...categories, category]),
    ]);
  }

  function handleRemoveSelectedCategories(category) {
    setSelectedCategories((categories) =>
      categories.filter((c) => c !== category)
    );
  }

  function onSubmit(data) {
    submitBlog(data, {
      onSettled() {
        window.scrollTo({ top: 0 });
      },
    });
  }

  if (isSuccess) {
    return <SubmitBlogSuccess />;
  }

  return (
    <>
      {error && <ErrorSnackbar message={error.message} />}

      <Formik
        initialValues={genInitialValues()}
        validationSchema={formSchema}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          setFieldValue,
        }) => (
          <form onSubmit={handleSubmit} className="row g-4">
            {/* Title */}
            <div className="col-12">
              <div className="form-floating theme-form-floating">
                <input
                  type="text"
                  className="form-control"
                  id="title"
                  name="title"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.title}
                />
                <label htmlFor="title">Title</label>
              </div>
              <span className="error-form">
                {errors.title && touched.title && errors.title}
              </span>
            </div>

            {/* Description */}
            <div className="col-12">
              <div className="form-floating theme-form-floating">
                <textarea
                  className="form-control"
                  id="email"
                  name="description"
                  rows={5}
                  style={{ height: "auto" }}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.description}
                />
                <label htmlFor="description">Description</label>
              </div>
              <span className="error-form">
                {errors.description &&
                  touched.description &&
                  errors.description}
              </span>
            </div>

            {/* Categories */}
            <div className="col-12">
              <div className="form-floating theme-form-floating">
                <select
                  name="categories"
                  className="form-control form-select bg-gray"
                  onChange={(e) => {
                    setFieldValue("categories", [
                      ...new Set([...selectedCategories, e.target.value]),
                    ]);
                    handleCategoriesChange(e.target.value);
                    e.target.value = "";
                  }}
                  onBlur={handleBlur}
                  defaultValue=""
                >
                  <option value="" disabled></option>
                  {categories
                    ?.filter((c) => !selectedCategories?.includes(c.slug))
                    ?.map((category) => (
                      <option key={category.id} value={category.slug}>
                        {category.name}
                      </option>
                    ))}
                </select>
                <label htmlFor="description">Catégories</label>
              </div>
              <span className="error-form">
                {errors.categories &&
                  touched.categories &&
                  !selectedCategories?.length &&
                  "Ce champ est obligatoire"}
              </span>
              <div className="selected-categories">
                <AnimatePresence>
                  {selectedCategories.map((category) => (
                    <motion.span
                      key={category}
                      initial={{ y: -10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      exit={{ y: -10, opacity: 0 }}
                      role="button"
                      onClick={() => {
                        handleRemoveSelectedCategories(category);
                        setFieldValue(
                          "categories",
                          selectedCategories.filter((c) => c !== category)
                        );
                      }}
                    >
                      &times; {category}
                    </motion.span>
                  ))}
                </AnimatePresence>
              </div>
            </div>

            {/* Thumbnail */}
            <div className="col-12">
              <div className="form-floating theme-form-floating">
                <input
                  type="file"
                  className="form-control"
                  id="thumbnail"
                  name="thumbnail"
                  style={{ height: "auto", paddingTop: "30px" }}
                  onChange={(event) => {
                    setFieldValue("thumbnail", event.currentTarget.files[0]);
                  }}
                  onBlur={handleBlur}
                />
                <label htmlFor="thumbnail">Image (1400 x 944 - &lt; 1mb)</label>
              </div>
              <span className="error-form">
                {errors.thumbnail && touched.thumbnail && errors.thumbnail}
              </span>
            </div>

            {/* Content */}
            <div className="col-12">
              <div>
                <label htmlFor="description" className="mb-2">
                  Contenu
                </label>
                <span className="error-form">
                  {errors.content && touched.content && errors.content}
                </span>
                <Editor
                  tinymceScriptSrc={`${process.env.NEXT_PUBLIC_BACKEND_URL}/js/tinymce/tinymce.min.js`}
                  onInit={(_evt, editor) => (editorRef.current = editor)}
                  initialValue=""
                  name="content"
                  onEditorChange={(content) =>
                    setFieldValue("content", content)
                  }
                  onBlur={handleBlur}
                  init={{
                    height: 500,
                    menubar: true,
                    automatic_uploads: true,
                    relative_urls: false,
                    remove_script_host: false,
                    convert_urls: true,
                    plugins: [
                      "anchor",
                      "autolink",
                      "charmap",
                      "codesample",
                      "emoticons",
                      "image",
                      "link",
                      "lists",
                      "media",
                      "searchreplace",
                      "table",
                      "visualblocks",
                      "wordcount",
                      "code",
                    ],
                    toolbar:
                      "undo redo | blocks fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat | code",
                    file_picker_callback: function (cb, value, meta) {
                      var input = document.createElement("input");
                      input.setAttribute("type", "file");
                      input.setAttribute("accept", "image/*");
                      input.onchange = function () {
                        var file = this.files[0];

                        var reader = new FileReader();
                        reader.readAsDataURL(file);
                        reader.onload = function () {
                          var id = "blobid" + new Date().getTime();
                          var blobCache =
                            editorRef.current.editorUpload.blobCache;
                          var base64 = reader.result.split(",")[1];
                          var blobInfo = blobCache.create(id, file, base64);
                          blobCache.add(blobInfo);
                          cb(blobInfo.blobUri(), {
                            title: file.name,
                          });
                        };
                      };
                      input.click();
                    },
                  }}
                />
              </div>
            </div>

            <div className="submit-blog-form-actions">
              <Link href="/blogs" disabled={isLoading}>
                Annuler
              </Link>
              <button type="submit" disabled={isLoading}>
                {isLoading ? "Soumettre..." : "Soumettre"}
              </button>
            </div>
          </form>
        )}
      </Formik>
    </>
  );
}
