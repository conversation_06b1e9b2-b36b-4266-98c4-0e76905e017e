"use client";

import { formatPrice } from "@/lib/helpers";
import { useAuthContext } from "@/providers/auth-provider";
import { useAddresses } from "@/services/customer";
import { useEffect, useState } from "react";
import { Map, Target, User } from "react-feather";
import { InfinitySpin } from "react-loader-spinner";
import AddCustomerInfos from "./add-customer-infos";
import ListAddresses from "./list-addresses";

export default function LeftSide({
  loading,
  deliveryAddress,
  billingAddress,
  deliveryMethod,
  saveData,
  paymentMethod,
  cartCalculation,
  applyWallet,
}) {
  const { currentUser, authenticated } = useAuthContext();

  const {
    addresses,
    isLoading: isLoadingAddresses,
    isFetching: isFetchingAddresses,
  } = useAddresses();

  const [billingAddresses, setBillingAddresses] = useState([]);
  const [deliveryAddresses, setDeliveryAddresses] = useState([]);
  const [defaultChecked, setDefaultChecked] = useState(true);

  useEffect(() => {
    if (
      !isLoadingAddresses &&
      !isFetchingAddresses &&
      addresses &&
      addresses.length
    ) {
      let addressDelivery = addresses.filter(function (el) {
        return el.type === "delivery";
      });
      if (addressDelivery && addressDelivery.length)
        setDeliveryAddresses(addressDelivery);

      let addressBilling = addresses.filter(function (el) {
        return el.type === "billing";
      });
      if (addressBilling && addressBilling.length)
        setBillingAddresses(addressBilling);
    }
  }, [isFetchingAddresses, isLoadingAddresses]);

  const savePayment = (data) => {
    if (data !== paymentMethod) {
      saveData("payment", data);
    }
  };
  const saveDelivery = (data) => {
    if (data !== deliveryMethod) {
      saveData("deliveryMethod", data);
    }
  };
  const saveCustomer = (data) => {
    saveData("customer", data);
  };
  const saveDeliveryAddress = (data) => {
    saveData("delivery", data);
    if (defaultChecked) {
      saveBillingAddress(data);
    }
  };
  const saveBillingAddress = (data) => {
    saveData("billing", data);
  };
  const checkTheSameAddress = (event) => {
    setDefaultChecked(event);
    if (event) {
      saveBillingAddress(deliveryAddress);
    } else {
      saveBillingAddress(null);
    }
  };

  return (
    <div>
      <div className="left-sidebar-checkout">
        <div className="checkout-detail-box">
          <ul>
            {!authenticated && (
              <li>
                <div className="checkout-icon">
                  <User />
                </div>
                <AddCustomerInfos save={saveCustomer} />
              </li>
            )}
            <li>
              <div className="checkout-icon">
                <Map />
              </div>

              {isLoadingAddresses || isFetchingAddresses ? (
                <div className="checkout-box">
                  <div className="min-vh-100 px-4 py-2 d-flex align-items-center justify-content-center">
                    <InfinitySpin
                      type="ThreeDots"
                      color="#2A3466"
                      height={220}
                      width={220}
                    />
                  </div>
                </div>
              ) : (
                <>
                  <ListAddresses
                    loading={loading}
                    current={deliveryAddress}
                    type="delivery"
                    addresses={deliveryAddresses}
                    save={saveDeliveryAddress}
                    isAuthenticated={authenticated}
                  />
                  {authenticated ? (
                    <ListAddresses
                      loading={loading}
                      defaultChecked={defaultChecked}
                      checkTheSameAddress={checkTheSameAddress}
                      currentDeliveryAddress={deliveryAddress}
                      current={billingAddress}
                      type="billing"
                      addresses={billingAddresses}
                      save={saveBillingAddress}
                      isAuthenticated={authenticated}
                      useAs={true}
                    />
                  ) : (
                    <div />
                  )}
                </>
              )}
            </li>

            <li>
              <div className="checkout-icon">
                <Target />
              </div>
              <div className="checkout-box">
                <div
                  className={`checkout-title ${deliveryAddress && billingAddress ? "" : "m-0"} `}
                >
                  <h4>La livraison</h4>
                </div>

                {deliveryAddress && billingAddress ? (
                  <>
                    <div
                      className="checkout-detail payment-details mb-3"
                      onClick={!loading ? () => saveDelivery("ecowatt") : null}
                    >
                      <div className="bg-white w-100 d-flex align-items-center justify-content-between p-4">
                        <div className="d-flex align-items-center">
                          <input
                            className="form-check-input my-0"
                            type="radio"
                            name="deliveryMethod"
                            id="credit"
                            checked={deliveryMethod === "ecowatt"}
                            readOnly
                          />
                          <span className="d-block ml-5">
                            Ecowatt Logistique
                          </span>
                        </div>
                        <img
                          alt="Ecowatt"
                          src="/assets/images/ecowatt-log.jpeg"
                        />
                      </div>
                    </div>
                    {/* <div
                      className="checkout-detail payment-details mb-3"
                      onClick={!loading ? () => saveDelivery("ctm") : null}
                    >
                      <div className="bg-white w-100 d-flex align-items-center justify-content-between p-4">
                        <div className="d-flex align-items-center">
                          <input
                            className="form-check-input my-0"
                            type="radio"
                            name="deliveryMethod"
                            id="credit"
                            checked={deliveryMethod === "ctm"}
                            readOnly
                          />
                          <span className="d-block ml-5">Livraison CTM</span>
                        </div>
                        <img alt="CTM" src="/assets/images/ctm.png" />
                      </div>
                    </div> */}

                    <div
                      className="checkout-detail payment-details mb-3"
                      onClick={
                        !loading ? () => saveDelivery("voie_express") : null
                      }
                    >
                      <div className="bg-white w-100 d-flex align-items-center justify-content-between p-4">
                        <div className="d-flex align-items-center">
                          <input
                            className="form-check-input my-0"
                            type="radio"
                            name="deliveryMethod"
                            id="credit"
                            checked={deliveryMethod === "voie_express"}
                            readOnly
                          />
                          <span className="d-block ml-5">La Voie EXPRESS</span>
                        </div>
                        <img
                          alt="La Voie EXPRESS"
                          src="/assets/images/voie-express.png"
                        />
                      </div>
                    </div>

                    <div
                      className="checkout-detail payment-details"
                      onClick={!loading ? () => saveDelivery("in_place") : null}
                    >
                      <div className="bg-white w-100 d-flex align-items-center justify-content-between p-4">
                        <div className="d-flex align-items-center">
                          <input
                            className="form-check-input my-0"
                            type="radio"
                            name="deliveryMethod"
                            id="credit"
                            checked={deliveryMethod === "in_place"}
                            readOnly
                          />
                          <span className="d-block ml-5">
                            Récupérer au magasin
                          </span>
                        </div>
                        <img alt="Surplace" src="/assets/images/in-place.png" />
                      </div>
                    </div>
                  </>
                ) : (
                  <div />
                )}
              </div>
            </li>

            {cartCalculation?.wallet > 0 && (
              <li>
                <div className="checkout-icon">
                  <svg
                    height="25"
                    viewBox="0 0 541 541"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M535.56 62.9V61.77L535.37 62.33C523 26.11 488.65 0 448.29 0H91.97C51.56 0 17.12 26.2 4.84 62.52L4.70001 62.19V62.9C1.64001 72.07 0 81.81 0 91.97V448.29C0 499 41.26 540.26 91.97 540.26H448.29C499 540.26 540.26 499 540.26 448.29V91.97C540.26 81.81 538.61 72.07 535.56 62.9ZM497.92 448.29C497.92 475.7 475.7 497.92 448.29 497.92H91.97C64.56 497.92 42.34 475.7 42.34 448.29V253.52C88.68 318.77 161.79 362.29 243.83 369.91L267.37 371.33C273 371.67 278.64 371.58 284.25 371.05L296.38 369.91C378.43 362.29 451.58 318.77 497.92 253.47V448.28V448.29ZM42.33 91.97C42.33 64.59 64.58 42.34 91.96 42.34H448.28C475.66 42.34 497.91 64.59 497.91 91.97V156.8C469.02 258.23 375.73 328.84 270.12 328.84C164.51 328.84 71.26 258.32 42.33 156.98V91.96V91.97Z"
                      fill="#3B5370"
                    />
                    <path
                      d="M270.13 289.26C296.617 289.26 318.09 267.788 318.09 241.3C318.09 214.812 296.617 193.34 270.13 193.34C243.642 193.34 222.17 214.812 222.17 241.3C222.17 267.788 243.642 289.26 270.13 289.26Z"
                      fill="#E30630"
                    />
                    <path
                      d="M306.38 348.93H233.88V437.22H306.38V348.93Z"
                      fill="#E30630"
                    />
                  </svg>
                </div>
                <div className="checkout-box">
                  <div
                    className={`checkout-title ${deliveryAddress && billingAddress && deliveryMethod ? "" : "m-0"} `}
                  >
                    <h4>EcoWallet</h4>
                  </div>
                  <div
                    className="checkout-detail payment-details mb-3"
                    onClick={!loading ? () => saveData("applyWallet") : null}
                  >
                    <div className="bg-white w-100 d-flex align-items-center justify-content-between p-4">
                      <div className="d-flex align-items-center">
                        <input
                          className="form-check-input my-0"
                          type="checkbox"
                          name="applyWallet"
                          checked={applyWallet}
                          readOnly
                        />
                        <span className="d-block ml-5">
                          -{formatPrice(cartCalculation.wallet)} DH
                        </span>
                      </div>
                      <svg
                        height="25"
                        viewBox="0 0 541 541"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M535.56 62.9V61.77L535.37 62.33C523 26.11 488.65 0 448.29 0H91.97C51.56 0 17.12 26.2 4.84 62.52L4.70001 62.19V62.9C1.64001 72.07 0 81.81 0 91.97V448.29C0 499 41.26 540.26 91.97 540.26H448.29C499 540.26 540.26 499 540.26 448.29V91.97C540.26 81.81 538.61 72.07 535.56 62.9ZM497.92 448.29C497.92 475.7 475.7 497.92 448.29 497.92H91.97C64.56 497.92 42.34 475.7 42.34 448.29V253.52C88.68 318.77 161.79 362.29 243.83 369.91L267.37 371.33C273 371.67 278.64 371.58 284.25 371.05L296.38 369.91C378.43 362.29 451.58 318.77 497.92 253.47V448.28V448.29ZM42.33 91.97C42.33 64.59 64.58 42.34 91.96 42.34H448.28C475.66 42.34 497.91 64.59 497.91 91.97V156.8C469.02 258.23 375.73 328.84 270.12 328.84C164.51 328.84 71.26 258.32 42.33 156.98V91.96V91.97Z"
                          fill="#3B5370"
                        />
                        <path
                          d="M270.13 289.26C296.617 289.26 318.09 267.788 318.09 241.3C318.09 214.812 296.617 193.34 270.13 193.34C243.642 193.34 222.17 214.812 222.17 241.3C222.17 267.788 243.642 289.26 270.13 289.26Z"
                          fill="#E30630"
                        />
                        <path
                          d="M306.38 348.93H233.88V437.22H306.38V348.93Z"
                          fill="#E30630"
                        />
                      </svg>
                      {/* <img
                        alt="ecowallet"
                        src="/assets/images/ecowallet.svg"
                      /> */}
                    </div>
                  </div>
                </div>
              </li>
            )}

            <li
              className={
                cartCalculation.total === 0
                  ? "opacity-50 pe-none user-select-none"
                  : ""
              }
            >
              <div className="checkout-icon">
                <Target />
              </div>
              <div className="checkout-box">
                <div
                  className={`checkout-title ${deliveryAddress && billingAddress && deliveryMethod ? "" : "m-0"} `}
                >
                  <h4>Modalité de paiement</h4>
                </div>

                {deliveryAddress && billingAddress && deliveryMethod ? (
                  <>
                    {authenticated &&
                      currentUser &&
                      currentUser?.type === "seller" && (
                        <>
                          <div
                            className="checkout-detail payment-details mb-3"
                            onClick={
                              !loading ? () => savePayment("cheque") : null
                            }
                          >
                            <div className="bg-white w-100 d-flex align-items-center justify-content-between p-4">
                              <div className="d-flex align-items-center">
                                <input
                                  className="form-check-input my-0"
                                  type="radio"
                                  name="paymentMethod"
                                  id="cheque"
                                  checked={paymentMethod === "cheque"}
                                  readOnly
                                />
                                <span className="d-block ml-5">Cheque</span>
                              </div>
                              <img
                                alt="cmi payment"
                                src="/assets/images/cheque.png"
                              />
                            </div>
                          </div>
                          <div
                            className="checkout-detail payment-details mb-3"
                            onClick={
                              !loading ? () => savePayment("effet") : null
                            }
                          >
                            <div className="bg-white w-100 d-flex align-items-center justify-content-between p-4">
                              <div className="d-flex align-items-center">
                                <input
                                  className="form-check-input my-0"
                                  type="radio"
                                  name="paymentMethod"
                                  id="effet"
                                  checked={paymentMethod === "effet"}
                                  readOnly
                                />
                                <span className="d-block ml-5">Effet</span>
                              </div>
                              <img
                                alt="cmi payment"
                                src="/assets/images/effet.png"
                              />
                            </div>
                          </div>
                        </>
                      )}
                    {/* <div
                      className="checkout-detail payment-details mb-3"
                      onClick={!loading ? () => savePayment("cmi") : null}
                    >
                      <div className="bg-white w-100 d-flex align-items-center justify-content-between p-4">
                        <div className="d-flex align-items-center">
                          <input
                            className="form-check-input my-0"
                            type="radio"
                            name="paymentMethod"
                            id="credit"
                            checked={paymentMethod === "cmi"}
                            readOnly
                          />
                          <span className="d-block ml-5">CMI</span>
                        </div>
                        <img alt="cmi payment" src="/assets/images/cmi.png" />
                      </div>
                    </div> */}
                    <div
                      className="checkout-detail bg-white p-4 payment-details mb-3"
                      onClick={!loading ? () => savePayment("virement") : null}
                    >
                      <div className=" w-100 d-flex align-items-center justify-content-between">
                        <div className="d-flex align-items-center">
                          <input
                            className="form-check-input my-0"
                            type="radio"
                            name="paymentMethod"
                            id="credit"
                            checked={paymentMethod === "virement"}
                            readOnly
                          />
                          <span className="d-block ml-5">
                            Virement bancaire
                          </span>
                        </div>
                        <img
                          alt="virement bancaire"
                          src="/assets/images/virement.png"
                        />
                      </div>

                      {paymentMethod === "virement" && (
                        <div className="mt-3">
                          <div className="form-floating theme-form-floating">
                            <input
                              accept="application/pdf"
                              type="file"
                              className="form-control w-100"
                              id="paymentAttachement"
                              name="paymentAttachement"
                              onChange={(event) => {
                                saveData(
                                  "paymentAttachement",
                                  event.currentTarget.files[0]
                                );
                              }}
                            />
                            <label htmlFor="paymentAttachement">
                              Document de virement(Optionnelle)
                            </label>
                          </div>
                        </div>
                      )}
                    </div>
                    {deliveryMethod === "voie_express" &&
                    cartCalculation.total > 20_000 ? null : (
                      <div
                        className="checkout-detail bg-white p-4 payment-details mb-3"
                        onClick={!loading ? () => savePayment("cod") : null}
                      >
                        <div className=" w-100 d-flex align-items-center justify-content-between">
                          <div className="d-flex align-items-center">
                            <input
                              className="form-check-input my-0"
                              type="radio"
                              name="paymentMethod"
                              id="credit"
                              checked={paymentMethod === "cod"}
                              readOnly
                            />
                            <span className="d-block ml-5">
                              COD (Paiement à la livraison)
                            </span>
                          </div>
                          <img alt="cod" src="/assets/images/cod.png" />
                        </div>
                      </div>
                    )}
                    {/* <div
                      className="checkout-detail opacity-75 pe-none bg-white p-4 payment-details mb-3"
                      // onClick={!loading ? () => savePayment("cod") : null}
                    >
                      <div className=" w-100 d-flex align-items-center justify-content-between">
                        <div className="d-flex align-items-center">
                          <input
                            className="form-check-input my-0"
                            type="radio"
                            name="paymentMethod"
                            id="credit"
                            // checked={paymentMethod === "cod"}
                            readOnly
                            disabled
                          />
                          <span className="d-block ml-5">Bank al yousr</span>
                        </div>
                        <img alt="cod" src="/assets/images/bank-alyousr.png" />
                      </div>
                    </div>
                    <div
                      className="checkout-detail opacity-75 bg-white pe-none p-4 payment-details"
                      // onClick={!loading ? () => savePayment("cod") : null}
                    >
                      <div className=" w-100 d-flex align-items-center justify-content-between">
                        <div className="d-flex align-items-center">
                          <input
                            className="form-check-input my-0"
                            type="radio"
                            name="paymentMethod"
                            id="credit"
                            // checked={paymentMethod === "cod"}
                            readOnly
                            disabled
                          />
                          <span className="d-block ml-5">WafaSalaf</span>
                        </div>
                        <img alt="cod" src="/assets/images/wafa-salaf.jpg" />
                      </div>
                    </div> */}
                  </>
                ) : (
                  <div />
                )}
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
