"use client";

import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";
import { formatPrice } from "@/lib/helpers";
import { applyCoupon } from "@/queries/queries";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { useContext, useState } from "react";
import { X } from "react-feather";
import { TailSpin } from "react-loader-spinner";
import { useMutation } from "react-query";

export default function RightSide({
  cartCalculation,
  orderSuccess,
  orderError,
  loading,
  handleSubmitting,
  applyWallet,
  enableCoupon,
  coupon,
  checkoutPassed,
}) {
  const [couponCode, setCouponCode] = useState("");
  const { cartItems } = useContext(CartAndWishlistProvider);

  const {
    mutate: applyCouponMutation,
    isLoading: isApplyingCoupon,
    error,
  } = useMutation({
    mutationFn: applyCoupon,
    onSuccess(data) {
      enableCoupon(data);
    },
  });

  const handleApplyCouponCode = (e) => {
    e.preventDefault();

    if (coupon) {
      enableCoupon(null);
      setCouponCode("");
    } else if (couponCode) {
      applyCouponMutation({
        coupon: couponCode,
        total: cartCalculation?.subtotal,
      });
    }
  };

  return (
    <div>
      <div className="right-side-summery-box">
        <div className="summery-box-2">
          <div className="summery-header">
            <h3>Résumé de la commande</h3>
          </div>

          <ul className="summery-contain">
            {cartItems.map((item, key) => (
              <>
                <li key={`checkout-cart-item-${key}`}>
                  <div className="summery-product-info">
                    <img
                      src={item?.image_link}
                      className="img-fluid blur-up lazyloaded checkout-image"
                      alt={item?.name}
                    />
                    <h4>
                      {item?.name} <span>X {item?.quantity}</span>
                    </h4>
                  </div>
                  {item?.discount && item?.new_price ? (
                    <h4 className="price">
                      {formatPrice(item?.total)} DH{" "}
                      <del className="text-content">
                        -{item?.new_price?.discount}%
                      </del>
                    </h4>
                  ) : item?.is_best_offer ? (
                    <h4 className="price">{formatPrice(item?.total)} DH </h4>
                  ) : (
                    <h4 className="price">{formatPrice(item?.total)} DH</h4>
                  )}
                </li>
              </>
            ))}
          </ul>

          {cartCalculation && (
            <ul className="summery-total">
              <li>
                <h4>Sous total</h4>
                <h4 className="price text-content">
                  {formatPrice(cartCalculation?.subtotal)} DH TTC
                </h4>
              </li>

              <li>
                <h4>Expédition</h4>
                <h4 className="price text-content">
                  {formatPrice(cartCalculation?.shipping_cost)} DH TTC
                </h4>
              </li>

              {cartCalculation?.wallet > 0 && applyWallet && (
                <li>
                  <h4 style={{ color: "#ff6b6b" }} className="font-bold">
                    Ecowallet
                  </h4>
                  <h4
                    className="price text-content"
                    style={{ color: "#ff6b6b" }}
                  >
                    - {formatPrice(cartCalculation.wallet)} DH TTC
                  </h4>
                </li>
              )}

              {coupon?.code && coupon?.amount > 0 && (
                <li>
                  <h4 style={{ color: "#ff6b6b" }} className="font-bold">
                    Code promo
                  </h4>
                  <h4
                    className="price text-content"
                    style={{ color: "#ff6b6b" }}
                  >
                    - {formatPrice(coupon.amount)} DH TTC
                  </h4>
                </li>
              )}

              <li className="list-total mt-2">
                <h4>Total</h4>
                <h4 className="price">
                  {formatPrice(cartCalculation?.total - (coupon?.amount ?? 0))}
                  DH TTC
                </h4>
              </li>
            </ul>
          )}
        </div>
        <div className="summery-box mt-2">
          <div className="summery-contain">
            <div class="coupon-cart">
              <h6 class="text-content mb-2">Code promo</h6>
              <form onSubmit={handleApplyCouponCode}>
                <div
                  class={`mb-3 coupon-box input-group ${isApplyingCoupon || coupon ? "disabled" : ""}`}
                >
                  <input
                    value={couponCode}
                    onChange={(e) => setCouponCode(e.target.value)}
                    type="text"
                    class="form-control"
                    placeholder="Entrez votre code promo ici..."
                    disabled={isApplyingCoupon || coupon}
                  />
                  <button class="btn-apply" disabled={isApplyingCoupon}>
                    {coupon?.code ? <X /> : "Appliquer"}
                  </button>
                </div>
              </form>
            </div>
            {coupon?.code && (
              <SuccessSnackbar
                secondary
                message={
                  <span className="text-white">
                    Vous avez appliqué le code promo{" "}
                    <strong>{coupon?.code}</strong> 🎉
                  </span>
                }
              />
            )}
            {error && (
              <ErrorSnackbar message={error?.response?.data?.message} />
            )}
          </div>
        </div>

        <button
          type="button"
          className="btn theme-bg-color text-white btn-md w-100 mt-4 fw-bold"
          onClick={handleSubmitting}
          disabled={loading || !checkoutPassed}
        >
          {loading ? (
            <TailSpin color="#fff" height={16} width={16} visible={loading} />
          ) : (
            "Valider la Commande"
          )}
        </button>

        {orderError && <ErrorSnackbar message={orderError} />}
        {orderSuccess && <SuccessSnackbar message={orderSuccess} />}
      </div>
    </div>
  );
}
