"use client";

import Link from "next/link";
import { useContext, useEffect, useState } from "react";

import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";
import { useAuthContext } from "@/providers/auth-provider";
import {
  AuthCheckout,
  getCalculatedDeliveryAuth,
  getCalculatedDeliveryGuest,
  GuestCheckout,
} from "@/queries/queries";
import Loading from "@/shared/components/loading";
import { useRouter } from "next/navigation";
import { AlertTriangle } from "react-feather";
import LeftSide from "./left-side";
import RightSide from "./right-side";

export default function Checkout() {
  const router = useRouter();
  const { authenticated, currentUser } = useAuthContext();
  const {
    cartItems,
    cartCalculation,
    clearGuestCartItem,
    getCartItemsGuestLoading,
  } = useContext(CartAndWishlistProvider);
  const [loading, setLoading] = useState(false);
  const [orderSuccess, setOrderSuccess] = useState(null);
  const [orderError, setOrderError] = useState(null);
  const [orderID, setOrderID] = useState(null);
  const [redirect, setRedirect] = useState(null);

  const [paymentMethod, setPaymentMethod] = useState(null);
  const [paymentAttachement, setPaymentAttachement] = useState(null);
  const [deliveryMethod, setDeliveryMethod] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [deliveryAddress, setDeliveryAddress] = useState(null);
  const [billingAddress, setBillingAddress] = useState(null);
  const [coupon, setCoupon] = useState(null);
  const [applyWallet, setApplyWallet] = useState(false);
  const [wrapcartCalculation, setWrapCartCalculation] =
    useState(cartCalculation);

  useEffect(() => {
    if (orderID) {
      const timer = setTimeout(() => {
        if (orderID && redirect) {
          if (!authenticated) {
            clearGuestCartItem();
          }
          window.location.href = `${process.env.NEXT_PUBLIC_BACKEND_URL}/complete-payment/${orderID}`;
          // window.location.href = 'https://dev.ecowatt.ma/complete-payment/' + orderID
        } else {
          //   history.push({
          //     // no need
          //     pathname: "/order-success",
          //     state: { paid: paymentMethod },
          //   });
          router.push("/order-success");
        }
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [redirect]);

  useEffect(() => {
    if (deliveryAddress && deliveryMethod) {
      if (authenticated) {
        getCalculatedDeliveryAuth(
          deliveryAddress,
          deliveryMethod,
          applyWallet,
          paymentMethod
        ).then(function (response) {
          setWrapCartCalculation({
            subtotal: response?.subtotal ?? 0,
            discount: response?.discount ?? 0,
            shipping_cost: response?.shipping_cost ?? 0,
            coupon_cost: response?.coupon_cost ?? 0,
            total: response?.total ?? 0,
            wallet: response?.wallet ?? 0,
          });
          if (response?.total === 0) setPaymentMethod("wallet");
        });
      } else {
        getCalculatedDeliveryGuest(
          deliveryAddress?.city,
          deliveryMethod,
          paymentMethod
        ).then(function (response) {
          setWrapCartCalculation({
            subtotal: response?.subtotal ?? 0,
            discount: response?.discount ?? 0,
            shipping_cost: response?.shipping_cost ?? 0,
            coupon_cost: response?.coupon_cost ?? 0,
            total: response?.total ?? 0,
          });
        });
      }
    } else if (deliveryMethod === "in_place") {
      setWrapCartCalculation(cartCalculation);
    }
  }, [deliveryAddress, deliveryMethod, paymentMethod, applyWallet]);

  const saveData = async (type, data) => {
    switch (type) {
      case "customer":
        setCustomer(data);
        break;
      case "delivery":
        setDeliveryAddress(data);
        break;
      case "billing":
        setBillingAddress(data);
        break;
      case "deliveryMethod":
        setDeliveryMethod(data);
        break;
      case "applyWallet":
        setApplyWallet((apply) => !apply);
        break;
      case "payment":
        if (data !== "virement" && paymentAttachement) {
          setPaymentAttachement(null);
        }
        setPaymentMethod(data);
        break;
      case "paymentAttachement":
        setPaymentAttachement(data);
        break;
      default:
      // console.log('Someting went wrong on save data')
    }

    if (orderSuccess) setOrderSuccess(null);
    if (orderError) setOrderError(null);
    if (loading) setLoading(false);
  };

  const enableCoupon = (coupon) => {
    setCoupon(coupon);
  };

  const handleSubmitOrder = async () => {
    if (orderSuccess) setOrderSuccess(null);
    if (orderError) setOrderError(null);

    setLoading(true);
    if (authenticated) {
      if (
        !paymentMethod ||
        !deliveryAddress ||
        !billingAddress ||
        !deliveryMethod
      ) {
        setOrderError("Veuillez suivre toutes les étapes avant de soumettre");
        setLoading(false);
        return false;
      }

      try {
        const res = await AuthCheckout({
          payment_method: paymentMethod,
          payment_attachement: paymentAttachement,
          delivery_address: deliveryAddress,
          billing_address: billingAddress,
          delivery_method: deliveryMethod,
          apply_wallet: applyWallet ? 1 : 0,
          coupon: coupon?.code,
        });
        if (res.status && res?.order_id) {
          // setLoading(false)
          setRedirect(res?.continue_payment);
          setOrderSuccess("Commande enregistrée avec succès");
          setOrderID(res?.order_id);
        } else {
          setLoading(false);
          setOrderError("La commande a échoué");
        }
      } catch (error) {
        setLoading(false);
        setOrderError(error?.response?.data?.message);
      }
    } else {
      // || !billingAddress
      if (!paymentMethod || !deliveryAddress || !customer || !deliveryMethod) {
        setOrderError("Veuillez suivre toutes les étapes avant de soumettre");
        setLoading(false);
        return false;
      }

      try {
        const res = await GuestCheckout({
          payment_method: paymentMethod,
          payment_attachement: paymentAttachement,
          delivery_address: deliveryAddress,
          delivery_method: deliveryMethod,
          customer: customer,
          coupon: coupon?.code,
        });
        if (res.status && res?.order_id) {
          // setLoading(false)
          setRedirect(res?.continue_payment);
          setOrderSuccess("Commande enregistrée avec succès");
          setOrderID(res?.order_id);
        } else {
          setLoading(false);
          setOrderError("La commande a échoué");
        }
      } catch (error) {
        setLoading(false);
        setOrderError(error?.response?.data?.message);
      }
    }
  };

  if (getCartItemsGuestLoading) {
    return <Loading />;
  }

  if (!cartItems || cartItems.length < 1 || !wrapcartCalculation) {
    router.replace("/cart");
    return null;
  }

  const checkoutPassed = authenticated
    ? paymentMethod && deliveryAddress && billingAddress && deliveryMethod
    : paymentMethod && deliveryAddress && customer && deliveryMethod;

  return (
    <section className="checkout-section-2 section-b-space">
      <div className="container-lg">
        {authenticated &&
        currentUser &&
        currentUser?.type === "seller" &&
        currentUser?.status === 1 ? (
          <div className="text-center my-5 py-3 px-0 row justify-content-center">
            <div className="col-12 col-md-7">
              <AlertTriangle
                className="text-warning mb-4 text-lg"
                style={{ width: 100, height: 100 }}
              />
              <h3 className="fs-3 mb-1">
                Votre Compte n'est pas encore validé.
              </h3>
              <h3 className="fs-3 mb-4">
                Vous ne pouvez pas encore faire des commandes.
              </h3>
              <Link
                className="btn btn-animation proceed-btn d-inline-block fw-bold"
                href={`/`}
              >
                <i className="fa-solid fa-arrow-left-long"></i>&nbsp; Retour a
                l'accueil
              </Link>
            </div>
          </div>
        ) : (
          <div className="row g-sm-4 g-3">
            <LeftSide
              deliveryAddress={deliveryAddress}
              billingAddress={billingAddress}
              saveData={saveData}
              paymentMethod={paymentMethod}
              deliveryMethod={deliveryMethod}
              loading={loading}
              cartCalculation={wrapcartCalculation}
              applyWallet={applyWallet}
            />
            <RightSide
              orderSuccess={orderSuccess}
              orderError={orderError}
              loading={loading}
              handleSubmitting={handleSubmitOrder}
              cartCalculation={wrapcartCalculation}
              applyWallet={applyWallet}
              enableCoupon={enableCoupon}
              coupon={coupon}
              checkoutPassed={checkoutPassed}
            />
          </div>
        )}
      </div>
    </section>
  );
}
