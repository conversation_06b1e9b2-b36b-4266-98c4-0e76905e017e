"use client";

import { useFormik } from "formik";
import { X } from "react-feather";
import { useQueryClient } from "react-query";
import { number, object, string } from "yup";

import { useCountries, useCountryCities } from "@/services/common";
import { useCreateAddressMutation } from "@/services/customer";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessAnimation from "@/shared/components/success-animation";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { TailSpin } from "react-loader-spinner";

export default function AddAddress({
  type,
  modelClose = null,
  isAuthenticated = null,
  saveAddress = null,
}) {
  const queryClient = useQueryClient();
  const { countries } = useCountries();

  const { createAddress, isLoading, isSuccess, error } =
    useCreateAddressMutation();

  const genInitialValues = {
    line_1: "",
    line_2: "",
    country: "",
    city: "",
  };

  async function onSubmit(values, actions) {
    if (!isAuthenticated) {
      saveAddress?.(values);
      return;
    }

    createAddress(
      {
        ...values,
        type,
      },
      {
        onSuccess() {
          actions.resetForm({
            values: genInitialValues,
          });
          queryClient.invalidateQueries("addresses");
        },
      }
    );
  }

  const addressFromik = useFormik({
    initialValues: genInitialValues,
    validationSchema: object({
      line_1: string()
        .min(1, "Trop court!")
        .max(10000, "Trop long!")
        .required("Ce champ est obligatoire"),
      line_2: string()
        .min(1, "Trop court!")
        .max(10000, "Trop long!")
        .notRequired(),
      country: number().required("Ce champ est obligatoire"),
      city: number().required("Ce champ est obligatoire"),
    }),
    onSubmit,
  });

  const { values, errors, touched, handleChange, handleBlur, handleSubmit } =
    addressFromik;

  const { cities } = useCountryCities(values.country);

  return (
    <div className="checkout-add-address">
      {error && <ErrorSnackbar message={error.message} />}
      {isSuccess && <SuccessSnackbar message="L'adresse a été enregistrée" />}
      {isSuccess && isAuthenticated ? (
        <SuccessAnimation />
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            <div className="row g-4">
              <div className="col-12 col-md-6">
                <div className="form-floating theme-form-floating">
                  <input
                    type="text"
                    className="form-control"
                    id="line_1"
                    name="line_1"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={values.line_1}
                  />
                  <label htmlFor="line_1">Ligne 1</label>
                </div>
                <span className="error-form">
                  {errors.line_1 && touched.line_1 && errors.line_1}
                </span>
              </div>
              <div className="col-12 col-md-6">
                <div className="form-floating theme-form-floating">
                  <input
                    type="text"
                    className="form-control"
                    id="line_2"
                    name="line_2"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={values.line_2}
                  />
                  <label htmlFor="line_2">Ligne 2</label>
                </div>
                <span className="error-form">
                  {errors.line_2 && touched.line_2 && errors.line_2}
                </span>
              </div>

              <div className="col-12 col-md-6">
                <div className="form-floating theme-form-floating">
                  <select
                    value={values.country}
                    className="form-control"
                    id="country"
                    name="country"
                    onChange={handleChange}
                    onBlur={handleBlur}
                  >
                    <option value="0" label="Choisissez un pays">
                      Choisissez un pays
                    </option>
                    {countries &&
                      countries.length &&
                      countries.map((item, key) => (
                        <option key={key} value={item?.id} label={item?.name}>
                          {item?.name}
                        </option>
                      ))}
                  </select>
                </div>
                <span className="error-form">
                  {errors.country && touched.country && errors.country}
                </span>
              </div>

              <div className="col-12 col-md-6">
                <div className="form-floating theme-form-floating">
                  <select
                    value={values.city}
                    className="form-control"
                    id="city"
                    name="city"
                    onChange={handleChange}
                    onBlur={handleBlur}
                  >
                    <option value="0" label="Sélectionnez une ville">
                      Sélectionnez une ville
                    </option>
                    {cities
                      ?.sort((a, b) => (a?.name > b?.name ? 1 : -1))
                      .map((city) => (
                        <option key={city.id} value={city.id} label={city.name}>
                          {city.name}
                        </option>
                      ))}
                  </select>
                </div>
                <span className="error-form">
                  {errors.city && touched.city && errors.city}
                </span>
              </div>
            </div>
          </div>
          <div className="modal-footer">
            <button
              disabled={isLoading}
              className="btn theme-bg-color btn-md fw-bold text-light"
              type="submit"
            >
              {isLoading ? (
                <TailSpin
                  type="ThreeDots"
                  color="#fff"
                  height={20}
                  width={20}
                />
              ) : (
                "Sauvegarder"
              )}
            </button>
          </div>
          {modelClose && (
            <div className="close-model">
              <X onClick={modelClose} />
            </div>
          )}
        </form>
      )}
    </div>
  );
}
