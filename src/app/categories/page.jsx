import BrandBox from "@/components/brand-box";
import CategoryBox from "@/components/category-box";
import ProductBox from "@/components/product-box";
import SectionTitle from "@/components/section-title";
import { CommonService } from "@/services/common";

const Icon = "https://via.placeholder.com/50x50";

export default async function CategoriesPage() {
  const featuredCategories = await CommonService.getFeaturedCategories();
  const featuredBrands = await CommonService.getFeaturedBrands();
  const bestOffers = await CommonService.getBestOffers();

  return (
    <div className="container-lg space-y-20 pb-40">
      <section>
        <SectionTitle title="Nos catégories" />

        <div>
          <ul className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {featuredCategories.map((category) => (
              <li key={category.id}>
                <CategoryBox category={category} />
              </li>
            ))}
          </ul>
        </div>
      </section>

      <section>
        <SectionTitle title="Nos marques" />

        <div>
          <ul className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {featuredBrands.map((brand) => (
              <li key={brand.id}>
                <BrandBox brand={brand} />
              </li>
            ))}
          </ul>
        </div>
      </section>

      <section>
        <SectionTitle title="Nos bons plans" />

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-x-2.5 gap-y-6">
          {bestOffers?.map((product) => (
            <ProductBox key={product.id} product={product} />
          ))}
        </div>
      </section>
    </div>
  );
}
