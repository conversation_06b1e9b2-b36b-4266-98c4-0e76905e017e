"use client";

import { useResetPasswordMutation } from "@/services/auth";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { Formik } from "formik";
import { useSearchParams } from "next/navigation";
import { Tail<PERSON>pin } from "react-loader-spinner";
import { object, ref, string } from "yup";

const formSchema = object({
  password: string()
    .required("Ce champ est obligatoire")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!+@#_\$%\^&\*])(?=.{8,})/,
      "Doit contenir 8 caractères, une majuscule, une minuscule, un chiffre et une casse spéciale"
    ),
  password_confirmation: string()
    .required("Ce champ est obligatoire")
    .oneOf([ref("password"), null], "Les mots de passe ne correspondent pas."),
});

const genInitialValues = () => ({
  password: "",
  password_confirmation: "",
});

export default function ResetPasswordForm() {
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const { resetPassword, isLoading, isSuccess, error } =
    useResetPasswordMutation();

  async function onSubmit(data, actions) {
    resetPassword(
      {
        ...data,
        token,
      },
      {
        onSuccess() {
          window.location.href = "/login";
        },
      }
    );
  }

  return (
    <section className="log-in-section section-b-space forgot-section">
      <div className="container-lg w-100">
        <div className="row">
          <div className="col-xxl-6 col-xl-5 col-lg-6 d-lg-block d-none ms-auto">
            <div className="image-contain">
              <img
                src="/assets/images/reset-password.svg"
                className="img-fluid"
                alt=""
              />
            </div>
          </div>

          <div className="col-xxl-4 col-xl-5 col-lg-6 col-sm-8 mx-auto">
            <div className="d-flex align-items-center justify-content-center h-100">
              <div className="log-in-box">
                <div className="log-in-title mb-4">
                  <h3>Bienvenue chez Ecowatt</h3>
                  <h4>Réinitialiser le mot de passe</h4>
                </div>

                {error && <ErrorSnackbar message={error.message} />}

                <div className="input-box">
                  <Formik
                    initialValues={genInitialValues()}
                    validationSchema={formSchema}
                    onSubmit={onSubmit}
                  >
                    {({
                      values,
                      errors,
                      touched,
                      handleChange,
                      handleBlur,
                      handleSubmit,
                    }) => (
                      <form onSubmit={handleSubmit} className="row g-4">
                        <div className="col-12">
                          <div className="form-floating theme-form-floating log-in-form">
                            <input
                              type="password"
                              className="form-control"
                              id="password"
                              name="password"
                              onChange={handleChange}
                              onBlur={handleBlur}
                              value={values.password}
                            />
                            <label htmlFor="password">Mot de passe</label>
                          </div>
                          <span className="error-form">
                            {errors.password &&
                              touched.password &&
                              errors.password}
                          </span>
                        </div>
                        <div className="col-12">
                          <div className="form-floating theme-form-floating log-in-form">
                            <input
                              type="password"
                              className="form-control"
                              id="password_confirmation"
                              name="password_confirmation"
                              onChange={handleChange}
                              onBlur={handleBlur}
                              value={values.password_confirmation}
                            />
                            <label htmlFor="password_confirmation">
                              Confirmer le Mot de passe
                            </label>
                          </div>
                          <span className="error-form">
                            {errors.password_confirmation &&
                              touched.password_confirmation &&
                              errors.password_confirmation}
                          </span>
                        </div>

                        <div className="col-12">
                          <button
                            disabled={isLoading}
                            className="btn btn-animation w-100 justify-content-center"
                            type="submit"
                          >
                            {isLoading ? (
                              <TailSpin
                                type="ThreeDots"
                                color="#fff"
                                height={20}
                                width={20}
                              />
                            ) : (
                              "Réinitialiser le mot de passe"
                            )}
                          </button>
                        </div>
                      </form>
                    )}
                  </Formik>

                  {isSuccess && (
                    <SuccessSnackbar
                      message={`Votre mot de passe a été réinitialisé`}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
