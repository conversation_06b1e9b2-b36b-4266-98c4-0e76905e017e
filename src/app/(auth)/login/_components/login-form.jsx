"use client";

import { useLoginMutation } from "@/services/auth";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import { Formik } from "formik";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import { Eye, EyeOff } from "react-feather";
import { TailSpin } from "react-loader-spinner";
import { object, string } from "yup";

const formSchema = object({
  email: string()
    .email("Adresse e-mail non valide")
    .required("Ce champ est obligatoire"),
  password: string(),
});

const genInitialValues = () => ({
  email: "",
  password: "",
});

export default function LoginForm() {
  const searchParams = useSearchParams();
  const redirect = searchParams.get("redirect");
  const [passwordStatus, setPasswordStatus] = useState(false);

  const { login, isLoading, error } = useLoginMutation();

  function togglePasswordStatus() {
    setPasswordStatus(!passwordStatus);
  }

  function onSubmit(data) {
    login(data, {
      onSuccess(data) {
        if (data.status) {
          window.location.href = redirect ?? "/";
        }
      },
    });
  }

  return (
    <section className="log-in-section background-image-2 section-b-space">
      <div className="container-lg w-100">
        <div className="row">
          <div className="col-xxl-6 col-xl-5 col-lg-6 d-lg-block d-none ms-auto">
            <div className="image-contain">
              <img
                src="/assets/images/login.svg"
                className="img-fluid"
                alt=""
              />
            </div>
          </div>

          <div className="col-xxl-4 col-xl-5 col-lg-6 col-sm-8 mx-auto">
            <div className="log-in-box">
              <div className="log-in-title mb-4">
                <h3>Bienvenue chez Ecowatt</h3>
                <h4>Connectez-vous à votre compte</h4>
              </div>

              {error && <ErrorSnackbar message={error?.message} />}

              <div className="input-box">
                <Formik
                  initialValues={genInitialValues()}
                  validationSchema={formSchema}
                  onSubmit={onSubmit}
                >
                  {({
                    values,
                    errors,
                    touched,
                    handleChange,
                    handleBlur,
                    handleSubmit,
                  }) => (
                    <form onSubmit={handleSubmit} className="row g-4">
                      <div className="col-12">
                        <div className="form-floating theme-form-floating log-in-form">
                          <input
                            type="email"
                            className="form-control"
                            id="email"
                            name="email"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.email}
                          />
                          <label htmlFor="email">Adresse e-mail</label>
                        </div>
                        <span className="error-form">
                          {errors.email && touched.email && errors.email}
                        </span>
                      </div>

                      <div className="col-12 form-group-password">
                        <div className="form-floating theme-form-floating log-in-form">
                          <input
                            type={passwordStatus ? "text" : "password"}
                            className="form-control"
                            id="password"
                            name="password"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.password}
                          />
                          <label htmlFor="password">Mot de passe</label>
                          {passwordStatus ? (
                            <Eye onClick={togglePasswordStatus} />
                          ) : (
                            <EyeOff onClick={togglePasswordStatus} />
                          )}
                        </div>
                        <span className="error-form">
                          {errors.password &&
                            touched.password &&
                            errors.password}
                        </span>
                      </div>

                      <div className="col-12">
                        <div className="forgot-box">
                          <div className="form-check ps-0 m-0 remember-box">
                            <input
                              className="checkbox_animated check-box"
                              type="checkbox"
                              id="flexCheckDefault"
                            />
                            <label
                              className="form-check-label"
                              htmlFor="flexCheckDefault"
                            >
                              Souviens-toi de moi
                            </label>
                          </div>
                          <Link
                            href="/forgot-password"
                            className="forgot-password"
                          >
                            Mot de passe oublié?
                          </Link>
                        </div>
                      </div>

                      <div className="col-12">
                        <button
                          disabled={isLoading}
                          className="btn btn-animation w-100 justify-content-center"
                          type="submit"
                        >
                          {isLoading ? (
                            <TailSpin
                              type="ThreeDots"
                              color="#fff"
                              height={20}
                              width={20}
                            />
                          ) : (
                            "Se connecter"
                          )}
                        </button>
                      </div>
                    </form>
                  )}
                </Formik>
              </div>
              <div className="sign-up-box">
                <h4>Je n'ai pas de compte?</h4>
                <Link href={"/register"}>S'inscrire</Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
