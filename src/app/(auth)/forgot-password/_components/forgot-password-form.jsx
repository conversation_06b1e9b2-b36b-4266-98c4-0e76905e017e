"use client";

import { useRequestPasswordResetMutation } from "@/services/auth";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { Formik } from "formik";
import { Tail<PERSON>pin } from "react-loader-spinner";
import { object, string } from "yup";

const formSchema = object({
  email: string()
    .email("Adresse e-mail non valide")
    .required("Ce champ est obligatoire"),
});

const genInitialValues = () => ({
  email: "",
});

export default function ForgotPasswordForm() {
  const { requestPasswordReset, isLoading, isSuccess, error } =
    useRequestPasswordResetMutation();

  async function onSubmit(data, actions) {
    requestPasswordReset(data, {
      onSuccess() {
        actions.resetForm({
          values: genInitialValues(),
        });
      },
    });
  }

  return (
    <section className="log-in-section section-b-space forgot-section">
      <div className="container-lg w-100">
        <div className="row">
          <div className="col-xxl-6 col-xl-5 col-lg-6 d-lg-block d-none ms-auto">
            <div className="image-contain">
              <img
                src="/assets/images/reset-password.svg"
                className="img-fluid"
                alt=""
              />
            </div>
          </div>

          <div className="col-xxl-4 col-xl-5 col-lg-6 col-sm-8 mx-auto">
            <div className="d-flex align-items-center justify-content-center h-100">
              <div className="log-in-box">
                <div className="log-in-title mb-4">
                  <h3>Bienvenue chez Ecowatt</h3>
                  <h4>Mot de passe oublié</h4>
                </div>

                {error && <ErrorSnackbar message={error.message} />}

                <div className="input-box">
                  <Formik
                    initialValues={genInitialValues()}
                    validationSchema={formSchema}
                    onSubmit={onSubmit}
                  >
                    {({
                      values,
                      errors,
                      touched,
                      handleChange,
                      handleBlur,
                      handleSubmit,
                    }) => (
                      <form onSubmit={handleSubmit} className="row g-4">
                        <div className="col-12">
                          <div className="form-floating theme-form-floating log-in-form">
                            <input
                              className="form-control"
                              id="email"
                              name="email"
                              onChange={handleChange}
                              onBlur={handleBlur}
                              value={values.email}
                            />
                            <label htmlFor="email">Adresse e-mail</label>
                          </div>
                          <span className="error-form">
                            {errors.email && touched.email && errors.email}
                          </span>
                        </div>

                        <div className="col-12">
                          <button
                            disabled={isLoading}
                            className="btn btn-animation w-100 justify-content-center"
                            type="submit"
                          >
                            {isLoading ? (
                              <TailSpin
                                type="ThreeDots"
                                color="#fff"
                                height={20}
                                width={20}
                              />
                            ) : (
                              "Mot de passe oublié"
                            )}
                          </button>
                        </div>
                      </form>
                    )}
                  </Formik>

                  {isSuccess && (
                    <SuccessSnackbar message={`Vérifiez votre E-mail`} />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
