"use client";

import { Formik } from "formik";
import Link from "next/link";
import React, { useState } from "react";
import { TailSpin } from "react-loader-spinner";
import { mixed, object, string } from "yup";

import { useSettingsContext } from "@/providers/settings-provider";
import { useRegisterMutation } from "@/services/auth";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import { useSearchParams } from "next/navigation";
import { Eye, EyeOff } from "react-feather";

const SUPPORTED_FORMATS = ["application/pdf"];
const FILE_SIZE = 1024 * 2048;

const fomSchema = object({
  fname: string()
    .min(1, "Trop court!")
    .max(191, "Trop long!")
    .required("Ce champ est obligatoire"),
  lname: string()
    .min(1, "Trop court!")
    .max(191, "Trop long!")
    .required("Ce champ est obligatoire"),
  email: string()
    .email("Adresse e-mail non valide")
    .required("Ce champ est obligatoire"),
  type: string()
    .oneOf(["individual", "professional", "seller"])
    .defined()
    .required("Ce champ est obligatoire"),
  mobile: string()
    .required("Ce champ est obligatoire")
    .matches(
      /^[0-9]{9}?$/,
      "Le numéro de téléphone doit être composé de 9 chiffres et respecter ce format: 601020304"
    ),
  password: string()
    .required("Ce champ est obligatoire")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!+@#_\$%\^&\*])(?=.{8,})/,
      "Doit contenir 8 caractères, une majuscule, une minuscule, un chiffre et une casse spéciale"
    ),
  referrer_code: string(),
  rc: string().when("type", {
    is: (val) => ["professional", "seller"].includes(val),
    then: (schema) => schema.required("Ce champ est obligatoire"),
  }),
  ice: string().when("type", {
    is: (val) => ["professional", "seller"].includes(val),
    then: (schema) => schema.required("Ce champ est obligatoire"),
  }),
  rc_file: mixed().when("type", {
    is: (val) => val === "seller",
    then: (schema) =>
      schema
        .required("Ce champ est obligatoire")
        .test(
          "FILE_SIZE",
          "Le fichier téléchargé est trop volumineux.",
          (value) => !value || (value && value.size <= FILE_SIZE)
        )
        .test(
          "FILE_FORMAT",
          "Le fichier téléchargé a un format non pris en charge.",
          (value) => !value || (value && SUPPORTED_FORMATS.includes(value.type))
        ),
  }),
  opening_form_file: mixed().when("type", {
    is: (val) => val === "seller",
    then: (schema) =>
      schema
        .required("Ce champ est obligatoire")
        .test(
          "FILE_SIZE",
          "Le fichier téléchargé est trop volumineux.",
          (value) => !value || (value && value.size <= FILE_SIZE)
        )
        .test(
          "FILE_FORMAT",
          "Le fichier téléchargé a un format non pris en charge.",
          (value) => !value || (value && SUPPORTED_FORMATS.includes(value.type))
        ),
  }),
  cin_file: mixed().when("type", {
    is: (val) => val === "seller",
    then: (schema) =>
      schema
        .required("Ce champ est obligatoire")
        .test(
          "FILE_SIZE",
          "Le fichier téléchargé est trop volumineux.",
          (value) => !value || (value && value.size <= FILE_SIZE)
        )
        .test(
          "FILE_FORMAT",
          "Le fichier téléchargé a un format non pris en charge.",
          (value) => !value || (value && SUPPORTED_FORMATS.includes(value.type))
        ),
  }),
});

const genInitialValues = () => ({
  fname: "",
  lname: "",
  type: "individual",
  email: "",
  mobile: "",
  password: "",
  referrer_code: "",
  rc: "",
  ice: "",
  rc_file: "",
  opening_form_file: "",
  cin_file: "",
});

export default function RegisterForm() {
  const searchParams = useSearchParams();
  const redirect = searchParams.get("redirect");
  const settings = useSettingsContext();
  const [passwordStatus, setPasswordStatus] = React.useState(false);
  const [active, setActive] = useState("particluer");

  const { register, isLoading, error } = useRegisterMutation();

  function togglePasswordStatus() {
    setPasswordStatus(!passwordStatus);
  }

  function onSubmit(data) {
    register(data, {
      onSuccess(data) {
        if (data.status) {
          window.location.href = redirect ?? "/";
        }
      },
    });
  }

  return (
    <section className="log-in-section section-b-space">
      <div className="container-lg w-100">
        <div className="row">
          {active === "seller" ? (
            <div className="col-xxl-6 col-xl-5 col-lg-6 d-lg-block d-none ms-auto">
              <p>
                Le Client Professionnel (B2B) : L'Expertise au Service de la
                Performance
              </p>
              <p>
                Les clients professionnels B2B qui choisissent Ecowatt E-Shop
                sont accueillis avec des avantages qui renforcent leur quête
                d'efficacité et de résultats:
              </p>
              <p>
                1. Tarifs Spécialisés : Les clients professionnels bénéficient
                de tarifs adaptés à leurs besoins commerciaux, garantissant une
                rentabilité accrue.
              </p>
              <p>
                2. Assistance Dédiée : Un support personnalisé est à la
                disposition des clients professionnels pour répondre à leurs
                questions et résoudre leurs problèmes rapidement.
              </p>
              <p>
                3. Gestion de Compte Centralisée : Pour les entreprises avec
                plusieurs collaborateurs, la gestion centralisée du compte
                facilite les achats et le suivi des dépenses.
              </p>
              <p>
                4. Commandes en Gros Simplifiées : Ecowatt E-Shop facilite les
                achats en gros, optimisant ainsi le processus
                d'approvisionnement.
              </p>
              <p>
                Dans le paysage varié de Ecowatt E-Shop, chaque client trouve sa
                place, enveloppé dans des avantages taillés sur mesure. Que vous
                soyez un Passager à la recherche d'efficacité, un Client
                Privilégié en quête d'exclusivité ou un Client Professionnel B2B
                en quête de performance, notre engagement est de vous offrir une
                expérience d'achat empreinte de satisfaction, de qualité et de
                pertinence.
              </p>

              {settings && settings?.opening_file && (
                <a
                  href={settings?.opening_file}
                  className="btn deal-button seller-document"
                  rel="noopener noreferrer"
                  target="_blank"
                >
                  Demande d'ouverture de compte
                </a>
              )}
            </div>
          ) : (
            <div className="col-xxl-6 col-xl-5 col-lg-6 d-lg-block d-none ms-auto">
              <div className="image-contain">
                <img
                  src="/assets/images/signup.svg"
                  className="img-fluid"
                  alt=""
                />
              </div>
            </div>
          )}

          <div className="col-xxl-4 col-xl-5 col-lg-6 col-sm-8 mx-auto">
            <div className="log-in-box">
              <div className="log-in-title mb-4">
                <h3>Bienvenue chez Ecowatt</h3>
                <h4>Créer un nouveau compte</h4>
              </div>

              {error && <ErrorSnackbar message={error?.message} />}

              <div className="input-box">
                <Formik
                  initialValues={genInitialValues()}
                  validationSchema={fomSchema}
                  onSubmit={onSubmit}
                >
                  {({
                    setFieldValue,
                    values,
                    errors,
                    touched,
                    handleChange,
                    handleBlur,
                    handleSubmit,
                  }) => (
                    <form onSubmit={handleSubmit} className="row g-4">
                      <div className="col-12">
                        <div className="d-flex align-items-center justify-content-between">
                          <div className="d-flex align-items-center">
                            <input
                              type="radio"
                              id="individual"
                              name="type"
                              checked={values.type === "individual"}
                              onChange={(e) => {
                                handleChange(e);
                                e.target.checked && setActive("individual");
                              }}
                              onBlur={handleBlur}
                              value={`individual`}
                            />
                            &nbsp;
                            <label htmlFor="individual">Particulier</label>
                          </div>
                          <div className="d-flex align-items-center">
                            <input
                              type="radio"
                              id="professional"
                              name="type"
                              checked={values.type === "professional"}
                              onChange={(e) => {
                                handleChange(e);
                                e.target.checked && setActive("professional");
                              }}
                              onBlur={handleBlur}
                              value={`professional`}
                            />
                            &nbsp;
                            <label htmlFor="professional">Professional</label>
                          </div>
                          <div className="d-flex align-items-center">
                            <input
                              type="radio"
                              id="seller"
                              name="type"
                              checked={values.type === "seller"}
                              onChange={(e) => {
                                handleChange(e);
                                e.target.checked && setActive("seller");
                              }}
                              onBlur={handleBlur}
                              value={`seller`}
                            />
                            &nbsp;
                            <label htmlFor="seller">Revendeur</label>
                          </div>
                        </div>
                        <span className="error-form">
                          {errors.type && touched.type && errors.type}
                        </span>
                      </div>
                      <div className="col-12 col-md-6">
                        <div className="form-floating theme-form-floating">
                          <input
                            type="text"
                            className="form-control"
                            id="fname"
                            name="fname"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.fname}
                          />
                          <label htmlFor="fname">Nom</label>
                        </div>
                        <span className="error-form">
                          {errors.fname && touched.fname && errors.fname}
                        </span>
                      </div>
                      <div className="col-12 col-md-6">
                        <div className="form-floating theme-form-floating">
                          <input
                            type="text"
                            className="form-control"
                            id="lname"
                            name="lname"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.lname}
                          />
                          <label htmlFor="lname">Prénom</label>
                        </div>
                        <span className="error-form">
                          {errors.lname && touched.lname && errors.lname}
                        </span>
                      </div>
                      <div className="col-12">
                        <div className="form-floating theme-form-floating">
                          <input
                            type="email"
                            className="form-control"
                            id="email"
                            name="email"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.email}
                          />
                          <label htmlFor="email">Adresse mail</label>
                        </div>
                        <span className="error-form">
                          {errors.email && touched.email && errors.email}
                        </span>
                      </div>
                      <div className="col-12">
                        <div className="form-floating theme-form-floating group-mobile">
                          <input
                            type="text"
                            className="form-control"
                            id="mobile"
                            name="mobile"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.mobile}
                          />
                          <label htmlFor="mobile">Téléphone</label>
                          <span>+212-</span>
                        </div>
                        <span className="error-form">
                          {errors.mobile && touched.mobile && errors.mobile}
                        </span>
                      </div>
                      <div className="col-12 form-group-password">
                        <div className="form-floating theme-form-floating">
                          <input
                            type={passwordStatus ? "text" : "password"}
                            className="form-control"
                            id="password"
                            name="password"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.password}
                          />
                          <label htmlFor="password">Mot de passe</label>
                          {passwordStatus ? (
                            <Eye onClick={togglePasswordStatus} />
                          ) : (
                            <EyeOff onClick={togglePasswordStatus} />
                          )}
                        </div>
                        <span className="error-form">
                          {errors.password &&
                            touched.password &&
                            errors.password}
                        </span>
                      </div>

                      <div className="col-12">
                        <div className="form-floating theme-form-floating">
                          <input
                            type="text"
                            className="form-control"
                            id="referrer_code"
                            name="referrer_code"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.referrer_code}
                          />
                          <label htmlFor="referrer_code">
                            Code d'invitation (Optionnelle)
                          </label>
                        </div>
                        <span className="error-form">
                          {errors.referrer_code &&
                            touched.referrer_code &&
                            errors.referrer_code}
                        </span>
                      </div>

                      {(values.type === "professional" ||
                        values.type === "seller") && (
                        <>
                          <div className="col-12 col-md-6">
                            <div className="form-floating theme-form-floating">
                              <input
                                type="text"
                                className="form-control"
                                id="rc"
                                name="rc"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                value={values.rc}
                              />
                              <label htmlFor="rc">RC</label>
                            </div>
                            <span className="error-form">
                              {errors.rc && touched.rc && errors.rc}
                            </span>
                          </div>
                          <div className="col-12 col-md-6">
                            <div className="form-floating theme-form-floating">
                              <input
                                type="text"
                                className="form-control"
                                id="ice"
                                name="ice"
                                onChange={handleChange}
                                onBlur={handleBlur}
                                value={values.ice}
                              />
                              <label htmlFor="ice">ICE</label>
                            </div>
                            <span className="error-form">
                              {errors.ice && touched.ice && errors.ice}
                            </span>
                          </div>
                        </>
                      )}

                      {values.type === "seller" && (
                        <>
                          <div className="col-12 col-md-6">
                            <div className="form-floating theme-form-floating">
                              <input
                                accept="application/pdf"
                                type="file"
                                className="form-control"
                                id="rc_file"
                                name="rc_file"
                                onChange={(event) => {
                                  setFieldValue(
                                    "rc_file",
                                    event.currentTarget.files[0]
                                  );
                                }}
                                onBlur={handleBlur}
                              />
                              <label htmlFor="rc_file">RC file</label>
                            </div>
                            <span className="error-form">
                              {errors.rc_file &&
                                touched.rc_file &&
                                errors.rc_file}
                            </span>
                          </div>
                          <div className="col-12 col-md-6">
                            <div className="form-floating theme-form-floating">
                              <input
                                accept="application/pdf"
                                type="file"
                                className="form-control"
                                id="cin_file"
                                name="cin_file"
                                onChange={(event) => {
                                  setFieldValue(
                                    "cin_file",
                                    event.currentTarget.files[0]
                                  );
                                }}
                                onBlur={handleBlur}
                              />
                              <label htmlFor="cin_file">CIN file</label>
                            </div>
                            <span className="error-form">
                              {errors.cin_file &&
                                touched.cin_file &&
                                errors.cin_file}
                            </span>
                          </div>
                          <div className="col-12">
                            <div className="form-floating theme-form-floating">
                              <input
                                accept="application/pdf"
                                type="file"
                                className="form-control"
                                id="opening_form_file"
                                name="opening_form_file"
                                onChange={(event) => {
                                  setFieldValue(
                                    "opening_form_file",
                                    event.currentTarget.files[0]
                                  );
                                }}
                                onBlur={handleBlur}
                              />
                              <label htmlFor="opening_form_file">
                                Uploader le document d’ouverture de compte signé
                              </label>
                            </div>
                            <span className="error-form">
                              {errors.opening_form_file &&
                                touched.opening_form_file &&
                                errors.opening_form_file}
                            </span>
                          </div>
                        </>
                      )}

                      <div className="col-12">
                        <button
                          disabled={isLoading}
                          className="btn btn-animation w-100"
                          type="submit"
                        >
                          {isLoading ? (
                            <TailSpin
                              type="ThreeDots"
                              color="#fff"
                              height={20}
                              width={20}
                            />
                          ) : (
                            "S'inscrire"
                          )}
                        </button>
                      </div>
                    </form>
                  )}
                </Formik>
              </div>
              <div className="sign-up-box">
                <h4>Vous avez déjà un compte?</h4>
                <Link href={`/login`}>Se connecter</Link>
              </div>
            </div>
          </div>

          <div className="col-xxl-7 col-xl-6 col-lg-6"></div>
        </div>
      </div>
    </section>
  );
}
