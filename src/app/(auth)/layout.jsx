"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuthContext } from "@/providers/auth-provider";

export default function Layout({ children }) {
  const router = useRouter();
  const { authenticated } = useAuthContext();

  useEffect(() => {
    if (authenticated) {
      router.replace("/");
    }
  }, [authenticated]);

  if(authenticated) {
    return null
  }

  return children;
}
