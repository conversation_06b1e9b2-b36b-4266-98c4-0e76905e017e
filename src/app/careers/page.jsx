import Link from "next/link";

import PageBanner from "@/components/page-banner";
import SectionTitle from "@/components/section-title";
import { Button } from "@/components/ui/button";
import { CareerService } from "@/services/career";
import { XIcon } from "lucide-react";
import CareerCard from "./_components/career-card";
import CareersFilter from "./_components/careers-filter";

export default async function CareersHome({ searchParams: filters }) {
  const data = await CareerService.getCareers(filters);

  return (
    <div className="-mt-[1.5rem] space-y-20 pb-20">
      <PageBanner
        title="Carrières"
        background="/assets/images/man-wooden-cubes-table-management-concept.webp"
      />

      <section className="container-lg pt-0">
        <SectionTitle title="Nos offres d'emploi" />
        <CareersFilter settings={data?.settings} filters={filters} />

        <div className="my-5">
          {data?.careers?.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
              {data.careers?.map((career) => (
                <CareerCard key={career.id} career={career} />
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center py-10 gap-2">
              <h6 className="text-xl font-semibold">Aucune Carrièrs trouvé!</h6>
              {Object.entries(filters)?.some(([_, value]) => value) && (
                <Button variant="outline" size="sm" asChild>
                  <Link href="/careers">
                    <XIcon /> Effacer les filtres
                  </Link>
                </Button>
              )}
            </div>
          )}
        </div>
      </section>
    </div>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Carrières",
  description:
    "Explorez nos opportunités et façonnez votre avenir avec Ecowatt.",
  alternates: {
    canonical: "/careers",
  },
};
