import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { MapIcon, ShapesIcon, SignatureIcon } from "lucide-react";
import moment from "moment";
import Link from "next/link";

export default function CareerCard({ career }) {
  return (
    <Card>
      <div className="h-full flex flex-col bg-secondary p-3 sm:p-4">
        <div className="flex items-center justify-between mb-3">
          <Link
            href={`/careers/${career.slug}`}
            className="flex items-center divide-x divide-brand-primary hover:underline"
          >
            <h4 className="text-lg font-black leading-5 line-clamp-2">
              {career.title}
            </h4>
          </Link>
          <Badge className="ml-1">#{career.ref}</Badge>
        </div>
        <div className="mb-4">
          <ul className="flex flex-wrap items-center gap-3">
            <li className="flex items-center gap-2">
              <ShapesIcon size={18} /> <span>{career.department}</span>
            </li>
            <li className="flex items-center gap-2">
              <MapIcon size={18} /> <span>{career.location}</span>
            </li>
            <li className="flex items-center gap-2">
              <SignatureIcon size={18} /> <span>{career.type}</span>
            </li>
          </ul>
        </div>
        <div className="mt-auto">
          {career.is_closed ? (
            <span className="font-medium text-danger italic">
              <strong>Clôturé</strong>
            </span>
          ) : (
            <span className="font-medium italic">
              Clôture le{" "}
              {moment(new Date(career.closing_date)).format("DD MMMM yyyy")}
            </span>
          )}
        </div>
      </div>
    </Card>
  );
}
