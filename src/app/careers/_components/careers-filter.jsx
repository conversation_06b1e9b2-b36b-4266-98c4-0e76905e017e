"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SlidersHorizontalIcon } from "lucide-react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

const listFilters = [
  {
    key: "department",
    label: "Direction",
  },
  {
    key: "type",
    label: "Type Contrat",
  },
  {
    key: "location",
    label: "Lieu",
  },
  {
    key: "experience",
    label: "Expérience",
  },
];

export default function CareersFilter({ settings, filters }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  function onFilterChange(key, value) {
    const current = new URLSearchParams(Array.from(searchParams.entries()));

    if (!value) {
      current.delete(key);
    } else {
      current.set(key, value);
    }

    const search = current.toString();

    const query = search ? `?${search}` : "";

    router.push(`${pathname}${query}`, { scroll: false });
  }

  return (
    <div className="my-6 flex items-center gap-2">
      <SlidersHorizontalIcon className="w-6 h-6" />
      <ul className="flex items-center overflow-x-auto justify gap-2">
        {listFilters.map((filter) => (
          <li key={filter.key} className="flex-1">
            <Select
              value={filters[filter.key]}
              onValueChange={(value) => {
                onFilterChange(filter.key, value);
              }}
            >
              <SelectTrigger className="min-w-[130px]">
                <SelectValue placeholder={filter.label} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={null}>{filter.label}</SelectItem>
                {settings?.[filter.key]?.map((item) => (
                  <SelectItem key={item} value={item}>
                    {item}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </li>
        ))}
      </ul>
    </div>
  );
}
