import { CareerService } from "@/services/career";
import CareerContainer from "./_components/career-container";

export default async function CareerDetails({ params }) {
  const { slug } = params;

  const career = await CareerService.getCareer(slug);

  return <CareerContainer career={career} />;
}

/** @return {import('next').Metadata} */
export async function generateMetadata({ params }) {
  const { slug } = params;
  const career = await CareerService.getCareer(slug);

  return {
    title: career.title,
    description: career.description,
  };
}
