import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import ReactConfetti from "react-confetti";
import { CheckCircle, ChevronLeft } from "react-feather";

export default function ApplySuccess() {
  return (
    <div className="-mt-[1.5rem] h-screen bg-brand-primary relative flex flex-col items-center text-center py-5 px-2">
      <ReactConfetti width={window.innerWidth} />
      <CheckCircle size={40} />
      <h2 className="text-green-600 mb-3 mt-5">
        Votre Candidature a été Envoyée avec Succès 🎉
      </h2>
      <p className="max-w-5xl text-lg text-center text-white">
        Nous vous remercions pour votre candidature. Nous allons examiner
        attentivement votre profil et nous vous contacterons par email ou par
        téléphone dans un délai d'un mois. Si vous ne recevez pas de réponse de
        notre part dans ce délai, cela signifie que votre profil ne correspond
        malheureusement pas à nos critères de sélection. Nous vous remercions de
        votre compréhension.
      </p>
      <div>
        <Link href="/careers" asChild>
          <Button variant="ghost" className="text-white">
            <ChevronLeft /> Retourner aux Carrières
          </Button>
        </Link>
      </div>
    </div>
  );
}
