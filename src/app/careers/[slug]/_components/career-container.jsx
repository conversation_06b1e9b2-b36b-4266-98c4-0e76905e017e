"use client";

import PageBanner from "@/components/page-banner";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import HtmlContent from "@/shared/components/html-content";
import { CircleXIcon, CornerLeftUpIcon, SendIcon } from "lucide-react";
import { useEffect, useState } from "react";
import ApplyCareerForm from "./apply-career-form";
import ApplySuccess from "./apply-success";

export default function CareerContainer({ career }) {
  const [showForm, setShowForm] = useState(false);
  const [applySuccess, setApplySuccess] = useState(false);

  useEffect(() => {
    window.scrollTo({ top: 0 });
  }, [showForm]);

  if (applySuccess) {
    return (
      <div>
        <ApplySuccess />
      </div>
    );
  }

  return (
    <div className="-mt-[1.5rem]">
      <PageBanner
        background="/assets/images/careers-banner.webp"
        title={career.title}
        link="/careers"
        linkLabel="Posteruler maintenant"
      />
      <div className="flex justify-center -translate-y-1/2">
        <Button
          size="lg"
          title={career.is_closed ? "Clôturé" : ""}
          className={cn(
            "h-12 border-2 border-white hover:bg-brand-primary disabled:opacity-100",
            career.is_closed && "!cursor-not-allowed"
          )}
          onClick={() => setShowForm((show) => !show)}
          disabled={career.is_closed}
        >
          {career.is_closed ? (
            <CircleXIcon />
          ) : showForm ? (
            <CornerLeftUpIcon />
          ) : (
            <SendIcon />
          )}
          {career.is_closed
            ? "Offre Non Disponible"
            : showForm
              ? "Consulter l'Annonce"
              : "Postuler maintenant"}
        </Button>
      </div>

      <div className="container-lg my-5">
        <div className="career-details-container">
          <div className="my-5">
            {showForm ? (
              <ApplyCareerForm
                career={career}
                setApplySuccess={setApplySuccess}
              />
            ) : (
              <div>
                <HtmlContent html={career?.content} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
