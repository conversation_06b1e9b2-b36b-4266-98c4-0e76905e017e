"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useApplyCareerMutation } from "@/services/career";
import { useCountryCities } from "@/services/common";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod";

const SUPPORTED_FORMATS = ["application/pdf"];
const FILE_SIZE = 1024 * 1024;

const formSchema = z.object({
  fname: z.string().min(1, "Ce champ est obligatoire"),
  lname: z.string().min(1, "Ce champ est obligatoire"),
  email: z
    .string()
    .email("Adresse e-mail non valide")
    .min(1, "Ce champ est obligatoire"),
  phone: z.string().min(1, "Ce champ est obligatoire"),
  city: z.string().min(1, "Ce champ est obligatoire"),
  situation: z.string().min(1, "Ce champ est obligatoire"),
  resume: z
    .any()
    .refine((file) => !!file, {
      message: "Ce champ est obligatoire",
    })
    .refine((file) => !file || file.size <= FILE_SIZE, {
      message: "Le fichier téléchargé est trop volumineux.",
    })
    .refine((file) => !file || SUPPORTED_FORMATS.includes(file.type), {
      message: "Le fichier téléchargé a un format non pris en charge.",
    }),
  cover_letter: z
    .any()
    .refine((file) => !file || file.size <= FILE_SIZE, {
      message: "Le fichier téléchargé est trop volumineux.",
    })
    .refine((file) => !file || SUPPORTED_FORMATS.includes(file.type), {
      message: "Le fichier téléchargé a un format non pris en charge.",
    }),
});

export default function ApplyCareerForm({ career, setApplySuccess }) {
  const { cities } = useCountryCities(134);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fname: "",
      lname: "",
      email: "",
      phone: "",
      city: "",
      situation: "",
      resume: "",
      cover_letter: "",
    },
  });

  const { applyCareer, isLoading, error } = useApplyCareerMutation();

  function onSubmit(data) {
    applyCareer(
      { slug: career.slug, data },
      {
        onSuccess() {
          setApplySuccess(true);
          window.scrollTo({ top: 0 });
        },
      }
    );
  }

  return (
    <div>
      <div className="mb-3 space-y-3">
        <h2>Soumettez votre candidature</h2>
        <div>
          <p className="text-justify">
            Avant de soumettre votre candidature, veuillez lire la description
            du poste pour vérifier qu'elle correspond à votre profil. Si vous
            n'avez pas de réponse sous un mois, cela signifie que votre profil
            ne correspond pas aux exigences. Merci de votre compréhension et de
            l'intérêt pour notre entreprise.
          </p>
        </div>
      </div>

      {error && <ErrorSnackbar message={error.message} />}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
          <div className="flex flex-col sm:flex-row gap-3">
            <FormField
              control={form.control}
              name="fname"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Prénom</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Saisissez votre prénom"
                      autoFocus
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lname"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Nom</FormLabel>
                  <FormControl>
                    <Input placeholder="Saisissez votre nom" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <FormField
              control={form.control}
              name="email"
              className="border-2 border-danger"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Adresse e-mail</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Saisissez votre adresse e-mail"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Téléphone</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Saisissez votre numéro de téléphone"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <FormField
              control={form.control}
              name="city"
              className="border-2 border-danger"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Ville</FormLabel>
                  <Select
                    name={field.name}
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    {...field}
                  >
                    <FormControl>
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Sélectionnez une ville" />
                      </SelectTrigger>
                    </FormControl>
                    <FormMessage />
                    <SelectContent>
                      {cities
                        ?.sort((a, b) => (a?.name > b?.name ? 1 : -1))
                        ?.map((city) => (
                          <SelectItem
                            key={city.id}
                            value={city.id.toString()}
                            label={city.name}
                          >
                            {city.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="situation"
              className="border-2 border-danger"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Situation</FormLabel>

                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Sélectionnez votre situation" />
                      </SelectTrigger>
                    </FormControl>
                    <FormMessage />
                    <SelectContent>
                      {["Employé(e)", "Stagiaire", "Sans emploi"].map(
                        (item) => (
                          <SelectItem key={item} value={item}>
                            {item}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <FormField
              control={form.control}
              name="resume"
              className="border-2 border-danger"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Resume/CV (PDF &lt; 1mb)</FormLabel>
                  <FormControl>
                    <Input
                      type="file"
                      placeholder="Télecharger votre CV"
                      onChange={(event) => {
                        field.onChange(event.target?.files?.[0] ?? null);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="cover_letter"
              className="border-2 border-danger"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Lettre de motivation (PDF &lt; 1mb)</FormLabel>
                  <FormControl>
                    <Input
                      type="file"
                      placeholder="Télecharger votre CV"
                      onChange={(event) => {
                        field.onChange(event.target?.files?.[0] ?? null);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="my-4">
            <span>
              Les informations collectées dans ce formulaire sont utilisées
              uniquement dans le cadre du processus de recrutement. Vos données
              seront traitées de manière confidentielle et ne seront pas
              partagées avec des tiers sans votre consentement.
            </span>
          </div>

          <div className="flex justify-between">
            <Link href="/careers" disabled={isLoading} asChild>
              <Button variant="outline">Annuler</Button>
            </Link>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Soumettre..." : "Soumettre"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
