import { useState } from "react";
import AccordionItem from "./accordion-item";
import { useSearchParams } from "next/navigation";

export default function PriceFilter({
  handleMinPriceFilter,
  handleMaxPriceFilter,
  minPricePossible,
  maxPricePossible,
}) {
  //   const location = useLocation();
  const queryParameters = useSearchParams();

  const [minPrice, setMinPrice] = useState(
    queryParameters.has("price[min]")
      ? queryParameters.get("price[min]")
      : minPricePossible
  );
  const [maxPrice, setMaxPrice] = useState(
    queryParameters.has("price[max]")
      ? queryParameters.get("price[max]")
      : maxPricePossible
  );

  const [minPriceRange, setMinPriceRange] = useState(
    queryParameters.has("price[min]")
      ? queryParameters.get("price[min]")
      : minPricePossible
  );
  const [maxPriceRange, setMaxPriceRange] = useState(
    queryParameters.has("price[max]")
      ? queryParameters.get("price[max]")
      : maxPricePossible
  );

  const handleMinPriceChange = (event) => {
    const newMinPrice = parseInt(event.target.value);
    if (!isNaN(newMinPrice)) {
      setMinPrice(newMinPrice);
    }
  };

  const handleMaxPriceChange = (event) => {
    const newMaxPrice = parseInt(event.target.value);
    if (!isNaN(newMaxPrice)) {
      setMaxPrice(newMaxPrice);
    }
  };

  const handleMinPriceRangeChange = (event) => {
    const newMinPrice = parseInt(event.target.value);
    if (!isNaN(newMinPrice) && newMinPrice < maxPriceRange) {
      setMinPriceRange(newMinPrice);
      setMinPrice(newMinPrice);
    }
  };

  const handleMaxPriceRangeChange = (event) => {
    const newMaxPrice = parseInt(event.target.value);
    if (!isNaN(newMaxPrice) && newMaxPrice > minPriceRange) {
      setMaxPriceRange(newMaxPrice);
      setMaxPrice(newMaxPrice);
    }
  };

  const handlePricing = (event) => {
    event.preventDefault();

    if (minPrice < minPricePossible) {
      setMinPrice(minPricePossible);
      handleMinPriceFilter(minPricePossible);
    } else {
      handleMinPriceFilter(minPrice);
    }
    if (maxPrice > maxPricePossible) {
      setMaxPrice(maxPricePossible);
      handleMaxPriceFilter(maxPricePossible);
    } else {
      handleMaxPriceFilter(maxPrice);
    }
  };

  if (maxPricePossible === minPricePossible) return null;

  return (
    <AccordionItem title={`Prix`} defaultStatus={true}>
      <form onSubmit={handlePricing}>
        <div className="d-flex flex-column flex-xl-row align-items-center gap-2">
          <div className="form-floating theme-form-floating log-in-form">
            <input
              type="number"
              className="form-control"
              id="min-price"
              name="min-price"
              value={minPrice}
              onChange={handleMinPriceChange}
            />
            <label htmlFor="min-price">Min (DH)</label>
          </div>
          <div>-</div>
          <div className="form-floating theme-form-floating log-in-form">
            <input
              type="number"
              className="form-control"
              id="max-price"
              value={maxPrice}
              onChange={handleMaxPriceChange}
            />
            <label htmlFor="max-price">Max (DH)</label>
          </div>
        </div>

        <div className="my-3">
          <div className="price-slider">
            <div
              className="fill"
              style={{
                left: `${((minPriceRange - minPricePossible) / (maxPricePossible - minPricePossible)) * 100}%`,
                maxWidth: `${((maxPriceRange - minPriceRange) / (maxPricePossible - minPricePossible)) * 100}%`,
                right: `${((maxPricePossible - maxPriceRange) / (maxPricePossible - minPricePossible)) * 100}%`,
              }}
            ></div>
            <div className="range-input">
              <input
                type="range"
                className="range-min"
                min={minPricePossible}
                max={maxPricePossible}
                value={minPriceRange}
                step="5"
                onChange={handleMinPriceRangeChange}
              />
              <input
                type="range"
                className="range-max"
                min={minPricePossible}
                max={maxPricePossible}
                value={maxPriceRange}
                step="5"
                onChange={handleMaxPriceRangeChange}
              />
            </div>
          </div>
        </div>
        <button type="submit" className="btn mt-2 mx-auto">
          Appliquer
        </button>
      </form>
    </AccordionItem>
  );
}
