"use client";

import ProductBox from "@/components/product-box";
import { getProducts } from "@/queries/queries";
import Pagination from "@/shared/components/pagination";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { InfinitySpin } from "react-loader-spinner";
import { useQuery } from "react-query";
import ProductsFilter from "./products-filter";
import Sidebar from "./sidebar";

const PageSize = 1;

export default function ProductsContainer() {
  const queryParameters = useSearchParams();
  const router = useRouter();

  const searchParams = queryParameters.toString();

  const [showMenu, setShowMenu] = useState(false);

  const [products, setProducts] = useState([]);
  const [listCategories, setListCategories] = useState([]);
  const [listBrands, setListBrands] = useState([]);
  const [listProperties, setListProperties] = useState([]);

  const [grid, setGrid] = useState(3);
  const [search, setSearch] = useState("");
  const [status, setStatus] = useState("");
  const [type, setType] = useState("");
  const [offer, setOffer] = useState("");
  const [categories, setCategories] = useState(
    queryParameters.get("categories")
      ? queryParameters.get("categories").split(",")
      : []
  ); //useState([])
  const [brands, setBrands] = useState(
    queryParameters.get("brands")
      ? queryParameters.get("brands").split(",")
      : []
  ); //useState([])
  const [properties, setProperties] = useState(
    queryParameters.get("properties")
      ? queryParameters.get("properties").split(",")
      : []
  ); //useState([])
  const [measures, setMeasures] = useState([]);
  const [showProperties, setShowProperties] = useState([]);
  const [sort, setSort] = useState("latest");
  const [minPrice, setMinPrice] = useState(
    queryParameters.has("price[min]") ? queryParameters.get("price[min]") : ""
  );
  const [maxPrice, setMaxPrice] = useState(
    queryParameters.has("price[max]") ? queryParameters.get("price[max]") : ""
  );

  const [minPricePossible, setMinPricePossible] = useState();
  const [maxPricePossible, setMaxPricePossible] = useState();

  const [page, setPage] = useState(1);
  const [number, setNumber] = useState(15);
  const [maxPages, setMaxPages] = useState(1);

  const { isFetching: isLoadingProducts } = useQuery(
    [
      "products",
      {
        search: search,
        categories: categories,
        brands: brands,
        properties: properties,
        sort: sort,
        page: page,
        number: number,
        status: status,
        type: type,
        offer: offer,
        "price[min]": minPrice,
        "price[max]": maxPrice,
      },
    ],
    () =>
      getProducts({
        search: search,
        categories: categories,
        brands: brands,
        properties: properties,
        sort: sort,
        page: page,
        number: number,
        status: status,
        type: type,
        offer: offer,
        "price[min]": minPrice,
        "price[max]": maxPrice,
      }),
    {
      retry: 2,
      refetchOnWindowFocus: false,
      // keepPreviousData: true,
      onSuccess(data) {
        if (data?.status === true) {
          handleData(data);
          generateUrl();
        } else {
          setProducts([]);
          setMaxPages(1);
        }
      },
      onError: (error) => {
        console.error("Error fetching products:", error);
        setProducts([]);
        setMaxPages(1);
      },
    }
  );

  useEffect(() => {
    if ("products?" + searchParams !== curentUrl()) {
      if (queryParameters.get("categories") !== categories?.join(",")) {
        setMinPrice(null);
        setMaxPrice(null);
      }

      if (queryParameters.get("search")) {
        setSearch(queryParameters.get("search"));
      }
      if (queryParameters.get("categories")) {
        setCategories(queryParameters.get("categories").split(","));
      }
      if (queryParameters.get("properties")) {
        setProperties(queryParameters.get("properties").split(","));
      }
      if (queryParameters.get("brands")) {
        setBrands(queryParameters.get("brands").split(","));
      }
      if (queryParameters.get("sort")) {
        setSort(queryParameters.get("sort"));
      }
      if (queryParameters.get("page")) {
        setPage(queryParameters.get("page"));
      }
      if (queryParameters.get("number")) {
        setNumber(queryParameters.get("number"));
      }
      if (queryParameters.get("price[max]")) {
        setMaxPrice(queryParameters.get("price[max]"));
      }
      if (queryParameters.get("price[min]")) {
        setMinPrice(queryParameters.get("price[min]"));
      }
      if (queryParameters.get("status")) {
        setStatus(queryParameters.get("status"));
      }
      if (queryParameters.get("type")) {
        setType(queryParameters.get("type"));
      }
      if (queryParameters.get("offer")) {
        setOffer(queryParameters.get("offer"));
      }
    }
  }, [searchParams]);

  const curentUrl = () => {
    const params = [];
    if (categories && categories.length) {
      params.push(`categories=${categories.toString()}`);
    }
    if (brands && brands.length) {
      params.push(`brands=${brands.toString()}`);
    }
    if (properties && properties.length) {
      params.push(`properties=${encodeURIComponent(properties.toString())}`);
    }
    if (maxPrice && maxPrice) {
      params.push(`price[max]=${maxPrice}`);
    }
    if (minPrice) {
      params.push(`price[min]=${minPrice}`);
    }
    params.push(`search=${search}`);
    params.push(`sort=${sort}`);
    params.push(`page=${page}`);
    params.push(`number=${number}`);
    params.push(`status=${status}`);
    params.push(`type=${type}`);
    params.push(`offer=${offer}`);

    const query = params.join("&");

    return `products?${query}`;
  };

  const generateUrl = () => {
    let fullurl = curentUrl();
    if ("products?" + searchParams !== curentUrl()) {
      router.push(fullurl);
    }
  };

  const removeItem = (selected, type) => {
    let filterResult = [];
    switch (type) {
      case "search":
        setPage(1);
        setSearch("");
        break;
      case "categories":
        filterResult = categories.filter((item) => item !== selected);
        setPage(1);
        setCategories(filterResult);
        break;
      case "brands":
        filterResult = brands.filter((item) => item !== selected);
        setPage(1);
        setBrands(filterResult);
        break;
      case "price":
        setMinPrice("");
        setMaxPrice("");
        break;
      default:
    }
  };

  const handleData = (data) => {
    setProducts(data?.data?.data);
    setListCategories(data?.categories);
    setListBrands(data?.brands);
    setListProperties(data?.properties);
    setMaxPages(data?.data?.last_page);
    setShowProperties(data?.old);
    setMaxPricePossible(data?.price?.max);
    setMinPricePossible(data?.price?.min);
  };
  const handleShowMenu = () => {
    setShowMenu(!showMenu);
  };
  const handleGrid = (selected) => {
    if (selected !== grid) {
      setGrid(selected);
    }
  };
  const handleSort = (selected) => {
    if (selected !== sort) {
      setSort(selected);
      setPage(1);
    }
  };
  const handleNumber = (selected) => {
    if (selected !== number) {
      setNumber(selected);
      setPage(1);
    }
  };
  const handlePage = (selected) => {
    if (selected !== page) {
      setPage(selected);
      window.scrollTo({ top: 0 });
    }
  };
  const handleAttributes = (value, key, action) => {
    action = action.toLowerCase();
    key = key.toLowerCase();
  };

  const handleCategoriesFilter = (selected) => {
    setMinPrice(null);
    setMaxPrice(null);
    if (categories.length > 0) {
      const list = [...categories];
      const filterResult = list.filter((item) => item === selected);
      if (filterResult.length) {
        const filterResult = list.filter((item) => item !== selected);
        setPage(1);
        setCategories(filterResult);
      } else {
        list.push(selected);
        setPage(1);
        setCategories([selected]);
      }
    } else {
      setPage(1);
      setCategories([selected]);
    }
  };
  const handleBrandsFilter = (selected) => {
    setMinPrice(null);
    setMaxPrice(null);
    if (brands.length > 0) {
      const list = [...brands];
      const filterResult = list.filter((item) => item === selected);
      if (filterResult.length) {
        const filterResult = list.filter((item) => item !== selected);
        setPage(1);
        setBrands(filterResult);
      } else {
        list.push(selected);
        setPage(1);
        setBrands(list);
      }
    } else {
      setPage(1);
      setBrands([selected]);
    }
  };
  const handleMeasuresFilter = (selected) => {
    setMinPrice(null);
    setMaxPrice(null);
    if (measures.length > 0) {
      const list = [...measures];
      const filterResult = list.filter((item) => item === selected);
      if (filterResult.length) {
        const filterResult = list.filter((item) => item !== selected);
        setPage(1);
        setMeasures(filterResult);
      } else {
        list.push(selected);
        setPage(1);
        setMeasures(list);
      }
    } else {
      setPage(1);
      setMeasures([selected]);
    }
  };
  const handlePropertiesFilter = (label, selected) => {
    setMinPrice(null);
    setMaxPrice(null);
    if (properties.includes(`${label}-${selected}`)) {
      setProperties((current) =>
        current.filter((row) => {
          return row !== `${label}-${selected}`;
        })
      );
    } else {
      setProperties((prev) => [...prev, `${label}-${selected}`]);
    }
  };
  const handleMinPriceFilter = (min) => {
    setMinPrice(min);
  };

  const handleMaxPriceFilter = (max) => {
    setMaxPrice(max);
    //   console.log(maxPrice)
  };

  const bannerCategory = listCategories?.find(
    (category) => category?.slug === categories?.at(-1)
  );

  return (
    <div>
      {bannerCategory && bannerCategory.full_banner ? (
        <div className="container-lg mt-4 home-contain">
          <img src={bannerCategory?.full_banner} alt={bannerCategory?.name} />
        </div>
      ) : null}
      <section className="section-b-space shop-section">
        <div className="container-lg">
          <div className="row">
            <Sidebar
              isLoadingProducts={isLoadingProducts}
              removeItem={removeItem}
              maxPrice={maxPrice}
              minPrice={minPrice}
              listCategories={listCategories}
              categories={categories}
              search={search}
              status={status}
              type={type}
              offer={offer}
              listBrands={listBrands}
              brands={brands}
              listProperties={listProperties}
              properties={properties}
              showProperties={showProperties}
              // listMeasures={listMeasures}
              handleCategoriesFilter={handleCategoriesFilter}
              handleBrandsFilter={handleBrandsFilter}
              handlePropertiesFilter={handlePropertiesFilter}
              handleShowMenu={handleShowMenu}
              handleAttributes={handleAttributes}
              showMenu={showMenu}
              handleMinPriceFilter={handleMinPriceFilter}
              handleMaxPriceFilter={handleMaxPriceFilter}
              minPricePossible={minPricePossible}
              maxPricePossible={maxPricePossible}
            />

            <div className="col-12 col-lg-9">
              <ProductsFilter
                handleSort={handleSort}
                sort={sort}
                handleNumber={handleNumber}
                number={number}
                handleGrid={handleGrid}
                grid={grid}
                handleShowMenu={handleShowMenu}
              />

              {isLoadingProducts ? (
                <div className="p-4 d-flex align-items-center justify-content-center">
                  <InfinitySpin
                    type="ThreeDots"
                    color="#2A3466"
                    height={220}
                    width={220}
                    visible={isLoadingProducts}
                  />
                </div>
              ) : products && products.length ? (
                <div
                  className="row mt-2 g-3 row-cols-2 row-cols-sm-3 row-cols-md-4 row-cols-lg-3 row-cols-xl-4 product-list-section"
                  // className={`row g-sm-4 g-3 ${grid != 12 ? `row-cols-xl-3 row-cols-lg-2 row-cols-md-3 row-cols-2 product-list-section row-cols-xxl-${12 / grid}` : `row-cols-1`}`}
                >
                  {products.map((item, key) => (
                    <div key={`pb-roducts-${item?.slug}`}>
                      <ProductBox
                        product={item}
                        isWishlist={false}
                        isHorizontal={grid == 12}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <h2 className="text-center py-5 my-3">Aucun produit trouvé</h2>
              )}
              {!isLoadingProducts && maxPages > 1 && (
                <Pagination
                  className="pagination-bar"
                  currentPage={page}
                  totalCount={maxPages}
                  pageSize={PageSize}
                  onPageChange={(page) => handlePage(page)}
                />
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
