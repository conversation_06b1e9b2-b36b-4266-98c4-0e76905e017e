import { But<PERSON> } from "@/components/ui/button";
import { XIcon } from "lucide-react";
import { InfinitySpin } from "react-loader-spinner";
import AccordionItem from "./accordion-item";
import PriceFilter from "./price-filter";

export default function Sidebar({
  isLoadingProducts,
  removeItem,
  maxPrice,
  minPrice,
  listCategories,
  categories,
  search,
  status,
  type,
  offer,
  listBrands,
  brands,
  listProperties,
  properties,
  showProperties,
  handleCategoriesFilter,
  handleBrandsFilter,
  handlePropertiesFilter,
  handleShowMenu,
  showMenu,
  handleMinPriceFilter,
  handleMaxPriceFilter,
  minPricePossible,
  maxPricePossible,
}) {
  return (
    <div className="col-12 col-lg-3">
      {isLoadingProducts ? (
        <div className="px-4 py-5 d-flex align-items-center justify-content-center">
          <InfinitySpin
            type="ThreeDots"
            color="#2A3466"
            height={220}
            width={220}
            visible={isLoadingProducts}
          />
        </div>
      ) : (
        <>
          {(search ||
            minPrice ||
            maxPrice ||
            categories.length ||
            brands.length ||
            Object.keys(showProperties).length ||
            status ||
            type ||
            offer) && (
            <div className={`selected-rows mb-3`}>
              <Button
                size="sm"
                variant="danger"
                onClick={() => (window.location.href = "/products")}
                className="rounded-full"
              >
                <XIcon /> Réinitialiser
              </Button>
            </div>
          )}

          {search && (
            <div className={`selected-rows mb-3`}>
              <h5>Recherche</h5>
              <button
                type="button"
                key={`side-search`}
                className={`rows-tags`}
                onClick={() => removeItem(search, "search")}
              >
                x {search}
              </button>
            </div>
          )}

          {minPrice && maxPrice ? (
            <div className={`selected-rows mb-3`}>
              <h5>Prix</h5>
              <button
                type="button"
                key={`side-search`}
                className={`rows-tags`}
                onClick={() => removeItem(null, "price")}
              >
                x {minPrice} MAD - {maxPrice} MAD
              </button>
            </div>
          ) : null}

          {categories && categories.length > 0 && (
            <div className={`selected-rows mb-3`}>
              <h5>Catégories</h5>
              {categories.map((item, i) => (
                <button
                  type="button"
                  key={`side-categories-${i}`}
                  className={`rows-tags`}
                  onClick={() => removeItem(item, "categories")}
                >
                  x {item.replace(/-/g, " ")}
                </button>
              ))}
            </div>
          )}

          {brands && brands.length > 0 && (
            <div className={`selected-rows mb-3`}>
              <h5>Les marques</h5>
              {brands.map((item, i) => (
                <button
                  type="button"
                  key={`side-brands-${i}`}
                  className={`rows-tags`}
                  onClick={() => removeItem(item, "brands")}
                >
                  x {item.replace(/-/g, " ")}
                </button>
              ))}
            </div>
          )}

          {showProperties &&
          typeof showProperties === "object" &&
          Object.keys(showProperties).length ? (
            Object.keys(showProperties).map((item, m) =>
              Array.isArray(showProperties[item]) &&
              showProperties[item].length ? (
                <div
                  className={`selected-rows mb-3`}
                  key={`selected-rows-${m}`}
                >
                  <h5>{item}</h5>
                  {showProperties[item].map((row, i) => (
                    <button
                      type="button"
                      key={`properties-${m}-${i}`}
                      className={`rows-tags`}
                      onClick={() => handlePropertiesFilter(item, row)}
                    >
                      x {row}
                    </button>
                  ))}
                </div>
              ) : (
                <div key={`empty-div-${m}`} />
              )
            )
          ) : (
            <div />
          )}

          <div
            className={`bg-overlay ${showMenu ? "show" : ""}`}
            onClick={() => handleShowMenu()}
          />
          <div className={`left-box wow fadeInUp ${showMenu ? "show" : ""}`}>
            <div className="shop-left-sidebar">
              <div className="accordion custome-accordion">
                <AccordionItem title={`Catégories`} defaultStatus={true}>
                  <ul className="category-list custom-padding custom-height">
                    {listCategories &&
                      listCategories.map((item) => (
                        <li key={item?.slug}>
                          <div
                            className="form-check ps-0 m-0 category-list-box"
                            onClick={() => handleCategoriesFilter(item?.slug)}
                          >
                            <div
                              className={`checkbox_animated ${categories.includes(item?.slug) ? "checked" : ""}`}
                            />
                            <label
                              className="form-check-label"
                              htmlFor={item?.slug}
                            >
                              <span className="name">{item?.name}</span>
                              <span className="number">
                                {item?.products_count}
                              </span>
                            </label>
                          </div>
                        </li>
                      ))}
                  </ul>
                </AccordionItem>

                <AccordionItem title={`Les marques`} defaultStatus={true}>
                  <ul className="category-list custom-padding custom-height">
                    {listBrands &&
                      listBrands.map((item) => (
                        <li key={item?.slug}>
                          <div
                            className="form-check ps-0 m-0 category-list-box"
                            onClick={() => handleBrandsFilter(item?.slug)}
                          >
                            <div
                              className={`checkbox_animated ${brands.includes(item?.slug) ? "checked" : ""}`}
                            />
                            <label
                              className="form-check-label"
                              htmlFor={item?.slug}
                            >
                              <span className="name">{item?.name}</span>
                              <span className="number">
                                {item?.products_count}
                              </span>
                            </label>
                          </div>
                        </li>
                      ))}
                  </ul>
                </AccordionItem>

                <PriceFilter
                  handleMinPriceFilter={handleMinPriceFilter}
                  handleMaxPriceFilter={handleMaxPriceFilter}
                  minPricePossible={minPricePossible}
                  maxPricePossible={maxPricePossible}
                />

                {listProperties && listProperties.length ? (
                  listProperties.map(
                    (item, key) =>
                      item?.chooses?.length > 0 && (
                        <AccordionItem
                          key={`accordion-item-${key}`}
                          title={item?.label}
                          defaultStatus={true}
                        >
                          <ul className="category-list custom-padding custom-height">
                            {item?.chooses.map((row, key1) => (
                              <li key={`${item?.label}-${key}-${key1}`}>
                                <div className="form-check ps-0 m-0 category-list-box">
                                  <input
                                    readOnly
                                    className="checkbox_animated"
                                    name="prop"
                                    type="checkbox"
                                    checked={
                                      properties &&
                                      properties.includes(
                                        `${item?.label}-${row?.value}`
                                      )
                                    }
                                    id={`${row}-${key}-${key1}`}
                                  />
                                  <label
                                    className="form-check-label"
                                    htmlFor={`${row?.value}-${key}-${key1}`}
                                    onClick={() =>
                                      handlePropertiesFilter(
                                        item?.label,
                                        row?.value
                                      )
                                    }
                                  >
                                    <span className="name">{`${row?.value} ${row?.measure}`}</span>
                                    <span className="number">{row?.num}</span>
                                  </label>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </AccordionItem>
                      )
                  )
                ) : (
                  <div />
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
