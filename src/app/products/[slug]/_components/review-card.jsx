import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import moment from "moment";

export default function ReviewCard({ review }) {
  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Avatar>
          <AvatarImage src={review.customer.full_avatar} />
          <AvatarFallback>{review.customer.full_name[0]}</AvatarFallback>
        </Avatar>
        <div className="">
          <div className="flex items-center gap-1">
            <h6 className="font-semibold text-brand-primary">
              {review.customer.full_name}
            </h6>
            <span>|</span>
            <span className="text-secondary">
              {moment(review.created_at).locale("fr-FR").format("DD MMM YYYY")}
            </span>
          </div>
          <div className="flex items-center">
            {Array.from({ length: review.rating }).map((_, key) => (
              <span key={key} className="text-[#FFAE00]">
                ★
              </span>
            ))}
            {Array.from({ length: 5 - review.rating }).map((_, key) => (
              <span key={key} className="text-gray-200">
                ★
              </span>
            ))}
          </div>
        </div>
      </div>
      <div className="pl-12">
        {review.comment ? (
          <p>{review.comment}</p>
        ) : (
          <p className="italic text-muted">Aucun commentaire.</p>
        )}
      </div>
    </div>
  );
}
