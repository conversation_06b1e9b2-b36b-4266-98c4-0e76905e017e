"use client";

import Link from "next/link";
import { useContext, useEffect } from "react";
import { ThumbsUp } from "react-feather";

import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";
import { useAuthContext } from "@/providers/auth-provider";
import { useQueryClient } from "react-query";

export default function OrderSuccessContainer() {
  const queryClient = useQueryClient();
  //   const location = useLocation();
  //   const { paid } = location.state ?? {};
  const { paid } = { paid: "virement" };

  const { currentUser, authenticated } = useAuthContext();
  const { clearAfterCheckout } = useContext(CartAndWishlistProvider);
  // const location = useLocation()
  // const queryParameters = new URLSearchParams(location.search)
  // const invoice = queryParameters.get("invoice") ?? null;

  useEffect(() => {
    clearAfterCheckout();
    queryClient.invalidateQueries("authentication");
  }, []);

  // if(!invoice){
  //   return <Redirect href='/' />
  // }

  return (
    <div>
      <div className="height: calc(30px + 20 * (100vw - 320px) / 1600)"></div>

      <section className="breadscrumb-section section-succes pt-0 mt-4 mb-5">
        <div className="container-lg">
          <div className="row">
            <div className="col-12">
              <div className="breadscrumb-contain breadscrumb-order">
                <div className="order-box">
                  <div className="order-image">
                    <div className="checkmark">
                      <ThumbsUp />
                    </div>
                  </div>

                  <div className="order-contain mt-3">
                    {authenticated &&
                    currentUser &&
                    (currentUser?.type === "professional" ||
                      (currentUser?.type === "seller" && !paid)) ? (
                      <>
                        <h3 className="theme-color">
                          Votre commande a été passée avec succès
                        </h3>
                        <h5 className="text-content">
                          Votre commande a été initié et elle est en cours de
                          traitement.
                        </h5>
                        <h5 className="text-content mb-2">
                          Si vous souhaitez une facture, Vous pouvez la
                          télécharger en cliquant sur le button ci-dessous.
                        </h5>
                        <Link
                          href={`/account/orders`}
                          className="quick-access d-inline-block mt-3"
                        >
                          Accédez à mes commandes
                        </Link>
                      </>
                    ) : null}

                    {authenticated &&
                    currentUser &&
                    currentUser?.type === "seller" &&
                    ["cheque", "effet", "virement", "cod"].includes(paid) ? (
                      <>
                        <h3 className="theme-color">Commande Initié</h3>
                        <h5 className="text-content mb-2">
                          Votre commande a été initié et elle est en cours de
                          traitement.{" "}
                        </h5>
                        <h5 className="text-content mb-2">
                          Notre équipe dévouée travaille activement pour
                          préparer et expédier votre commande dans les plus
                          brefs délais.
                        </h5>
                        <h5 className="text-content mb-2">
                          Si vous souhaitez une facture, vous pouvez la demander
                          à l'équipe support via l'email suivant :
                          <EMAIL>
                        </h5>
                        <Link
                          href={`/account/orders`}
                          className="quick-access d-inline-block mt-3"
                        >
                          Accédez à mes commandes
                        </Link>
                      </>
                    ) : null}

                    {(authenticated &&
                      currentUser &&
                      currentUser?.type === "individual") ||
                    !authenticated ? (
                      <>
                        <h3 className="theme-color">
                          Votre commande a été passée avec succès
                        </h3>
                        <h5 className="text-content">
                          Votre commande a été initié et elle est en cours de
                          traitement.
                        </h5>
                        <h5 className="text-content mb-2">
                          Si vous souhaitez une facture, vous pouvez la demander
                          à l'équipe support via l'email suivant :
                          <EMAIL>
                        </h5>
                        <h5 className="text-content mb-2">
                          Nous vous confirmons que votre commande sera livrée
                          dans un délai maximum de 48 heures.
                        </h5>
                        <h5 className="text-content mb-2">
                          Vous recevrez le bon de commande par votre mail
                        </h5>
                        {authenticated ? (
                          <Link
                            href={`/account/orders`}
                            className="quick-access d-inline-block mt-3"
                          >
                            Accédez à mes commandes
                          </Link>
                        ) : (
                          <Link
                            href={`/check-order`}
                            className="quick-access d-inline-block mt-3"
                          >
                            Vérifier L'état de votre commande
                          </Link>
                        )}
                      </>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
