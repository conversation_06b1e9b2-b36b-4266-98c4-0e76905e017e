import Subscribe from "./_components/subscribe";
export default function Academy() {
  return (
    <div className="academy">
      <div className="container-lg">
        <h1>Restez informé de l'arrivée de notre plateforme d'académie</h1>
        <div className="academy-description">
          <h4 className="mb-4">
            Découvrez Ecowatt Academy, votre future plateforme dédiée à
            l'apprentissage des solutions énergétiques durables. Très bientôt,
            vous aurez accès à une variété de cours en ligne, des tutoriels
            interactifs, et des ressources exclusives pour approfondir vos
            connaissances sur l'énergie solaire et les technologies vertes. Que
            vous soyez professionnel ou passionné par les énergies
            renouvelables, notre académie vous permettra de développer vos
            compétences grâce à des formations adaptées à tous les niveaux.
            Inscrivez votre email pour être informé dès le lancement et
            bénéficier de contenus exclusifs dès l'ouverture !
          </h4>
          <h4>
            Notre plateforme d'académie est en cours de développement. Inscrivez
            votre adresse email pour être notifié dès son lancement et accéder à
            des ressources exclusives sur l'énergie solaire.
          </h4>
        </div>
        <Subscribe />
      </div>
    </div>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Académie",
};
