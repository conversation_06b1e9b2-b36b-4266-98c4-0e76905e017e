"use client";

import { useRegisterNewsletterMutation } from "@/services/common";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import { Formik } from "formik";
import { TailSpin } from "react-loader-spinner";
import { object, string } from "yup";

const formSchema = object({
  email: string().email("Adresse e-mail non valide").required("Required"),
});

export default function Subscribe() {
  const { registerNewsletter, isLoading, isSuccess, error } =
    useRegisterNewsletterMutation();

  function onSubmit(data, actions) {
    registerNewsletter(data, {
      onSuccess() {
        actions.resetForm({
          values: {
            email: "",
          },
        });
      },
    });
  }

  if (isSuccess) {
    return (
      <div className="academy-register">
        <h3 className="text-success text-start mt-2">
          Merci pour votre inscription ! Vous serez informé(e) dès que l'Ecowatt
          Academy sera disponible.
        </h3>
      </div>
    );
  }

  return (
    <div className="academy-register">
      {isSuccess ? (
        <h3 className="text-success text-start mt-2">
          Merci pour votre inscription ! Vous serez informé(e) dès que l'Ecowatt
          Academy sera disponible.
        </h3>
      ) : (
        <Formik
          initialValues={{ email: "" }}
          validationSchema={formSchema}
          onSubmit={onSubmit}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            handleSubmit,
          }) => (
            <form onSubmit={handleSubmit}>
              <div className="form-container">
                <div>
                  <input
                    required
                    type="email"
                    name="email"
                    placeholder="Entrez votre adresse email"
                    className="form-control"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={values.email}
                    disabled={isLoading}
                  />
                </div>
                <button disabled={isLoading}>
                  {isLoading ? (
                    <TailSpin
                      type="ThreeDots"
                      color="#fff"
                      height={20}
                      width={20}
                    />
                  ) : (
                    "S'inscrire"
                  )}
                </button>
              </div>
            </form>
          )}
        </Formik>
      )}

      {error && <ErrorSnackbar message={error.message} />}
    </div>
  );
}
