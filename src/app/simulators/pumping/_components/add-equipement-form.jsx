import { Formik } from "formik";
import { PlusCircle } from "react-feather";
import { number, object, string } from "yup";

const ValidationSchemaForm = object({
  label: string().required("Ce champ est obligatoire"),
  type: string().required("Ce champ est obligatoire"),
  puissance: number()
    .required("Ce champ est obligatoire")
    .typeError("Puissance doit être un nombre valid"),
});

export default function AddEquipementForm({ onSave }) {
  function handleSubmit(values, actions) {
    onSave(values);
    actions.setValues(
      {
        label: "",
        puissance: "",
        type: values.type,
      },
      false
    );
    document.activeElement?.blur();
  }

  return (
    <div className="input-box">
      <Formik
        initialValues={{
          label: "Pompe 1",
          type: "Immergie",
          puissance: "",
        }}
        validationSchema={ValidationSchemaForm}
        onSubmit={handleSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
        }) => (
          <form onSubmit={handleSubmit} className="row g-3 add-equipement-form">
            <div className="col-6">
              <div className="form-floating theme-form-floating">
                <input
                  type="text"
                  className="form-control"
                  id="label"
                  name="label"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.label}
                />
                <label htmlFor="label">Libellé</label>
              </div>
              <span className="error-form">
                {errors.label && touched.label && errors.label}
              </span>
            </div>

            <div className="col-6">
              <div className="form-floating theme-form-floating">
                <select
                  id="type"
                  name="type"
                  className="form-control form-select"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.type}
                >
                  <option value=""></option>
                  <option value="Immergee">Immergée</option>
                  <option value="Surface">Surface</option>
                </select>
                <label htmlFor="type">Type</label>
              </div>
              <span className="error-form">
                {errors.type && touched.type && errors.type}
              </span>
            </div>

            <div className="col-6">
              <div className="form-floating theme-form-floating">
                <input
                  type="text"
                  className="form-control"
                  id="puissance"
                  name="puissance"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.puissance}
                />
                <label htmlFor="puissance">Puissance (KW)</label>
              </div>
              <span className="error-form">
                {errors.puissance && touched.puissance && errors.puissance}
              </span>
            </div>

            <div className="col-6">
              <button
                className="btn rounded-0 gap-2 bg-gray w-100 justify-content-center submit-button"
                type="submit"
              >
                <PlusCircle size={16} />
                Ajouter
              </button>
            </div>
          </form>
        )}
      </Formik>
    </div>
  );
}
