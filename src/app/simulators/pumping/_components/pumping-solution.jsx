import { energyTypes } from "@/app/financement/wafasalaf/_components/step01";
import ProductActions from "@/app/products/[slug]/_components/product-actions";
import ProductPrice from "@/components/product-price";
import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";
import { formatPrice } from "@/lib/helpers";
import { useAuthContext } from "@/providers/auth-provider";
import { useSettingsContext } from "@/providers/settings-provider";
import Link from "next/link";
import { useContext, useState } from "react";
import ReactConfetti from "react-confetti";
import { Phone, ShoppingCart } from "react-feather";

export default function PumpingSolution({
  equipements,
  solution,
  puissance,
  isLoading,
}) {
  const { authenticated } = useAuthContext();
  const settings = useSettingsContext();
  const { addToCartMutation, storeGuestCartItem } = useContext(
    CartAndWishlistProvider
  );

  const [energyTypePrice, setEnergyTypePrice] = useState(energyTypes[0].price);

  if (isLoading) {
    return (
      <div
        className="is__step-container text-center py-5 flex align-items-center d-flex justify-content-center"
        style={{ minHeight: "300px" }}
      >
        <span>CHARGEMENT DE VOTRE SOLUTION...</span>
      </div>
    );
  }

  if (equipements.length === 0) {
    return (
      <div>
        <h6 className="mb-4">
          <p className="text-center">
            Veuillez ajouter vos équipements pour découvrir la solution solaire
            la plus adaptée à vos besoins.
          </p>
        </h6>
      </div>
    );
  }

  const savingsPerYear =
    Number(puissance?.replace(",", ".")) * 1450 * energyTypePrice;

  if (!solution?.id) {
    return (
      <div
        className="is__step-container text-center py-5 flex flex-column align-items-center d-flex justify-content-center"
        style={{ minHeight: "300px" }}
      >
        <h3>AUCUNE SOLUTION TROUVÉE POUR VOTRE EQUIPEMENTS !</h3>
        <h5 className="mt-2">
          Contactez-nous pour obtenir une évaluation de votre situation de la
          part de nos ingénieurs.
        </h5>
        <a href={`tel:${settings?.store_phone}`}>
          <button className="d-flex align-items-center mt-4 gap-2 btn btn-animation">
            <Phone />
            <h6>Je passe un appel</h6>
          </button>
        </a>
      </div>
    );
  }

  return (
    <div>
      <h6 className="mb-4">
        Voici votre solution. Elle est convenable à vos équipements.
      </h6>

      <div className="row product-section">
        <ReactConfetti
          width={window.screen.width}
          height={window.screen.height}
        />
        <div className="col-sm-5 mb-4 mb-sm-0">
          <Link href={`/products/${solution?.slug}`}>
            <img
              src={solution?.full_image}
              className="img-fluid lazyload h-sm-100 ratio-1x1 object-fit-cover text-truncate"
              alt={solution?.title}
            />
          </Link>
          <Link
            href={`/products/${solution?.slug}`}
            className="btn btn-sm mt-2 gap-2 theme-bg-color text-white w-100 rounded-0"
          >
            <ShoppingCart size={18} />
            <span>Acheter Maintenant</span>
          </Link>
        </div>
        <div className="col-sm-7 d-flex flex-column right-box-contain text-start">
          <h6 className="mb-2 font-bold">#{solution?.ref}</h6>
          <Link href={`/products/${solution?.slug}`}>
            <h6 className="fw-bold">{solution?.title}</h6>
          </Link>

          <div className="mt-3">
            {solution?.is_active !== 0 && solution?.is_active !== 3 ? (
              <>
                {solution?.is_active === 1 && (
                  <div className="h6 text-success">
                    <strong>En stock</strong>
                  </div>
                )}
                {solution?.is_active === 2 && (
                  <div className="h6 text-orange">
                    <strong>En arrivage</strong>
                  </div>
                )}
              </>
            ) : (
              <div className="h6 text-danger mt-4">
                <strong>En rupture de stock</strong>
              </div>
            )}
          </div>
          <ProductPrice product={solution} />

          <div className="mt-auto">
            <div className="mt-2 border p-2 bg-secondary rounded">
              <select
                className="form-select form-control mb-2 p-2"
                value={energyTypePrice}
                onChange={(e) => setEnergyTypePrice(e.target.value)}
              >
                {energyTypes.map((type) => (
                  <option key={type.key} value={type.price}>
                    {type.label}
                  </option>
                ))}
              </select>
              <span className="text-left">
                <span style={{ color: "#ff0000" }} className="fw-bold">
                  {formatPrice(savingsPerYear)} DH
                </span>{" "}
                économisés par an grâce à cette solution, ce qui correspond à{" "}
                <span style={{ color: "#ff0000" }} className="fw-bold">
                  {formatPrice(savingsPerYear / 12)} DH
                </span>{" "}
                par mois.
              </span>
            </div>
            <ProductActions product={solution} />
          </div>
        </div>
      </div>
    </div>
  );
}
