import { Trash2 } from "react-feather";

export default function ListEquipements({ equipements, onDeleteEquipement }) {
  return (
    <ul className="equipements">
      {equipements.map((equipement, key) => (
        <li key={key} className="equipements-item">
          <div>
            <h6 className="fw-bold mb-2">{equipement.label}</h6>
            <h6 className="fw-medium">
              {equipement.type} | {equipement.puissance} KW
            </h6>
          </div>
          <div>
            <button
              className="btn btn-sm"
              onClick={() => onDeleteEquipement(key)}
            >
              <Trash2 size={14} />
            </button>
          </div>
        </li>
      ))}
      {equipements.length === 0 && (
        <li className="equipements-item">
          Aucun équipement n'est ajouté pour le moment.
        </li>
      )}
    </ul>
  );
}
