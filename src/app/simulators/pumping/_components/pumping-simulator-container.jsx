"use client";

import "@/css/simulators/pumping-simulator.css";
import { useGetPumpingSimulatorSolution } from "@/services/common";
import { useState } from "react";
import { ChevronLeft, ChevronRight } from "react-feather";
import AddEquipementForm from "./add-equipement-form";
import ListEquipements from "./list-equipements";
import PumpingSolution from "./pumping-solution";

export default function PumpingSimulatorContainer() {
  const [view, setView] = useState("equipements");
  const [equipements, setEquipements] = useState([]);

  function onSaveEquipement(equipement) {
    setEquipements((equipements) => [...equipements, equipement]);
  }

  function onDeleteEquipement(index) {
    setEquipements((equipements) => equipements.filter((_, i) => i !== index));
  }

  return (
    <div className="container-lg ps-wrapper">
      <div className="ps-container border">
        {view === "equipements" && (
          <Equipements
            equipements={equipements}
            onSaveEquipement={onSaveEquipement}
            onDeleteEquipement={onDeleteEquipement}
            setView={setView}
          />
        )}
        {view === "solution" && (
          <Solution equipements={equipements} setView={setView} />
        )}
      </div>
    </div>
  );
}

function Equipements({
  equipements,
  onSaveEquipement,
  onDeleteEquipement,
  setView,
}) {
  return (
    <aside className="sp-side sp-side-equipements">
      <div className="sp-side-title">
        <h3>INDIQUER VOS EQUIPEMENTS</h3>
      </div>
      <div>
        <div className="p-3 border-bottom">
          <h6 className="mb-4">Ajouter un équipement</h6>
          <AddEquipementForm
            equipements={equipements}
            onSave={onSaveEquipement}
          />
        </div>
        <div className="my-4 p-3">
          <h6 className="mb-4">Liste des équipements</h6>
          <ListEquipements
            equipements={equipements}
            onDeleteEquipement={onDeleteEquipement}
          />
        </div>
        {equipements.length > 0 && (
          <button
            className="btn btn-animation text-white rounded-0 w-100"
            onClick={() => setView("solution")}
          >
            <ChevronRight />
            <span>Découvrir votre solution</span>
          </button>
        )}
      </div>
    </aside>
  );
}

function Solution({ equipements, setView }) {
  const { data, isLoading } = useGetPumpingSimulatorSolution({
    equipements,
  });

  const solution = data?.solution;

  return (
    <aside className="sp-side sp-side-solution border-start">
      <div className="sp-side-title d-flex align-items-center justify-content-between">
        <div role="button" onClick={() => setView("equipements")}>
          <ChevronLeft />
        </div>
        <h3>VOTRE SOLUTION</h3>
        <div></div>
      </div>
      <div className="p-3">
        <PumpingSolution
          equipements={equipements}
          solution={solution}
          puissance={data?.puissance}
          isLoading={isLoading}
        />
      </div>
    </aside>
  );
}
