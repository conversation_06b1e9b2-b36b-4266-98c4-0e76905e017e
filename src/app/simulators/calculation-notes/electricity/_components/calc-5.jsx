"use client";

import { useState } from "react";

export default function Calc5() {
  const [result, setResult] = useState([]);
  const [formData, setFormData] = useState({
    value: 20,
    unit: "KVA",
  });

  function handleInputChange(e) {
    setFormData((formData) => ({
      ...formData,
      [e.target.name]: e.target.value,
    }));
  }

  function handleSubmit(e) {
    e.preventDefault();

    const puissance = data.transformateur?.find(
      ({ unit }) =>
        formData.value > unit[formData.unit]?.min &&
        formData.value <= unit[formData.unit]?.max
    )?.puissance;

    setResult([
      {
        label: `Puissance normalisée`,
        value: puissance,
        suffix: "KVA",
      },
    ]);
  }

  return (
    <div className="space-y-4">
      <form action="" className="space-y-4" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-2">
          <div className="flex-1">
            <label htmlFor="value">Valeur :</label>
            <input
              type="number"
              id="value"
              name="value"
              onChange={handleInputChange}
              value={formData.value}
              step="0.01"
              className="form-control"
            />
          </div>
          <div className="flex-1">
            <label htmlFor="unit">Unité :</label>
            <select
              id="unit"
              name="unit"
              onChange={handleInputChange}
              value={formData.unit}
              className="form-control form-select"
            >
              <option value="KVA">KVA</option>
              <option value="KW">KW</option>
              <option value="A">A</option>
            </select>
          </div>
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2.5 bg-[#FF6565] text-md font-medium text-white rounded-md"
        >
          Calculer
        </button>
      </form>

      {/* Result */}
      <div className="bg-white rounded-md p-3 space-y-2">
        <span className="text-brand-primary font-semibold">Résultats</span>
        <ul className="flex flex-col gap-2">
          {result.map((res, idx) => (
            <li key={idx} className="space-x-2 font-medium">
              <span className="italic">{res.label}</span>
              <span>:</span>
              <span className="font-semibold text-brand-primary">
                {res.value} {res.suffix}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

const data = {
  transformateur: [
    {
      puissance: 50,
      unit: {
        KVA: { min: 0, max: 50 },
        KW: { min: 0, max: 40 },
        A: { min: 0, max: 72.16 },
      },
    },
    {
      puissance: 100,
      unit: {
        KVA: { min: 50, max: 100 },
        KW: { min: 40, max: 80 },
        A: { min: 72.16, max: 144.33 },
      },
    },
    {
      puissance: 160,
      unit: {
        KVA: { min: 100, max: 160 },
        KW: { min: 80, max: 128 },
        A: { min: 144.33, max: 230.94 },
      },
    },
    {
      puissance: 200,
      unit: {
        KVA: { min: 160, max: 200 },
        KW: { min: 128, max: 160 },
        A: { min: 230.94, max: 288.67 },
      },
    },
    {
      puissance: 250,
      unit: {
        KVA: { min: 200, max: 250 },
        KW: { min: 160, max: 200 },
        A: { min: 288.67, max: 360.84 },
      },
    },
    {
      puissance: 315,
      unit: {
        KVA: { min: 250, max: 315 },
        KW: { min: 200, max: 252 },
        A: { min: 360.84, max: 454.66 },
      },
    },
    {
      puissance: 400,
      unit: {
        KVA: { min: 315, max: 400 },
        KW: { min: 252, max: 320 },
        A: { min: 454.66, max: 577.35 },
      },
    },
    {
      puissance: 500,
      unit: {
        KVA: { min: 400, max: 500 },
        KW: { min: 320, max: 400 },
        A: { min: 577.35, max: 721.68 },
      },
    },
    {
      puissance: 630,
      unit: {
        KVA: { min: 500, max: 630 },
        KW: { min: 400, max: 504 },
        A: { min: 721.68, max: 909.32 },
      },
    },
    {
      puissance: 800,
      unit: {
        KVA: { min: 630, max: 800 },
        KW: { min: 504, max: 640 },
        A: { min: 909.32, max: 1154.7 },
      },
    },
    {
      puissance: 1000,
      unit: {
        KVA: { min: 800, max: 1000 },
        KW: { min: 640, max: 800 },
        A: { min: 1154.7, max: 1443.37 },
      },
    },
    {
      puissance: 1250,
      unit: {
        KVA: { min: 1000, max: 1250 },
        KW: { min: 800, max: 1000 },
        A: { min: 1443.37, max: 1804.22 },
      },
    },
    {
      puissance: 1600,
      unit: {
        KVA: { min: 1250, max: 1600 },
        KW: { min: 1000, max: 1280 },
        A: { min: 1804.22, max: 2309.4 },
      },
    },
    {
      puissance: 2000,
      unit: {
        KVA: { min: 1600, max: 2000 },
        KW: { min: 1280, max: 1600 },
        A: { min: 2309.4, max: 2886.75 },
      },
    },
    {
      puissance: 2500,
      unit: {
        KVA: { min: 2000, max: 2500 },
        KW: { min: 1600, max: 2000 },
        A: { min: 2886.75, max: 3608.44 },
      },
    },
  ],
};
