"use client";

import { useState } from "react";

const typeInstallation = {
  monophase: 2,
  triphase: 1.732050808,
  continu: 2,
};

export default function Calc1() {
  const [result, setResult] = useState([]);
  const [formData, setFormData] = useState({
    typeInstallation: "monophase",
    resistance: "0.0237",
    tension: 0,
    sectionCable: 0,
    chuteTension: 0,
    courant: 0,
  });

  function handleChange(e) {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  }

  function handleSubmit(e) {
    e.preventDefault();

    if (!formData.tension || !formData.sectionCable || !formData.courant) {
      return setResult([]);
    }

    const REACTANCE = 0.00008;
    const COS = 0.8;
    const SIN = 0.6;

    const chuteTensionV = (formData.chuteTension * formData.tension) / 100;

    const longeurCable = ["monophase", "triphase"].includes(
      formData.typeInstallation
    )
      ? chuteTensionV /
        (typeInstallation[formData.typeInstallation] *
          formData.courant *
          ((formData.resistance / formData.sectionCable) * COS +
            REACTANCE * SIN))
      : chuteTensionV /
        (2 * formData.courant * (formData.resistance / formData.sectionCable));

    setResult([
      {
        label: "Longueur du Câble électrique",
        value: longeurCable.toFixed(2),
        suffix: "m",
      },
    ]);
  }

  return (
    <div className="space-y-4">
      <form action="" className="space-y-4" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="typeInstallation"
            className="text-xs text-brand-primary font-semibold"
          >
            Type d'installation
          </label>
          <select
            name="typeInstallation"
            id="typeInstallation"
            onChange={handleChange}
            value={formData.typeInstallation}
            className="form-select form-control"
          >
            <option value="monophase">Monophasé</option>
            <option value="triphase">Triphasé</option>
            <option value="continu">Continu</option>
          </select>
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="resistance"
            className="text-xs text-brand-primary font-semibold"
          >
            Résistance linéique en Ω/m ( R )
          </label>
          <select
            name="resistance"
            id="resistance"
            onChange={handleChange}
            value={formData.resistance}
            className="form-select form-control"
          >
            <option value="0.0237">Cuivre</option>
            <option value="0.0376">Alimunium</option>
          </select>
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="tension"
            className="text-xs text-brand-primary font-semibold"
          >
            Tension en V (U)
          </label>
          <input
            id="tension"
            name="tension"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.tension}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="sectionCable"
            className="text-xs text-brand-primary font-semibold"
          >
            Section du câble électrique en mm² (S)
          </label>
          <input
            id="sectionCable"
            name="sectionCable"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.sectionCable}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="chuteTension"
            className="text-xs text-brand-primary font-semibold"
          >
            Chute de tension en % (DU%)
          </label>
          <input
            id="chuteTension"
            name="chuteTension"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.chuteTension}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="courant"
            className="text-xs text-brand-primary font-semibold"
          >
            Courant en A (I)
          </label>
          <input
            id="courant"
            name="courant"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.courant}
          />
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2.5 bg-[#FF6565] text-md font-medium text-white rounded-md"
        >
          Calculer
        </button>
      </form>

      {/* Result */}
      <div className="bg-white rounded-md p-3 space-y-2 drop-shadow-sm border !border-brand-prim">
        <span className="text-brand-primary font-semibold">Résultats</span>
        <ul className="flex flex-col gap-2">
          {result.map((res, idx) => (
            <li key={idx} className="space-x-2 font-medium">
              <span className="italic">{res.label}</span>
              <span>:</span>
              <span className="font-semibold text-brand-primary">
                {res.value} {res.suffix}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
