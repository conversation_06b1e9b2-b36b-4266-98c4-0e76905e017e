"use client";

import { Trash2Icon } from "lucide-react";
import { useState } from "react";

const nbrDiversSectionMapping = {
  "1x1.5": 6.4,
  "1x2.5": 6.8,
  "1x4": 7.2,
  "1x6": 8.2,
  "1x10": 9.2,
  "1x16": 10.5,
  "1x25": 12.5,
  "1x35": 13.5,
  "1x50": 15.0,
  "1x70": 17.0,
  "1x95": 19.0,
  "1x120": 21.0,
  "1x150": 23.0,
  "1x185": 25.5,
  "1x240": 28.5,
  "1x300": 31.0,
  "1x400": 34.5,
  "1x500": 38.5,
  "1x630": 43.0,
  "2x1.5": 10.5,
  "2x2.5": 11.5,
  "2x4": 13.0,
  "2x6": 14.0,
  "2x10": 16.0,
  "2x16": 18.5,
  "2x25": 22.0,
  "2x35": 24.5,
  "3x1.5": 11.0,
  "3x2.5": 12.5,
  "3x4": 13.5,
  "3x6": 15.0,
  "3x10": 17.0,
  "3x16": 16.5,
  "3x25": 23.5,
  "3x35": 26.0,
  "3x50": 29.0,
  "3x70": 34.0,
  "3x95": 38.5,
  "3x120": 42.5,
  "3x150": 47.5,
  "3x185": 53.0,
  "3x240": 59.5,
  "3x300": 66.0,
  "4x1.5": 12.0,
  "4x2.5": 13.0,
  "4x4": 14.5,
  "4x6": 16.0,
  "4x10": 18.5,
  "4x16": 21.0,
  "4x25": 25.5,
  "4x35": 28.5,
  "4x50": 32.5,
  "4x70": 37.5,
  "4x95": 42.5,
  "4x120": 47.5,
  "4x150": 52.5,
  "4x185": 59.0,
  "4x240": 66.5,
  "4x300": 73.5,
  "5x1.5": 13.0,
  "5x2.5": 14.5,
  "5x4": 16.0,
  "5x6": 17.5,
  "5x10": 20.0,
  "5x16": 23.0,
  "5x25": 28.0,
  "5x35": 34.0,
  "5x50": 36.0,
  "5x70": 43.0,
  "5x95": 47.0,
};

const cablePathCalcMapping = {
  33: {
    "65/33": 2145,
    "95/33": 3135,
    "125/33": 4125,
    "155/33": 5115,
    "215/33": 7095,
    "305/33": 10065,
    "365x33": 12045,
  },
  63: {
    "95/63": 5985,
    "125/63": 7875,
    "155/63": 9765,
    "215/63": 13545,
    "305/63": 19215,
    "365/63": 22995,
    "500/63": 31500,
  },
};

export default function Calc4() {
  const [result, setResult] = useState([]);
  const [rows, setRows] = useState([
    {
      nbDivers: "1",
      section: "35",
      qty: 1,
    },
  ]);

  function handleInputChange(e) {
    const rowKey = e.target.getAttribute("data-key");

    setRows((rows) =>
      rows.map((row, key) => {
        if (key != rowKey) return row;

        row[e.target.name] = e.target.value;
        return row;
      })
    );
  }

  function handleAddRow() {
    setRows((rows) => [
      ...rows,
      {
        nbDivers: "",
        section: "",
        qty: 1,
      },
    ]);
  }

  function handleDeleteRow(rowKey) {
    setRows((rows) => rows.filter((_, key) => key != rowKey));
  }

  function handleSubmit(e) {
    e.preventDefault();

    const S = rows.reduce((acc, row) => {
      const A = `${row.nbDivers}x${row.section}`;
      const maxi = nbrDiversSectionMapping[A];

      const B = (2 * (maxi / 2)) ** 2 * row.qty;

      return acc + B;
    }, 0);

    const R = S * 1.2 * 1.2;

    const chemin33 = Object.entries(cablePathCalcMapping["33"])?.reduce(
      (prev, curr) => {
        return Math.abs(curr[1] - R) < Math.abs(prev[1] - R) ? curr : prev;
      }
    );

    const chemin63 = Object.entries(cablePathCalcMapping["63"])?.reduce(
      (prev, curr) => {
        return Math.abs(curr[1] - R) < Math.abs(prev[1] - R) ? curr : prev;
      }
    );

    let res33 = "";
    let res63 = "";

    if (R < cablePathCalcMapping["33"]["365x33"]) {
      res33 = chemin33[0];
    }

    if (R < cablePathCalcMapping["63"]["500/63"]) {
      res63 = chemin63[0];
    }

    setResult([
      {
        label: "Dimension du chemin de câble",
        value: `${res33} ${res33 && res63 && "ou"} ${res63}`,
      },
    ]);
  }

  return (
    <div className="space-y-4">
      <form action="" className="space-y-4" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-2">
          {rows.map((row, key) => (
            <div key={key} className="grid grid-cols-12 gap-2">
              <div className="col-span-4">
                <label htmlFor={`nbDivers-${key}`}>Conducteurs:</label>
                <select
                  data-key={key}
                  id={`nbDivers-${key}`}
                  name="nbDivers"
                  onChange={handleInputChange}
                  value={row.nbDivers}
                  className="form-control form-select"
                >
                  <option value=""></option>
                  <option value="1">1</option>
                  <option value="2">2</option>
                  <option value="3">3</option>
                  <option value="4">4</option>
                  <option value="5">5</option>
                </select>
              </div>

              <div className="col-span-4">
                <label htmlFor={`section-${key}`}>Section:</label>
                <select
                  data-key={key}
                  id={`section-${key}`}
                  name="section"
                  onChange={handleInputChange}
                  value={row.section}
                  className="form-control form-select"
                >
                  <option value=""></option>
                  {Object.keys(nbrDiversSectionMapping)
                    ?.filter((item) => item.split("x")[0] === row.nbDivers)
                    ?.map((nbr) => nbr.split("x")[1])
                    ?.map((item) => (
                      <option key={item} value={item}>
                        {item}
                      </option>
                    ))}
                </select>
              </div>
              <div className="col-span-3">
                <label htmlFor={`qty-${key}`}>Qte(Unité):</label>
                <input
                  data-key={key}
                  type="number"
                  id={`qty-${key}`}
                  name="qty"
                  onChange={handleInputChange}
                  value={row.qty}
                  min="0"
                  className="form-control"
                />
              </div>
              <div className="col-span-1 flex flex-col">
                {rows.length > 1 && (
                  <>
                    <label className="invisible">X</label>
                    <div className="flex-1 flex items-center">
                      <div role="button" onClick={() => handleDeleteRow(key)}>
                        <Trash2Icon size={14} />
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
        <button
          type="button"
          className="mb-5 w-fit px-4 py-1 bg-[#FF6565] text-md font-medium text-white rounded-md"
          onClick={() => handleAddRow()}
        >
          + Ajouter
        </button>
        <button
          type="submit"
          className="w-full px-4 py-2.5 bg-[#FF6565] text-md font-medium text-white rounded-md"
        >
          Calculer
        </button>
      </form>

      {/* Result */}
      <div className="bg-white rounded-md p-3 space-y-2">
        <span className="text-brand-primary font-semibold">Résultats</span>
        <ul className="flex flex-col gap-2">
          {result.map((res, idx) => (
            <li key={idx} className="space-x-2 font-medium">
              <span className="italic">{res.label}</span>
              <span>:</span>
              <span className="font-semibold text-brand-primary">
                {res.value} {res.suffix}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
