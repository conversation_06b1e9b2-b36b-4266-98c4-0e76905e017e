"use client";

import { useState } from "react";

export default function Calc4() {
  const [result, setResult] = useState([]);
  const [formData, setFormData] = useState({
    power: 0,
    desiredProduction: 0,
  });

  function handleChange(e) {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  }

  function handleSubmit(e) {
    e.preventDefault();

    if (!formData.desiredProduction) {
      return setResult([]);
    }

    const puissanceElecKW = formData.power / (formData.desiredProduction / 100);
    const puissanceElecHP = puissanceElecKW / 0.735499;

    setResult([
      {
        label: "Puissance électrique du GMP",
        value: puissanceElecKW.toFixed(2),
        suffix: "kW",
      },
      {
        label: "Puissance électrique du GMP",
        value: puissanceElecHP.toFixed(2),
        suffix: "CV",
      },
    ]);
  }

  return (
    <div className="space-y-4">
      <form action="" className="space-y-4" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="power"
            className="text-xs text-brand-primary font-semibold"
          >
            Puissance hydraulique (kW)
          </label>
          <input
            id="power"
            name="power"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.power}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="desiredProduction"
            className="text-xs text-brand-primary font-semibold"
          >
            Rendement souhaité du GMP (en %)
          </label>
          <input
            id="desiredProduction"
            name="desiredProduction"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.desiredProduction}
          />
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2.5 bg-[#FF6565] text-md font-medium text-white rounded-md"
        >
          Calculer
        </button>
      </form>

      {/* Result */}
      <div className="bg-white rounded-md p-3 space-y-2">
        <span className="text-brand-primary font-semibold">Résultats</span>
        <ul className="flex flex-col gap-2">
          {result.map((res, idx) => (
            <li key={idx} className="space-x-2 font-medium">
              <span className="italic">{res.label}</span>
              <span>:</span>
              <span className="font-semibold text-brand-primary">
                {res.value} {res.suffix}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
