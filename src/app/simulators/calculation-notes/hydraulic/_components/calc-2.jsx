"use client";

import { useState } from "react";

export default function Calc2() {
  const [result, setResult] = useState([]);
  const [formData, setFormData] = useState({
    typePompe: "",
    hmt: 0,
    debit: 0,
    pgmp: 0,
  });

  function handleChange(e) {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  }

  function handleSubmit(e) {
    e.preventDefault();

    if (!formData.pgmp) {
      return setResult([]);
    }

    const gmpproduction =
      ((1000 * 9.81 * formData.hmt * formData.debit) /
        (3600 * formData.pgmp * 1000)) *
      100;

    setResult([
      {
        label: "Rendement du GMP",
        value: gmpproduction.toFixed(2),
        suffix: "%",
      },
    ]);
  }

  return (
    <div className="space-y-4">
      <form action="" className="space-y-4" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="typePompe"
            className="text-xs text-brand-primary font-semibold"
          >
            Type de pompe
          </label>
          <select
            name="typePompe"
            id="typePompe"
            onChange={handleChange}
            value={formData.typePompe}
            className="form-select form-control"
          >
            <option value="immergee">Immergée</option>
            <option value="surface">Surface</option>
          </select>
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="hmt"
            className="text-xs text-brand-primary font-semibold"
          >
            HMT (m)
          </label>
          <input
            id="hmt"
            name="hmt"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.hmt}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="debit"
            className="text-xs text-brand-primary font-semibold"
          >
            Débit D (m3/h)
          </label>
          <input
            id="debit"
            name="debit"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.debit}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="lt"
            className="text-xs text-brand-primary font-semibold"
          >
            Puissance électrique du GMP P (kW)
          </label>
          <input
            id="pgmp"
            name="pgmp"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.pgmp}
          />
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2.5 bg-[#FF6565] text-md font-medium text-white rounded-md"
        >
          Calculer
        </button>
      </form>

      {/* Result */}
      <div className="bg-white rounded-md p-3 space-y-2">
        <span className="text-brand-primary font-semibold">Résultats</span>
        <ul className="flex flex-col gap-2">
          {result.map((res, idx) => (
            <li key={idx} className="space-x-2 font-medium">
              <span className="italic">{res.label}</span>
              <span>:</span>
              <span className="font-semibold text-brand-primary">
                {res.value} {res.suffix}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
