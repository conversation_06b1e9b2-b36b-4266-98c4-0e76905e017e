"use client";

import { useState } from "react";

export default function Calc3() {
  const [result, setResult] = useState([]);
  const [formData, setFormData] = useState({
    hmt: 0,
    debit: 0,
  });

  function handleChange(e) {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  }

  function handleSubmit(e) {
    e.preventDefault();

    const puissance = (2.725 * formData.debit * formData.hmt) / 1000;

    setResult([
      {
        label: "Puissance hydraulique du GMP",
        value: puissance.toFixed(2),
        suffix: "kW",
      },
    ]);
  }

  return (
    <div className="space-y-4">
      <form action="" className="space-y-4" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="hmt"
            className="text-xs text-brand-primary font-semibold"
          >
            HMT (m)
          </label>
          <input
            id="hmt"
            name="hmt"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.hmt}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="debit"
            className="text-xs text-brand-primary font-semibold"
          >
            Débit D (m3/h)
          </label>
          <input
            id="debit"
            name="debit"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.debit}
          />
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2.5 bg-[#FF6565] text-md font-medium text-white rounded-md"
        >
          Calculer
        </button>
      </form>

      {/* Result */}
      <div className="bg-white rounded-md p-3 space-y-2">
        <span className="text-brand-primary font-semibold">Résultats</span>
        <ul className="flex flex-col gap-2">
          {result.map((res, idx) => (
            <li key={idx} className="space-x-2 font-medium">
              <span className="italic">{res.label}</span>
              <span>:</span>
              <span className="font-semibold text-brand-primary">
                {res.value} {res.suffix}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
