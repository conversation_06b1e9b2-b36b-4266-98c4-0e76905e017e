"use client";

import { useState } from "react";

export default function Calc5() {
  const [result, setResult] = useState([]);
  const [formData, setFormData] = useState({
    debit: 0,
    vitesse: 0,
  });

  function handleChange(e) {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  }

  function handleSubmit(e) {
    e.preventDefault();

    if (!formData.vitesse) {
      return setResult([]);
    }

    const diametre =
      Math.sqrt((4 * formData.debit) / (Math.PI * formData.vitesse * 3600)) *
      1000;

    setResult([
      {
        label: "Diamètre de la conduite",
        value: diametre.toFixed(2),
        suffix: "mm",
      },
    ]);
  }

  return (
    <div className="space-y-4">
      <form action="" className="space-y-4" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="debit"
            className="text-xs text-brand-primary font-semibold"
          >
            Débit de l'écoulement D (m3/h)
          </label>
          <input
            id="debit"
            name="debit"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.debit}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="vitesse"
            className="text-xs text-brand-primary font-semibold"
          >
            Vitesse de l'écoulement à l'intérieur de la conduite (m/s)
          </label>
          <input
            id="vitesse"
            name="vitesse"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.vitesse}
          />
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2.5 bg-[#FF6565] text-md font-medium text-white rounded-md"
        >
          Calculer
        </button>
      </form>

      {/* Result */}
      <div className="bg-white rounded-md p-3 space-y-2">
        <span className="text-brand-primary font-semibold">Résultats</span>
        <ul className="flex flex-col gap-2">
          {result.map((res, idx) => (
            <li key={idx} className="space-x-2 font-medium">
              <span className="italic">{res.label}</span>
              <span>:</span>
              <span className="font-semibold text-brand-primary">
                {res.value} {res.suffix}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
