"use client";

import { useState } from "react";

export default function Calc1() {
  const [result, setResult] = useState([]);
  const [formData, setFormData] = useState({
    typePompe: "",
    ha: 0,
    hr: 0,
    lt: 0,
  });

  function handleChange(e) {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  }

  function handleSubmit(e) {
    e.preventDefault();

    const pc = (10 / 100) * formData.lt;

    const hmt = Number(formData.ha) + Number(formData.hr) + pc;

    setResult([
      {
        label: "Hauteur manométrique totale (HMT)",
        value: hmt.toFixed(2),
        suffix: "m",
      },
    ]);
  }

  return (
    <div className="space-y-4">
      <form action="" className="space-y-4" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="typePompe"
            className="text-xs text-brand-primary font-semibold"
          >
            Type de pompe
          </label>
          <select
            name="typePompe"
            id="typePompe"
            onChange={handleChange}
            value={formData.typePompe}
            className="form-select form-control"
          >
            <option value="immergee">Immergée</option>
            <option value="surface">Surface</option>
          </select>
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="ha"
            className="text-xs text-brand-primary font-semibold"
          >
            Hauteur manométrique d'aspiration Ha (m)
          </label>
          <input
            id="ha"
            name="ha"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.ha}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="hr"
            className="text-xs text-brand-primary font-semibold"
          >
            Hauteur de refoulement Hr (m)
          </label>
          <input
            id="hr"
            name="hr"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.hr}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="lt"
            className="text-xs text-brand-primary font-semibold"
          >
            Longueur du tuyau L (m)
          </label>
          <input
            id="lt"
            name="lt"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.lt}
          />
        </div>

        <button
          type="submit"
          className="w-full px-4 py-2.5 bg-[#FF6565] text-md font-medium text-white rounded-md"
        >
          Calculer
        </button>
      </form>

      {/* Result */}
      <div className="bg-white rounded-md p-3 space-y-2">
        <span className="text-brand-primary font-semibold">Résultats</span>
        <ul className="flex flex-col gap-2">
          {result.map((res, idx) => (
            <li key={idx} className="space-x-2 font-medium">
              <span className="italic">{res.label}</span>
              <span>:</span>
              <span className="font-semibold text-brand-primary">
                {res.value} {res.suffix}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
