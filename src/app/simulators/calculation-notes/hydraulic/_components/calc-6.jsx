"use client";

import { useState } from "react";

export default function Calc6() {
  const [result, setResult] = useState([]);
  const [formData, setFormData] = useState({
    longeurConduite: 0,
    diametreInterne: 0,
    coefficientPerteCharge: 0,
    typeTuyau: "0.025",
  });

  function handleChange(e) {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  }

  function handleSubmit(e) {
    e.preventDefault();

    const VME = 1.5;
    const GP = 9.81;

    if (!formData.longeurConduite || !formData.diametreInterne) {
      return setResult([]);
    }

    const perteChargeHydrauliqueHF =
      formData.typeTuyau *
      (formData.longeurConduite / formData.diametreInterne) *
      (VME ** 2 / (2 * GP));

    const perteChargeHydrauliqueHS = (10 / 100) * perteChargeHydrauliqueHF;

    const total = perteChargeHydrauliqueHF + perteChargeHydrauliqueHS;

    setResult([
      {
        label: "Pertes de charges hydrauliques régulières Hf",
        value: perteChargeHydrauliqueHF.toFixed(2),
        suffix: "mC",
      },
      {
        label: "Pertes de charges hydrauliques singulières Hs",
        value: perteChargeHydrauliqueHS.toFixed(2),
        suffix: "mC",
      },
      {
        label: "Pertes de charges hydrauliques totales",
        value: total.toFixed(2),
        suffix: "mC",
      },
    ]);
  }

  return (
    <div className="space-y-4">
      <form action="" className="space-y-4" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="longeurConduite"
            className="text-xs text-brand-primary font-semibold"
          >
            Longueur de la conduite (m)
          </label>
          <input
            id="longeurConduite"
            name="longeurConduite"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.longeurConduite}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="diametreInterne"
            className="text-xs text-brand-primary font-semibold"
          >
            Diamètre intérieur de la conduite (m)
          </label>
          <input
            id="diametreInterne"
            name="diametreInterne"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.diametreInterne}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="coefficientPerteCharge"
            className="text-xs text-brand-primary font-semibold"
          >
            Coefficient de pertes de charge régulières f
          </label>
          <input
            id="coefficientPerteCharge"
            name="coefficientPerteCharge"
            type="number"
            className="form-control"
            onChange={handleChange}
            value={formData.coefficientPerteCharge}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label
            htmlFor="typeTuyau"
            className="text-xs text-brand-primary font-semibold"
          >
            Type de tuyau
          </label>
          <select
            name="typeTuyau"
            id="typeTuyau"
            onChange={handleChange}
            value={formData.typeTuyau}
            className="form-select form-control"
          >
            <option value="0.025">PVC / PEHD</option>
            <option value="0.035">Acier galvanisé</option>
            <option value="0.045">Fonte</option>
          </select>
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2.5 bg-[#FF6565] text-md font-medium text-white rounded-md"
        >
          Calculer
        </button>
      </form>

      {/* Result */}
      <div className="bg-white rounded-md p-3 space-y-2">
        <span className="text-brand-primary font-semibold">Résultats</span>
        <ul className="flex flex-col gap-2">
          {result.map((res, idx) => (
            <li key={idx} className="space-x-2 font-medium">
              <span className="italic">{res.label}</span>
              <span>:</span>
              <span className="font-semibold text-brand-primary">
                {res.value} {res.suffix}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
