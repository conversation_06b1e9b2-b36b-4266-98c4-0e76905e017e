import BackButton from "@/components/back-button";
import SectionTitle from "@/components/section-title";
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON><PERSON>le,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import Link from "next/link";
import Calc1 from "./_components/calc-1";
import Calc2 from "./_components/calc-2";
import Calc3 from "./_components/calc-3";
import Calc4 from "./_components/calc-4";
import Calc5 from "./_components/calc-5";
import Calc6 from "./_components/calc-6";

export default function HydraulicCalculationNotePage() {
  return (
    <div className="container-lg">
      <section className="flex flex-col gap-12 pb-40">
        <div className="flex items-center gap-2">
          <Link href="/simulators/calculation-notes">
            <BackButton />
          </Link>
          <SectionTitle title="Hydraulique" className="mb-0" />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10">
          {hydraulicCalculationNotes.map((calculation, index) => (
            <Sheet key={calculation.title}>
              <SheetTrigger>
                <div className="group bg-[var(--theme-color-light)] shadow h-full rounded-lg overflow-hidden flex flex-col items-center">
                  <div className="px-6 py-12">
                    <div className="group-hover:scale-110 group-hover:rotate-6 transition-transform duration-300">
                      {calculation.icon}
                    </div>
                  </div>
                  <div className="bg-theme p-6 text-white mt-auto text-center w-full">
                    <h4 className="font-medium text-sm xl:text-lg">
                      {calculation.title}
                    </h4>
                  </div>
                </div>
              </SheetTrigger>
              <SheetContent className="w-[600px] !max-w-full max-h-screen overflow-y-auto">
                <SheetHeader className="flex items-center gap-2">
                  <SheetClose asChild>
                    <BackButton />
                  </SheetClose>
                  <SheetTitle className="text-brand-primary">
                    <h3 className="relative w-fit font-semibold">
                      {calculation.title}
                    </h3>
                  </SheetTitle>
                </SheetHeader>
                <div className="!bg-secondary p-3 lg:p-4 mt-8 overflow-scroll rounded-lg">
                  {calculation.component}
                </div>
              </SheetContent>
            </Sheet>
          ))}
        </div>
      </section>
    </div>
  );
}

const hydraulicCalculationNotes = [
  {
    title: "Hauteur manométrique totale",
    icon: (
      <svg
        width="63"
        height="63"
        viewBox="0 0 63 63"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M63.0005 45.6514V63.0004H45.6641V45.6514H63.0005ZM49.9678 47.8656H47.8767V49.9582H49.9678V47.8656ZM59.6807 51.0645H57.5896V53.1571H59.6807V51.0645ZM53.1643 52.1725H51.0733V54.2651H53.1643V52.1725ZM57.5913 55.3714H55.5626L55.3786 55.5555V57.5856H57.5913V55.3714ZM52.1804 57.5873H49.9678V59.6799H52.1804V57.5873ZM60.7878 58.6936H58.6967V60.7862H60.7878V58.6936Z"
          fill="#133B70"
        />
        <path
          d="M10.2519 0C10.4013 0.129863 10.8662 0.499726 10.8662 0.677261V39.1299H6.5625V0.677261C6.5625 0.555617 7.08157 0.138082 7.17685 0H10.2502H10.2519Z"
          fill="#133B70"
        />
        <path
          d="M55.869 0C55.9643 0.138082 56.4834 0.555617 56.4834 0.677261V39.1299H52.1797V0.677261C52.1797 0.499726 52.6446 0.129863 52.794 0H55.8674H55.869Z"
          fill="#133B70"
        />
        <path
          d="M63.0005 41.2207H45.6641V43.435H63.0005V41.2207Z"
          fill="#133B70"
        />
        <path
          d="M63.0013 5.04441V7.99675C62.819 8.23675 62.3886 8.73483 62.0798 8.73483H58.6992V4.30469H62.203C62.4034 4.30469 62.8534 4.87181 63.0029 5.04277L63.0013 5.04441Z"
          fill="#133B70"
        />
        <path
          d="M17.3833 45.6514V63.0004H0.046875V45.6514H17.3833ZM4.35058 47.8656H2.25951V49.9582H4.35058V47.8656ZM14.1867 51.0645H11.9741V53.1571H14.1867V51.0645ZM7.67035 52.1725H5.45772V54.2651H7.67035V52.1725ZM11.9741 55.3714H9.88298V57.5856H11.9741V55.3714ZM6.56322 57.5873H4.35058V59.6799H6.56322V57.5873ZM15.1706 58.6936H13.0796V60.7862H15.1706V58.6936Z"
          fill="#133B70"
        />
        <path
          d="M49.9671 4.30664H13.0801V8.73678H49.9671V4.30664Z"
          fill="#133B70"
        />
        <path
          d="M32.6292 52.663L37.2401 48.1129L38.776 49.6498L31.5681 56.8383L24.2715 49.6498L25.8057 48.1129L30.4166 52.663V40.2981L30.6006 40.1123H32.4452L32.6292 40.2981V52.663Z"
          fill="#133B70"
        />
        <path
          d="M32.6283 19.0711V27.1916H30.4157V19.0711L25.8048 23.5045C25.6734 23.5045 24.4069 22.3111 24.3215 22.1451C24.2722 22.0497 24.2394 21.9938 24.3215 21.9018L31.5672 14.7725L38.7225 21.9018C38.8046 21.9938 38.7718 22.0514 38.7225 22.1451C38.6387 22.3078 37.3723 23.5045 37.2392 23.5045L32.6283 19.0711Z"
          fill="#133B70"
        />
        <path
          d="M17.3833 41.2207H0.046875V43.435H17.3833V41.2207Z"
          fill="#133B70"
        />
        <path
          d="M4.35028 4.30664V8.73678H0.968089C0.531147 8.73678 0.0942061 8.13021 0.0432844 7.69294C-0.0125653 7.2113 -0.0257064 5.56582 0.0728518 5.13185C0.128701 4.88198 0.636276 4.30664 0.846534 4.30664H4.35028Z"
          fill="#133B70"
        />
        <path
          d="M32.6286 38.0219H30.416V33.7759L30.6 33.5918H32.4447L32.6286 33.7759V38.0219Z"
          fill="#133B70"
        />
        <path
          d="M32.6286 29.2852H30.416V31.4994H32.6286V29.2852Z"
          fill="#133B70"
        />
      </svg>
    ),
    component: <Calc1 />,
  },
  {
    title: "Rendement du groupe motopompe",
    icon: (
      <svg
        width="93"
        height="52"
        viewBox="0 0 93 52"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M86.0242 15.8769C91.7181 16.5533 94.9068 22.5502 91.7659 27.4924C90.432 29.5923 88.4671 30.349 86.1772 31.0584C85.3071 35.7973 85.4792 42.2104 80.861 45.0717C78.1216 46.7695 75.191 46.5709 72.0835 46.5662C71.7154 48.666 72.0309 50.9787 69.4732 51.6929C68.0581 52.0901 62.9857 52.0428 61.3507 51.8962C58.0759 51.6077 58.0185 49.3518 57.7317 46.6749L51.727 46.5709C51.3063 48.7795 51.6123 51.2247 48.8012 51.7969C47.5104 52.0618 40.9082 52.0476 39.7608 51.6692C37.4708 50.922 37.6143 48.4958 37.3513 46.552C35.7737 46.4337 34.0622 46.6844 32.5036 46.5141C27.3309 45.9608 24.7588 38.9282 23.7501 34.6196C23.4728 33.442 22.139 29.0484 24.8496 30.2307C25.6002 30.5571 26.5851 35.4331 28.8416 33.2812C32.3028 29.9801 31.9013 18.2937 29.4248 14.4108C26.9484 10.528 26.1548 15.0635 25.1078 16.4303C24.3094 17.466 23.0043 16.8843 23.0377 15.6405C23.1764 10.9773 26.9819 0.321876 32.8861 0.284041C46.9033 0.983995 61.7905 -0.61455 75.7073 0.284041C84.7621 0.86576 84.8959 8.90105 86.0242 15.8769ZM26.3508 35.6459C27.546 40.6024 31.6766 48.5714 36.6151 41.3827C42.046 33.4799 41.8165 14.7372 37.2605 6.45121C32.5323 -2.14688 27.6751 5.23575 26.3508 11.1806C28.1866 10.6557 31.1172 10.5374 33.0104 10.8638C36.9641 11.5448 38.2501 18.9511 38.3983 22.1813C38.5848 26.2297 37.6047 35.5892 32.2789 36.0621C30.4957 36.2182 28.0432 36.1709 26.3508 35.6412V35.6459ZM73.1018 12.6231C72.6094 9.06185 72.2508 4.78645 68.4645 3.12642C68.2159 3.01765 66.8916 2.54943 66.7625 2.54943H37.1505C39.5791 5.39182 40.9321 9.0051 41.7209 12.6231H73.097H73.1018ZM71.854 44.2819H77.1511C78.2794 44.2819 80.6793 42.5982 81.3773 41.6759C83.7246 38.564 84.3892 27.2559 84.3318 23.2028C84.2744 19.1497 83.4569 10.055 81.9892 6.60728C81.2912 4.9709 78.7909 2.55416 76.9455 2.55416H71.6484C74.5217 5.3729 75.0332 9.7618 75.5687 13.5832C76.6157 21.0793 76.6204 29.701 75.1193 37.1262C74.5551 39.9213 73.9719 42.2056 71.8588 44.2819H71.854ZM33.5458 13.5217C32.8192 12.8643 32.0447 13.0298 31.1268 13.0393C34.5163 19.0835 34.5163 27.7573 31.1268 33.7967C32.6184 34.0096 33.3976 33.6454 34.2104 32.4299C36.7011 28.6984 37.0023 16.6384 33.5458 13.5217ZM73.5177 14.6804H42.1416C42.7918 18.0572 42.8922 21.5191 42.9735 24.9574H74.144L73.5225 14.6804H73.5177ZM74.1392 27.0147H42.7631C42.5241 30.5003 41.9361 33.9623 40.8939 37.2918H72.5807C73.7854 34.0758 73.6994 30.4199 74.1392 27.0147ZM72.2699 39.3491H40.062C39.4453 41.1794 38.3648 42.7968 37.1553 44.2819H66.9729C67.145 44.2819 68.541 43.7711 68.8182 43.6387C70.458 42.8725 71.9688 41.1179 72.2747 39.3491H72.2699ZM49.4132 46.5425H39.6461C39.6413 47.2661 39.6078 49.3471 40.4158 49.5835C42.854 49.4748 45.6842 49.8673 48.0745 49.6356C49.7669 49.47 49.1502 47.8857 49.4132 46.5425ZM69.7744 46.5425H60.0073C60.0456 47.2709 59.9882 49.3565 60.777 49.5835C63.2152 49.4748 66.0454 49.872 68.4358 49.6356C70.0947 49.4748 69.5879 47.8762 69.7744 46.5425Z"
          fill="#133B70"
        />
        <path
          d="M28.5316 25.9882C29.6216 26.2199 29.8176 27.8989 28.4885 28.2063L19.7063 28.2536C19.539 32.0986 16.9239 41.2264 11.9424 40.9852C6.77436 40.7298 4.60389 31.148 4.35051 27.1422C3.16011 26.9388 2.56729 27.1611 1.52509 26.4044C-1.64933 24.1106 0.573718 19.4616 4.30749 20.0102C4.55609 15.9855 6.71699 6.11516 11.9184 5.84085C17.1199 5.56655 19.6298 14.9403 19.7015 18.7995L28.4885 18.842C29.0383 18.9697 29.3921 19.3765 29.4543 19.9298L28.7419 21.0601H19.9119C20.1413 22.7296 20.0505 24.3187 19.9119 25.9929H28.5363L28.5316 25.9882ZM6.60703 27.0145C6.38711 30.5143 10.3217 43.9458 14.959 36.6105C18.774 30.5805 18.8075 15.924 14.7965 9.96964C12.2674 6.20975 9.88663 8.6927 8.50499 11.729C7.36717 14.2403 6.79348 17.2861 6.60703 20.0244H11.9041C12.0619 20.0244 12.3726 20.5257 12.4204 20.748C12.5304 21.3297 12.4874 26.2389 12.32 26.5983C12.2722 26.6976 11.9424 27.0145 11.9089 27.0145H6.61181H6.60703Z"
          fill="#133B70"
        />
        <path
          d="M78.4433 9.79023C81.5174 9.09974 81.6273 12.4576 78.6202 11.9941C77.1286 11.7624 77.0187 10.1071 78.4433 9.79023Z"
          fill="#133B70"
        />
        <path
          d="M78.4433 24.385C81.5174 23.6945 81.6273 27.0524 78.6202 26.5889C77.1286 26.3571 77.0187 24.7018 78.4433 24.385Z"
          fill="#133B70"
        />
        <path
          d="M78.4433 38.9797C81.5174 38.2892 81.6273 41.6471 78.6202 41.1836C77.1286 40.9519 77.0187 39.2966 78.4433 38.9797Z"
          fill="#133B70"
        />
      </svg>
    ),
    component: <Calc2 />,
  },
  {
    title: "Puissance hydraulique du GMP",
    icon: (
      <svg
        width="82"
        height="56"
        viewBox="0 0 82 56"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2205_869)">
          <path
            d="M0 18.6836C0.168698 18.4906 0.649167 17.8904 0.879792 17.8904H6.3913V11.3034C6.3913 9.908 8.77016 7.76395 10.2052 7.71093C10.3781 7.44584 10.1261 3.37827 10.2222 2.56816C10.3077 1.83862 10.6771 1.46326 11.4202 1.37843C13.1713 1.17484 17.3076 1.21301 19.1013 1.37418C19.7184 1.42932 20.2672 1.61595 20.4167 2.28822C20.6281 3.22346 20.248 7.36949 20.4722 7.71093C22.4347 8.0863 23.9338 9.58989 24.2861 11.541H29.3983V8.60375C29.3983 7.70669 31.3138 7.61762 31.9544 7.73001V3.36343C31.9544 2.26701 34.0748 0.233232 35.2685 0.146282C39.5629 0.322302 44.248 -0.216363 48.4996 0.101746C49.7509 0.195058 50.9553 0.835519 51.6792 1.86407C51.8138 2.05494 52.4095 3.23619 52.4095 3.36131V7.72577H74.8592C77.8466 7.72577 81.5857 11.5431 81.7694 14.5121C81.4085 22.2952 82.3908 30.7399 81.8164 38.4488C81.622 41.07 79.9585 43.6997 77.6053 44.9001C77.1355 45.1397 75.2927 45.8226 74.8592 45.8226H66.4713V48.3632H69.2665C70.3577 48.3632 72.0874 50.028 72.5059 51.0184C72.9501 52.0639 73.39 55.631 71.9486 55.9491L30.7521 55.9915C28.9477 56.1484 29.2616 53.0034 29.473 51.8497C29.7186 50.5115 31.5978 48.3653 32.9965 48.3653H35.7917V45.8247H30.2802C30.0923 45.8247 29.4004 45.1376 29.4004 44.951V42.0137H24.2882C23.9081 44.0348 22.3194 45.6508 20.2203 45.8289C17.9994 46.0177 12.1185 46.1068 10.0258 45.7844C8.81714 45.5978 7.47396 44.5607 6.89953 43.4919C6.81412 43.3328 6.3913 42.317 6.3913 42.2491V34.3918H0.879792C0.651302 34.3918 0.170833 33.7917 0 33.5987V18.6772V18.6836ZM49.8513 7.73001V3.84059C49.8513 3.58823 48.8797 2.61269 48.4846 2.65935C44.5277 2.91383 40.0818 2.28822 36.1825 2.64238C35.6273 2.69328 34.5126 3.29981 34.5126 3.84059V7.73001H49.8513ZM17.8948 3.91906H12.7826V7.72789H17.8948V3.91906ZM10.5041 10.3067C9.69266 10.453 8.98156 11.261 8.94953 12.0945L9.01787 41.8695C9.23995 42.5948 9.87417 43.1653 10.6408 43.2735C13.5108 43.6785 17.3374 43.0571 20.3035 43.2204C21.0403 43.0444 21.7108 42.2322 21.73 41.4624L21.6617 11.6873C21.4396 10.962 20.8054 10.3915 20.0387 10.2834C18.5376 10.0713 11.9327 10.048 10.5041 10.3067ZM69.0273 10.2685H31.9586V43.2883H35.7939C35.781 42.1686 35.5461 40.9131 36.9897 40.7455C46.5863 40.8325 56.2213 40.5886 65.7943 40.8685C66.736 41.3606 66.4392 42.4082 66.4734 43.2883H69.0295V10.2685H69.0273ZM71.5834 43.2883H74.2207C76.5098 43.2883 78.9869 40.7986 79.2133 38.5633C78.878 31.0814 79.8005 22.9929 79.2624 15.5788C79.0788 13.0637 76.9263 10.2685 74.2228 10.2685H71.5856V43.2883H71.5834ZM29.4004 14.0795H24.2882V39.4795H29.4004V14.0795ZM6.3913 20.4289H2.55609V31.8576H6.3913V20.4289ZM38.3478 44.5586C38.3179 45.8247 38.3692 47.1014 38.3478 48.3675H63.913C63.8917 47.1014 63.9408 45.8247 63.913 44.5586C63.9066 44.3105 63.992 43.3116 63.8212 43.2862H38.3478C38.3414 43.7103 38.3585 44.1366 38.3478 44.5586ZM70.3043 53.4487C70.4858 52.1869 69.7897 51.029 68.4657 50.9081L34.1069 50.9017C32.6356 50.8381 31.7536 52.0554 31.9565 53.4487H70.3043Z"
            fill="#133B70"
          />
          <path
            d="M37.9818 15.3839L63.9955 15.3457C65.6098 15.5048 65.6141 17.7336 63.9955 17.8927H38.2637C36.8052 17.7697 36.6023 15.842 37.9818 15.3839Z"
            fill="#133B70"
          />
          <path
            d="M37.9818 20.463L63.9955 20.4248C65.6098 20.5839 65.6141 22.8127 63.9955 22.9718H38.2637C36.8052 22.8488 36.6023 20.9211 37.9818 20.463Z"
            fill="#133B70"
          />
          <path
            d="M37.9818 25.5421L63.9955 25.5039C65.6098 25.663 65.6141 27.8918 63.9955 28.0509H38.2637C36.8052 27.9279 36.6023 26.0002 37.9818 25.5421Z"
            fill="#133B70"
          />
          <path
            d="M37.9818 30.6231L63.9955 30.585C65.6098 30.744 65.6141 32.9729 63.9955 33.132H38.2637C36.8052 33.009 36.6023 31.0812 37.9818 30.6231Z"
            fill="#133B70"
          />
          <path
            d="M37.9818 35.7022L63.9955 35.6641C65.6098 35.8231 65.6141 38.052 63.9955 38.2111H38.2637C36.8052 38.0881 36.6023 36.1603 37.9818 35.7022Z"
            fill="#133B70"
          />
        </g>
        <defs>
          <clipPath id="clip0_2205_869">
            <rect width="82" height="56" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    component: <Calc3 />,
  },
  {
    title: "Puissance électrique du GMP",
    icon: (
      <svg
        width="71"
        height="74"
        viewBox="0 0 71 74"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2205_1024)">
          <path
            d="M0.0461347 31.1063C1.29641 14.7454 15.4031 0.977607 31.5821 0.0573097C59.3272 -1.52089 77.288 29.2772 61.8583 52.7284L59.5394 55.8019H68.0485C69.5913 55.8019 70.9888 57.8528 70.9888 59.3403C70.9888 60.8278 69.5913 62.8787 68.0485 62.8787H59.396V65.1901H68.0485C68.3659 65.1901 69.404 65.7457 69.6964 65.9811C72.1396 67.9413 70.9276 71.8386 67.9052 72.2669H59.3941C59.4801 73.0213 59.2488 73.5499 58.6791 73.9994H46.3809C43.7045 73.1814 41.2727 71.171 40.9458 68.223C38.5906 68.1516 36.2162 68.3137 33.8609 68.2288C17.6322 67.6403 4.51004 57.4978 0.805094 41.3877L0.0461347 37.0274C0.155104 35.0981 -0.0991573 33.0144 0.0461347 31.1063ZM51.5311 54.0693C53.5748 54.0481 55.6509 54.1484 57.6774 53.9941C71.2278 38.5709 65.5919 14.4869 47.5566 5.58681C28.4354 -3.84961 5.56723 8.43838 2.64227 29.611C0.432305 45.6091 10.5473 60.9571 26.0458 64.8582C30.9455 66.0911 35.7975 65.738 40.8063 65.7669V62.3018C34.0387 62.4813 27.7415 62.4716 21.5474 59.4387C2.27522 50.0023 0.403629 22.7097 18.221 10.6841C37.801 -2.53187 63.9842 13.3756 61.5391 36.9541C61.2083 40.1356 59.3731 46.4233 57.316 48.8639C56.928 49.3231 56.1843 49.4968 55.6777 49.1591C54.4924 48.3681 55.9128 46.8015 56.3238 45.9854C67.2513 24.2532 46.01 1.09144 23.9333 10.2423C8.90704 16.4702 3.43756 35.4106 12.8012 48.916C17.7813 56.099 25.434 59.6586 34.0062 59.9963C36.2334 60.0831 38.4854 59.9345 40.7145 59.9712C40.9955 59.8747 41.072 58.9255 41.2058 58.5898C43.1902 53.6429 47.1093 54.1137 51.5292 54.0674L51.5311 54.0693ZM57.1077 56.3787H47.0252C45.6258 56.3787 43.7255 58.2656 43.3107 59.5602C42.8977 60.849 42.9723 65.7418 43.087 67.2892C43.2246 69.1568 45.0465 71.6881 47.0252 71.6881H57.1077V56.3787ZM59.396 60.5673H67.7618C68.1059 60.5673 68.6985 59.7493 68.6985 59.3403C68.6985 58.9313 68.104 58.1132 67.7618 58.1132H59.396V60.5693V60.5673ZM59.396 69.9555H67.7618C68.1059 69.9555 68.6985 69.1375 68.6985 68.7285C68.6985 68.3195 68.104 67.5014 67.7618 67.5014H59.396V69.9575V69.9555Z"
            fill="#193C6D"
          />
          <path
            d="M27.9338 24.7512L35.2902 14.489C35.6286 13.8196 37.2287 14.3868 37.2287 14.8575V23.0167C39.4463 20.3407 41.2605 17.331 43.3711 14.5604C43.9905 13.8466 44.8412 14.0395 45.2714 14.8209C45.539 19.8005 45.279 24.8477 45.4033 29.8505L54.094 35.3703C54.5911 35.7253 54.8205 36.2829 54.6102 36.8849C50.5726 42.5629 46.6841 48.4069 42.4305 53.8998C41.9813 54.3802 40.8036 53.7608 40.8036 53.2689V45.1097C38.586 47.8088 36.7775 50.8881 34.5867 53.6335C34.1756 54.4207 32.652 53.9461 32.652 53.2689V43.5199C32.4474 43.5855 32.2945 43.8016 32.1626 43.9656C29.7825 46.9136 27.8153 50.5041 25.4314 53.4869C24.724 54.4226 23.4929 54.1892 23.3514 52.9814L23.2845 38.3184L14.437 32.6654C13.5461 31.7972 14.2974 30.9637 14.8002 30.1862C18.205 24.9036 22.3496 19.8719 25.8539 14.6318C26.2764 13.6903 27.93 14.2672 27.93 14.9965V24.7454L27.9338 24.7512ZM25.6455 30.8904V18.9767L16.9165 31.3284C16.8993 31.5252 17.0312 31.5715 17.1555 31.666C19.4591 33.4005 22.4739 34.7491 24.833 36.4758C25.0356 36.5955 25.6436 37.2148 25.6436 37.3904V49.1613L34.3611 36.8289C34.1871 36.555 33.8889 36.4276 33.6404 36.2482C31.4228 34.6565 28.2493 33.2635 26.2247 31.6062C26.0527 31.4653 25.6856 31.0872 25.6436 30.8923L25.6455 30.8904ZM34.9403 30.8904V18.9767L28.1556 28.5173C27.886 28.8858 27.7866 29.6711 28.0772 30.0241C30.616 31.7316 33.4224 33.2095 35.9287 34.9478C36.4774 35.3279 37.2058 35.6057 37.2383 36.3832C37.2765 37.3305 34.9403 39.7615 34.9403 39.9911V49.1613L43.6521 36.8308C43.6196 36.6418 43.1149 36.3755 42.9352 36.2482C40.7176 34.6603 37.5422 33.2635 35.5196 31.6062C35.3475 31.4653 34.9805 31.0872 34.9384 30.8923L34.9403 30.8904ZM43.092 30.8904V18.9767L37.3052 27.0703C37.156 27.4427 37.1484 29.7772 37.3702 30.028L45.9443 35.3781C46.5102 35.8006 46.6688 36.3234 46.4127 36.991C46.0858 37.8457 43.092 41.3494 43.092 41.7237V49.1613L51.8038 36.8308C51.7713 36.6418 51.2666 36.3755 51.0869 36.2482C48.8693 34.6603 45.6939 33.2635 43.6712 31.6062C43.4992 31.4653 43.1321 31.0872 43.0901 30.8923L43.092 30.8904Z"
            fill="#193C6D"
          />
          <path
            d="M54.8197 50.3156C55.9725 51.2783 54.5119 53.1151 53.2464 52.1928C52.2981 51.5021 53.4566 49.1773 54.8197 50.3156Z"
            fill="#193C6D"
          />
          <path
            d="M51.205 60.1623C52.0003 59.9867 52.5834 60.4112 52.6771 61.2138C52.809 62.354 52.8338 66.1838 52.6656 67.2739C52.4687 68.5511 50.5512 68.609 50.383 67.1407C50.2511 65.9928 50.2243 62.1785 50.3945 61.0807C50.4614 60.6543 50.7768 60.2568 51.205 60.1623Z"
            fill="#193C6D"
          />
        </g>
        <defs>
          <clipPath id="clip0_2205_1024">
            <rect width="71" height="74" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    component: <Calc4 />,
  },
  {
    title: "Diamètre de conduite hydraulique",
    icon: (
      <svg
        width="74"
        height="54"
        viewBox="0 0 74 54"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2205_1002)">
          <path
            d="M54.7539 4.83266L54.7588 4.82515C54.7613 4.79261 54.7613 4.76008 54.7638 4.72754C54.7613 4.76258 54.7588 4.79762 54.7539 4.83266Z"
            fill="#193C6D"
          />
          <path
            d="M37.0733 23.9473C36.9769 23.9473 36.8805 23.9473 36.7817 23.9523C36.6582 23.9573 36.5346 23.9648 36.4062 23.9773C36.2876 23.9873 36.1715 23.9998 36.0553 24.0148C35.9417 24.0299 35.828 24.0449 35.7144 24.0649C32.3641 24.6831 29.8242 27.6566 29.8242 31.2282C29.8242 35.2503 33.0435 38.5116 37.014 38.5116C40.9844 38.5116 44.2037 35.2503 44.2037 31.2282C44.2037 27.206 41.0165 23.9773 37.0733 23.9473ZM37.014 36.1189C34.3481 36.1189 32.1862 33.9288 32.1862 31.2282C32.1862 28.5276 34.3481 26.34 37.014 26.34C39.6798 26.34 41.8392 28.5276 41.8392 31.2282C41.8392 33.9288 39.6798 36.1189 37.014 36.1189Z"
            fill="#193C6D"
          />
          <path
            d="M36.4055 23.9782C36.2869 23.9882 36.1708 24.0007 36.0547 24.0157C36.2943 23.9832 36.534 23.9581 36.7811 23.9531C36.6575 23.9581 36.534 23.9656 36.4055 23.9782Z"
            fill="#193C6D"
          />
          <path
            d="M35.7148 24.0657C35.8285 24.0457 35.9421 24.0306 36.0558 24.0156C35.9421 24.0306 35.826 24.0457 35.7148 24.0657Z"
            fill="#193C6D"
          />
          <path
            d="M37.0728 23.9478C36.9764 23.9478 36.8801 23.9478 36.7812 23.9528C36.8578 23.9478 36.9369 23.9453 37.0135 23.9453C37.0333 23.9453 37.053 23.9453 37.0728 23.9453V23.9478Z"
            fill="#193C6D"
          />
          <path
            d="M37.0132 22.0509C37.6968 22.0509 38.251 21.4895 38.251 20.7969C38.251 20.1044 37.6968 19.543 37.0132 19.543C36.3296 19.543 35.7754 20.1044 35.7754 20.7969C35.7754 21.4895 36.3296 22.0509 37.0132 22.0509Z"
            fill="#193C6D"
          />
          <path
            d="M37.0132 42.9171C37.6968 42.9171 38.251 42.3557 38.251 41.6631C38.251 40.9706 37.6968 40.4092 37.0132 40.4092C36.3296 40.4092 35.7754 40.9706 35.7754 41.6631C35.7754 42.3557 36.3296 42.9171 37.0132 42.9171Z"
            fill="#193C6D"
          />
          <path
            d="M29.732 25.1065C30.4156 25.1065 30.9698 24.5451 30.9698 23.8526C30.9698 23.16 30.4156 22.5986 29.732 22.5986C29.0483 22.5986 28.4941 23.16 28.4941 23.8526C28.4941 24.5451 29.0483 25.1065 29.732 25.1065Z"
            fill="#193C6D"
          />
          <path
            d="M44.2964 39.8614C44.98 39.8614 45.5342 39.3 45.5342 38.6075C45.5342 37.9149 44.98 37.3535 44.2964 37.3535C43.6128 37.3535 43.0586 37.9149 43.0586 38.6075C43.0586 39.3 43.6128 39.8614 44.2964 39.8614Z"
            fill="#193C6D"
          />
          <path
            d="M26.7163 32.4825C27.4 32.4825 27.9542 31.9211 27.9542 31.2286C27.9542 30.536 27.4 29.9746 26.7163 29.9746C26.0327 29.9746 25.4785 30.536 25.4785 31.2286C25.4785 31.9211 26.0327 32.4825 26.7163 32.4825Z"
            fill="#193C6D"
          />
          <path
            d="M47.314 32.4825C47.9976 32.4825 48.5518 31.9211 48.5518 31.2286C48.5518 30.536 47.9976 29.9746 47.314 29.9746C46.6304 29.9746 46.0762 30.536 46.0762 31.2286C46.0762 31.9211 46.6304 32.4825 47.314 32.4825Z"
            fill="#193C6D"
          />
          <path
            d="M29.732 39.8614C30.4156 39.8614 30.9698 39.3 30.9698 38.6075C30.9698 37.9149 30.4156 37.3535 29.732 37.3535C29.0483 37.3535 28.4941 37.9149 28.4941 38.6075C28.4941 39.3 29.0483 39.8614 29.732 39.8614Z"
            fill="#193C6D"
          />
          <path
            d="M44.2964 25.1065C44.98 25.1065 45.5342 24.5451 45.5342 23.8526C45.5342 23.16 44.98 22.5986 44.2964 22.5986C43.6128 22.5986 43.0586 23.16 43.0586 23.8526C43.0586 24.5451 43.6128 25.1065 44.2964 25.1065Z"
            fill="#193C6D"
          />
          <path
            d="M52.3906 8.43246C52.6451 8.2998 52.8848 8.14212 53.1047 7.96191C52.8971 8.11709 52.6451 8.28228 52.3906 8.43246Z"
            fill="#193C6D"
          />
          <path
            d="M54.7525 4.83266C54.7525 4.85018 54.7525 4.8677 54.75 4.88522C54.7525 4.8652 54.7549 4.84518 54.7574 4.82515C54.7599 4.79261 54.7599 4.76008 54.7624 4.72754C54.7599 4.76258 54.7574 4.79762 54.7525 4.83266Z"
            fill="#193C6D"
          />
          <path
            d="M72.9289 8.97539C72.0345 8.77265 68.2791 8.83773 67.2834 8.9829C65.8034 9.19815 63.6292 11.0203 63.6292 12.5871V19.425H48.1873C48.0415 19.425 46.3343 17.7305 45.8723 17.6079C45.64 16.1562 46.3121 12.0414 45.5931 10.9977C45.4967 10.8576 44.9606 10.4321 44.8593 10.4321H41.4374V8.93534H50.3591C51.0904 8.93534 51.7797 8.75513 52.3875 8.43476H52.39C52.6445 8.28209 52.8965 8.11689 53.104 7.96171C53.1263 7.9467 53.146 7.93168 53.1633 7.91416C53.272 7.82406 53.3783 7.72644 53.4771 7.62633C54.0824 7.01312 54.5123 6.2247 54.6853 5.34118C54.6976 5.27861 54.7075 5.21353 54.7174 5.14846C54.7297 5.07838 54.7372 5.0083 54.7446 4.93571C54.747 4.91819 54.747 4.90317 54.7495 4.88565C54.752 4.86813 54.752 4.85061 54.752 4.83309C54.7569 4.79805 54.7594 4.76301 54.7619 4.72797C54.7619 4.71796 54.7619 4.70795 54.7619 4.69794C54.7643 4.63787 54.7668 4.5753 54.7668 4.51523V4.46767C54.7668 4.38758 54.7643 4.30498 54.7619 4.22489C54.7569 4.1448 54.7495 4.0647 54.7421 3.98461C54.7372 3.93205 54.7297 3.87949 54.7223 3.82693C54.71 3.75184 54.6976 3.67676 54.6828 3.60167C54.668 3.51657 54.6482 3.43397 54.6284 3.35138C54.6087 3.27879 54.5889 3.20871 54.5691 3.13863C54.0108 1.31903 52.3356 0 50.3566 0H49.272C46.5764 0.105122 43.8216 0.0750869 41.0495 0H23.5025C23.2258 0 22.9565 0.025029 22.6946 0.0750869C19.752 0.813441 18.1806 4.01214 19.9052 6.85543C20.2313 7.39356 21.0639 8.17196 21.8472 8.60997C21.8472 8.60997 21.8488 8.6108 21.8521 8.61247C22.3611 8.82021 22.9194 8.93534 23.5025 8.93534H32.5577V10.4321H29.1357C29.032 10.4321 28.4958 10.8576 28.3995 10.9977C27.683 12.0414 28.355 16.1562 28.1227 17.6079C27.6607 17.7305 25.9535 19.425 25.8077 19.425H10.3633V12.5871C10.3633 11.0203 8.18912 9.19815 6.70917 8.9829C5.71595 8.83773 1.96048 8.77265 1.06362 8.97539C0.384172 9.12807 -0.043259 9.82637 0.00121362 10.5172L0.048157 52.8286C0.208753 53.3993 0.638655 53.7948 1.22174 53.8874C2.63251 54.1101 7.0353 54.045 8.20889 53.3067C8.96492 52.8286 10.3658 51.1567 10.3658 50.2457V43.4077H25.8102C25.9065 43.4077 27.0406 44.494 27.3198 44.6867C32.7751 48.4361 39.2829 49.1769 45.1212 45.7329C45.7389 45.37 47.9946 43.4077 48.1873 43.4077H63.6292V50.2457C63.6292 51.2869 65.1092 52.9913 66.0233 53.4419C67.192 54.02 71.4366 54.1001 72.7733 53.8874C73.3588 53.7948 73.7863 53.3993 73.9469 52.8286L73.9963 10.5172C74.0383 9.82637 73.6133 9.12807 72.9289 8.97539ZM6.39045 50.9014H2.96853V11.9313H6.39045C6.74129 11.9313 7.52203 12.8724 7.41085 13.3329L7.41579 49.3171C7.5838 49.8277 6.81294 50.9014 6.39045 50.9014ZM23.5865 5.94188C21.8966 5.79671 21.6643 3.51907 23.2604 2.97845L50.4109 2.9359C52.2813 3.12111 52.2837 5.75166 50.4109 5.94188H23.5865ZM38.4775 8.93534V10.4321H35.5176V8.93534H38.4775ZM31.0802 13.4305H42.9173V16.0536C39.0383 14.6044 34.9567 14.6044 31.0802 16.0536V13.4305ZM22.1165 37.9664C22.4624 38.8074 22.9837 39.5658 23.3123 40.4093H10.3658V22.4235H23.3123C23.0109 23.2244 22.5093 23.9427 22.1708 24.7336C20.303 29.0937 20.3079 33.5914 22.1165 37.9664ZM47.6166 39.6434C41.1409 48.4511 27.2605 45.788 24.1894 35.3059C21.9262 27.5794 27.0851 18.8969 35.1074 17.9783C47.0459 16.6142 54.8385 29.8195 47.6166 39.6434ZM63.6292 40.4093H50.6827C51.2584 39.1503 51.9749 37.9539 52.4023 36.6224C53.6723 32.6528 53.4647 28.7032 51.881 24.8663C51.5327 24.0253 51.0113 23.2669 50.6827 22.4235H63.6292V40.4093ZM71.0265 50.9014H67.607C67.1821 50.9014 66.4137 49.8277 66.5792 49.3171L66.5842 13.3329C66.4755 12.8724 67.2562 11.9313 67.607 11.9313H71.0265V50.9014Z"
            fill="#193C6D"
          />
        </g>
        <defs>
          <clipPath id="clip0_2205_1002">
            <rect width="74" height="54" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    component: <Calc5 />,
  },
  {
    title: "Pertes de charge hydraulique",
    icon: (
      <svg
        width="102"
        height="49"
        viewBox="0 0 102 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2208_1203)">
          <path
            d="M89.3276 42.6222C88.8777 45.9406 85.6358 48.7509 82.3234 48.9914L7.44844 49.0023C2.89463 48.4699 0.54996 45.4515 0.243662 41.0144C0.861679 30.2081 -0.553256 18.5397 0.254504 7.8361C0.533696 4.15022 2.26577 1.43445 5.89256 0.30491C31.1879 -0.308502 56.5944 0.226544 81.9304 0.0346844C86.9288 0.383275 89.8156 4.26101 89.3249 9.14669C94.1146 9.70335 101.184 6.92273 101.71 13.8918C102.081 18.8154 102.108 27.4031 101.699 32.3023C101.634 33.0941 101.412 34.3695 100.983 35.0262C100.644 35.545 99.1509 36.8556 98.5572 36.8556H89.3249C89.1515 38.658 89.5608 40.8712 89.3249 42.6195L89.3276 42.6222ZM7.3102 2.12353C4.76224 2.46942 2.63712 4.37991 2.4772 7.01461L2.48804 42.2142C2.77536 45.0597 5.29352 46.9053 8.05562 46.9783H81.7244C85.0205 46.7188 87.0453 44.6922 87.3028 41.4143C88.1621 30.4945 86.6469 18.4641 87.292 7.42805C86.9992 4.2421 84.7467 2.13434 81.5211 2.05597L7.30749 2.12353H7.3102ZM89.3276 35.037H97.5462C98.9042 35.037 99.5846 33.4778 99.6849 32.3131C100.192 26.3682 99.2945 19.7206 99.6767 13.7C99.7607 12.8407 98.8473 11.168 97.9555 11.168H89.3331V35.037H89.3276Z"
            fill="#133B70"
          />
          <path
            d="M30.839 4.94498C32.7879 4.61531 40.7192 4.63963 42.744 4.91526C44.205 5.11252 45.395 6.1637 45.7663 7.56347C46.0889 18.2077 45.8151 28.9194 45.9073 39.5879C45.5847 41.7416 44.2728 42.7495 42.1612 42.9333C39.5238 43.1603 33.7963 43.2089 31.2212 42.9036C29.7765 42.7333 28.6678 41.8092 28.1935 40.4607C27.8682 29.6112 28.1501 18.6995 28.0498 7.82559C28.2802 6.44473 29.4322 5.18548 30.8417 4.94498H30.839ZM31.2402 6.96357C30.6764 7.16353 30.08 7.57428 30.0719 8.22552L30.1234 39.95C30.4378 40.5391 30.9474 40.8309 31.598 40.9066C33.9264 41.1822 39.5726 41.1282 41.9579 40.9093C43.4243 40.7742 43.7496 40.4553 43.8851 38.988L43.7876 7.92016C43.5165 7.26622 43.053 6.99599 42.3618 6.91493C38.9491 6.51769 34.7396 7.18515 31.2402 6.96086V6.96357Z"
            fill="#133B70"
          />
          <path
            d="M8.92669 4.94501C10.8756 4.61264 18.8068 4.63966 20.8317 4.91529C23.0137 5.21254 23.8431 6.83119 24.0004 8.83356C24.7647 18.5238 23.4094 29.1924 24.0004 38.988C23.8079 41.4336 22.7535 42.7171 20.2462 42.9333C17.7389 43.1495 11.7457 43.2252 9.30075 42.909C7.88853 42.7252 6.78802 41.7795 6.23506 40.504L6.13477 7.82562C6.36517 6.44477 7.51717 5.18552 8.92669 4.94501ZM9.12456 6.9636C8.40354 7.12844 8.13519 7.73644 8.15959 8.42822L8.21109 39.95C8.52552 40.5391 9.03511 40.831 9.68566 40.9066C12.0141 41.1796 17.663 41.1309 20.0456 40.9093C21.512 40.7742 21.8373 40.4554 21.9728 38.988C22.8592 29.4329 21.287 18.532 21.9728 8.83356C21.8698 7.73644 21.6665 7.05548 20.4468 6.91496C16.969 6.51503 12.6863 7.18789 9.12456 6.9636Z"
            fill="#133B70"
          />
          <path
            d="M69.2388 26.3386V30.6866C69.2388 31.0433 67.7317 32.0242 67.2031 31.5026C62.755 29.1463 57.2959 27.6708 52.9291 25.3037C52.1186 24.8632 52.0671 24.7443 51.9831 23.8201C51.8611 22.4906 51.7744 17.9022 51.9967 16.7348C52.1485 15.9458 53.2408 15.308 54.0242 15.7161L64.7744 20.472V15.3134C64.7744 14.4379 66.8968 13.8245 67.5176 14.8054C71.3965 19.7749 76.1861 24.2687 80.0975 29.1679C81.0625 30.3758 82.1711 32.0269 79.5825 31.7026L69.2415 26.3386H69.2388ZM66.8047 17.2374V21.788C66.8047 22.1772 65.2569 23.0905 64.7202 22.6528C61.1477 21.153 57.6456 19.3398 54.0242 18.0454V23.7093L67.2113 29.1706V25.428C67.2113 24.6686 68.1058 23.839 68.9135 23.9093C69.9951 24.0039 73.6246 26.417 74.9772 26.9899C75.1588 27.0655 75.5627 27.3493 75.5274 26.9466L66.8047 17.2347V17.2374Z"
            fill="#133B70"
          />
          <path
            d="M31.2405 6.96345C34.7399 7.18504 38.9495 6.52028 42.3621 6.91752C43.0533 6.99858 43.5169 7.26881 43.7879 7.92275L43.8855 38.9906C43.75 40.4579 43.4247 40.7768 41.9583 40.9119C39.5729 41.1308 33.9267 41.1821 31.5983 40.9092C30.9451 40.8335 30.4382 40.5417 30.1238 39.9526L30.0723 8.22811C30.0804 7.57687 30.6794 7.16612 31.2405 6.96616V6.96345Z"
            fill="#133B70"
          />
          <path
            d="M9.1244 6.96361C12.6888 7.18789 16.9689 6.51503 20.4466 6.91497C21.6664 7.05548 21.8697 7.73645 21.9727 8.83357C21.2869 18.532 22.859 29.4329 21.9727 38.988C21.8371 40.4554 21.5119 40.7742 20.0454 40.9093C17.6628 41.1282 12.0112 41.1796 9.68549 40.9066C9.03224 40.831 8.52535 40.5391 8.21092 39.95L8.15942 8.42823C8.13774 7.73645 8.40338 7.12844 9.1244 6.96361Z"
            fill="#133B70"
          />
        </g>
        <defs>
          <clipPath id="clip0_2208_1203">
            <rect width="102" height="49" fill="white" />
          </clipPath>
        </defs>
      </svg>
    ),
    component: <Calc6 />,
  },
];
