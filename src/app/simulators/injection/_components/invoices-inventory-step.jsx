"use client";

import InvoicesInventoryIllustration from "../illustrations/invoices-inventory-illustration";
import StepQuestion from "./step-question";

export default function InvoicesInventoryStep({ state, setState }) {
  function handleMonthlyConsumptionChange(monthlyConsumption, key) {
    if (state.type === 1) {
      const monthlyConsumptions = {};
      Array.from({ length: 12 }, (_, key) => {
        monthlyConsumptions[key] = monthlyConsumption;
      });
      setState((state) => ({
        ...state,
        consumption: {
          ...state.consumption,
          monthlyConsumptions,
        },
      }));
    } else {
      setState((state) => ({
        ...state,
        consumption: {
          ...state.consumption,
          monthlyConsumptions: {
            ...state.consumption.monthlyConsumptions,
            [key]: monthlyConsumption,
          },
        },
      }));
    }
  }

  function handleInputKeyDown(e, key) {
    if (e.key !== "Enter" || isNaN(e.target.value || "none")) return;

    document.getElementById(`in-${key + 1}`)?.focus();
  }

  return (
    <div className="flex">
      <aside className="flex items-center justify-center w-full md:w-1/2 md:pr-4">
        <div className="flex flex-col gap-4">
          <StepQuestion
            number={4}
            question={
              state.type === 1
                ? "Combien dépensez-vous en moyenne pour votre consommation électrique ?"
                : "Quel est votre consommation mensuelle en Dirham ?"
            }
          />
          <div>
            <div className="relative rounded-3xl border-8 border-t-[30px] p-2 md:p-4 border-brand-primary">
              <div className="absolute h-10 w-4 bottom-full mb-2 left-1/4 bg-brand-primary border-2 border-white rounded-full" />
              <div className="absolute h-10 w-4 bottom-full mb-2 right-1/4 bg-brand-primary border-2 border-white rounded-full" />
              {state.type === 1 ? (
                <div className="flex justify-center py-6">
                  <input
                    type="number"
                    className="min-w-0 px-1 w-fit text-center py-0.5 text-xs rounded outline-none ring-2 ring-brand-primary"
                    placeholder="MAD"
                    value={
                      state.consumption.monthlyConsumptions?.[0] || undefined
                    }
                    onChange={(e) =>
                      handleMonthlyConsumptionChange(+e.target.value, 0)
                    }
                    onKeyDown={(e) => handleInputKeyDown(e, 0)}
                  />
                </div>
              ) : (
                <div className=" grid grid-cols-3 gap-2">
                  {Array.from({ length: 12 }, (_, key) => (
                    <div key={key} className="rounded overflow-hidden">
                      <div className="p-2 bg-secondary w-full">
                        {/* {state.type === 1 ? (
                        <input
                          id={`in-${key}`}
                          type="number"
                          className="min-w-0 px-1 w-full text-center py-0.5 text-xs rounded focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand-primary"
                          placeholder="MAD"
                          value={
                            state.consumption.monthlyConsumptions?.[key] ||
                            undefined
                          }
                          onChange={(e) =>
                            handleMonthlyConsumptionChange(+e.target.value, key)
                          }
                          onKeyDown={(e) => handleInputKeyDown(e, key)}
                        />
                      ) : ( */}
                        <div className="flex items-stretch gap-1">
                          <input
                            id={`in-${key * 3 + 1}`}
                            type="number"
                            className="min-w-0 px-1 w-full text-center py-0.5 text-xs rounded-l focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand-primary"
                            placeholder="HP"
                            value={
                              state.consumption.monthlyConsumptions?.[
                                key
                              ]?.[0] || undefined
                            }
                            onChange={(e) =>
                              handleMonthlyConsumptionChange(
                                [
                                  +e.target.value,
                                  state.consumption.monthlyConsumptions?.[
                                    key
                                  ]?.[1],
                                  state.consumption.monthlyConsumptions?.[
                                    key
                                  ]?.[2],
                                ],
                                key
                              )
                            }
                            onKeyDown={(e) =>
                              handleInputKeyDown(e, key * 3 + 1)
                            }
                          />
                          <input
                            id={`in-${key * 3 + 2}`}
                            type="number"
                            className="min-w-0 px-1 w-full text-center py-0.5 text-xs focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand-primary"
                            placeholder="HPL"
                            value={
                              state.consumption.monthlyConsumptions?.[
                                key
                              ]?.[1] || undefined
                            }
                            onChange={(e) =>
                              handleMonthlyConsumptionChange(
                                [
                                  state.consumption.monthlyConsumptions?.[
                                    key
                                  ]?.[0],
                                  +e.target.value,
                                  state.consumption.monthlyConsumptions?.[
                                    key
                                  ]?.[2],
                                ],
                                key
                              )
                            }
                            onKeyDown={(e) =>
                              handleInputKeyDown(e, key * 3 + 2)
                            }
                          />
                          <input
                            id={`in-${key * 3 + 3}`}
                            type="number"
                            className="min-w-0 w-full text-center py-0.5 text-xs rounded-r focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand-primary"
                            placeholder="HC"
                            value={
                              state.consumption.monthlyConsumptions?.[
                                key
                              ]?.[2] || undefined
                            }
                            onChange={(e) =>
                              handleMonthlyConsumptionChange(
                                [
                                  state.consumption.monthlyConsumptions?.[
                                    key
                                  ]?.[0],
                                  state.consumption.monthlyConsumptions?.[
                                    key
                                  ]?.[1],
                                  +e.target.value,
                                ],
                                key
                              )
                            }
                            onKeyDown={(e) =>
                              handleInputKeyDown(e, key * 3 + 3)
                            }
                          />
                        </div>
                        {/* )} */}
                      </div>
                      <div className="p-1 bg-brand-primary text-center">
                        <span className="text-xs text-white">
                          Mois {key + 1}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </aside>
      <aside className="hidden md:flex items-center justify-center w-full md:w-1/2">
        {/* <img src="/assets/images/simulators/injection/invoices-inventory.svg" /> */}
        <InvoicesInventoryIllustration />
      </aside>
    </div>
  );
}
