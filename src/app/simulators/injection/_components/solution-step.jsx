"use client";

import ProductPrice from "@/components/product-price";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useSettingsContext } from "@/providers/settings-provider";
import {
  useGetInjectionSimulatorSolution,
  useSendInjectionFormMutation,
} from "@/services/common";
import Link from "next/link";
import { useEffect, useState } from "react";
import React<PERSON>on<PERSON>tti from "react-confetti";
import { useForm } from "react-hook-form";
import { TailSpin } from "react-loader-spinner";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import CongratulationsIllustration from "../illustrations/congratulations-illustration";
import FormIllustration from "../illustrations/form-illustration";
import NoSolutionIllustration from "../illustrations/no-solution-Illustration";

export default function SolutionStep({ state, setState }) {
  const { data, isLoading } = useGetInjectionSimulatorSolution(state);
  const [showSuccess, setShowSuccess] = useState(true);
  const [showForm, setShowForm] = useState(true);

  const solutions = data?.solutions;

  useEffect(() => {
    document.documentElement.style.scrollBehavior = "auto";
    document.getElementById("step-4")?.scrollIntoView({
      block: "nearest",
    });
  }, []);

  if (isLoading) {
    return <SolutionLoading />;
  }

  if (!solutions?.length) {
    return <SolutionNotFound details={data?.details} />;
  }

  if (showSuccess) {
    return (
      <SolutionSuccess
        onShowSolution={() => {
          setShowSuccess(false);
          setShowForm(true);
        }}
      />
    );
  }

  if (showForm) {
    return (
      <SolutionForm
        stats={data?.stats}
        onSubmitSuccess={() => setShowForm(false)}
      />
    );
  }

  return <Solution solutions={solutions} solutionsStats={data?.stats} />;
}

function SolutionLoading() {
  return (
    <div className="flex gap-2 items-center justify-center backdrop-blur-2xl min-h-[300px] lg:min-h-[500px]">
      <TailSpin color="#2A3466" height={20} width={20} />
      <span>CHARGEMENT DE VOTRE SOLUTION...</span>
    </div>
  );
}

function SolutionNotFound({ details }) {
  const settings = useSettingsContext();

  return (
    <div className="lg:pl-10 backdrop-blur-2xl flex items-center">
      <div className="w-full lg:w-1/2 space-y-4">
        <h2 className="text-4xl text-brand-ternary !font-black">Oups !</h2>
        <div className="space-y-6">
          <h6 className="font-bold text-md text-brand-ternary">
            Malheureusement, nous n'avons pas de kit solaire adapté à votre
            besoin pour le moment.
          </h6>
          <div className="space-y-2">
            <p>
              Mais ne vous inquiétez pas, équipe ECOWATT est là pour vous aider
              ! Contactez-nous pour discuter de vos besoins spécifiques, et nous
              trouverons la meilleure solution pour vous !{" "}
            </p>
          </div>
          <Button variant="brand-ternary" className="animate-pulse" asChild>
            <a
              href={`${settings?.sm_whatsapp}&text=${encodeURIComponent(`Bonjour ☀️
Je souhaite faire une étude pour passer à l'énergie solaire.
Ma puissance crête installée est ${details?.puissance} kWc pour un ${details?.type == 1 ? "Résidentiel" : "Industriel"}.
Je serais ravi(e) d'avoir votre retour pour une solution sur mesure. Merci beaucoup 🙏`)}`}
              target="_blank"
              rel="noreferrer"
            >
              Cliquez ici pour nous contacter !
            </a>
          </Button>
        </div>
      </div>
      <div className="hidden md:flex items-center">
        {/* <img src="/assets/images/simulators/injection/not-found.svg" /> */}
        <NoSolutionIllustration />
      </div>
    </div>
  );
}

function SolutionSuccess({ onShowSolution }) {
  return (
    <div className="lg:pl-10 -mt-6 pt-6 backdrop-blur-2xl flex items-center">
      <div className="w-full lg:w-1/2 space-y-4">
        <h2 className="text-5xl text-brand-ternary !font-black">
          Félicitations!
        </h2>
        <div className="space-y-6">
          <h6 className="font-bold text-lg text-brand-ternary">
            Votre kit solaire sur-mesure est prêt !
          </h6>
          <div className="space-y-2">
            <span className="text-lg">Votre solution inclut :</span>
            <ul className="space-y-2 [&>li]:flex [&>li]:text-lg [&>li]:items-baseline [&>li]:gap-2 [&_svg]:flex-shrink-0">
              <li>
                <span>☀️</span> Un équipement solaire fiable et durable.
              </li>
              <li>
                <span>⚡</span> Une installation optimisée pour maximiser votre
                production énergétique.
              </li>
              <li>
                <span>🤝</span> Un accompagnement complet à chaque étape.
              </li>
            </ul>
          </div>
          <Button
            variant="brand-ternary"
            className="animate-pulse"
            onClick={onShowSolution}
          >
            Découvrez votre solution
          </Button>
        </div>
      </div>
      <div className="hidden md:flex items-center">
        {/* <img src="/assets/images/simulators/injection/congratulations.svg" /> */}
        <CongratulationsIllustration />
      </div>
      <ReactConfetti />
    </div>
  );
}

function SolutionForm({ onSubmitSuccess, stats }) {
  const form = useForm({
    defaultValues: {
      puissance: stats?.map((s) => s.systeme_max_capacity)?.join("-"),
    },
  });

  const { sendInjectionForm, isLoading, isSuccess } =
    useSendInjectionFormMutation();

  function onSubmit(values) {
    sendInjectionForm(values, {
      onSuccess() {
        onSubmitSuccess();
      },
    });
  }

  return (
    <div className="lg:pl-10 backdrop-blur-2xl flex items-center">
      <div className="hidden md:flex items-center">
        {/* <img src="/assets/images/simulators/injection/form.svg" /> */}
        <FormIllustration />
      </div>
      <div className="w-full lg:w-1/2 space-y-4">
        <h2 className="text-4xl text-brand-ternary !font-black">
          Recevez votre kit solaire personnalisé !
        </h2>
        <div className="space-y-4">
          <h6 className="font-bold text-md text-brand-ternary">
            Vos informations seront traitées de manière sécurisée et
            confidentielle.
          </h6>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
              <FormField
                control={form.control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nom complet</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Saisissez votre nom"
                        {...field}
                        required
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Adresse e-mail</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Saisissez votre adresse e-mail"
                        {...field}
                        required
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Numéro de télèphone</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Saisissez votre numéro de télèphone"
                        {...field}
                        required
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <Button
                variant="brand-ternary"
                className="w-full"
                disabled={isLoading || isSuccess}
              >
                {isLoading ? (
                  <TailSpin color="#fff" height={20} width={20} />
                ) : (
                  "Découvrez votre solution"
                )}
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}

function Solution({ solutions, solutionsStats }) {
  const [stats, setStats] = useState(solutionsStats[0]);

  return (
    <div className="flex flex-col-reverse md:flex-row items-center md:items-stretch gap-2 overflow-hidden">
      <aside className="w-full flex-1 bg-secondary p-4 rounded-lg space-y-6">
        <h2 className="!text-left text-2xl lg:text-3xl font-black text-brand-ternary">
          Voici ce que nous avons calculé pour vous !
        </h2>
        <div>
          <ul className="space-y-5 [&_svg]:flex-shrink-0">
            <li className="flex items-start gap-2">
              <svg width="24" height="24" viewBox="0 0 19 24" fill="none">
                <path
                  d="M9.25814 0V3.37529H10.5768C11.6235 3.37529 12.9422 4.81836 12.9595 5.88437L12.9508 10.7027L11.1091 12.5172V6.02404C11.1091 5.71338 10.5781 5.23737 10.2529 5.25053C7.82756 5.38957 5.20256 5.06638 2.80013 5.24865C2.30678 5.28623 1.98654 5.54991 1.86413 6.03532C1.87588 10.5568 1.785 15.1002 1.90988 19.6098C2.02735 19.9705 2.31111 20.1697 2.66721 20.2493L7.49866 20.2536C7.53699 20.9119 7.80097 21.5163 8.00808 22.1289H2.38654C1.31824 22.1289 -0.0375353 20.6282 0.000794824 19.529L0.00821356 5.84241C0.0397432 4.79393 1.35657 3.37529 2.38592 3.37529H3.7046V0H9.25752H9.25814ZM7.40716 1.87523H5.55619V3.37529H7.40716V1.87523Z"
                  fill="#FF6565"
                />
                <path
                  d="M13.87 12.4272C14.0666 12.3915 16.1179 14.6976 16.385 15.0271C17.9546 16.9624 19.3098 19.0293 17.9676 21.5509C16.1711 24.9262 11.2617 24.784 9.68275 21.3016C8.65526 19.0356 9.6469 17.3044 10.9996 15.5231C11.8441 14.4114 12.8388 13.3598 13.87 12.4272ZM14.8128 21.9417C15.8335 21.6298 16.5816 20.5951 16.6576 19.5216C16.733 18.4612 15.4527 16.8284 14.7906 16.0336C14.6805 15.902 13.9393 15.0408 13.8706 15.0534C13.2017 15.7724 12.5247 16.539 11.9801 17.3601C11.5894 17.9489 11.0651 18.7926 11.117 19.521C11.1931 20.5895 11.943 21.6336 12.9618 21.9411V18.3779H14.8128V21.9411V21.9417Z"
                  fill="#FF6565"
                />
                <path
                  d="M6.15612 10.877H9.81231L7.29118 15.9822L5.65226 15.121L6.80402 12.7522H3.14844L5.66957 7.64697L7.30849 8.5088L6.15612 10.877Z"
                  fill="#FF6565"
                />
              </svg>
              <span>
                Consommation électrique anuelle est de{" "}
                <strong>{stats?.yearly_consumption} kWh</strong>.
              </span>
            </li>
            <li className="flex items-start gap-2">
              <svg width="24" height="24" viewBox="0 0 26 24" fill="none">
                <path
                  d="M17.4781 6.62012V12.7493C15.1953 13.6269 13.4642 15.7254 13.369 18.2061H7.49062L7.46491 17.9995L5.1237 20.275H0V19.4472H4.81167L7.49062 16.8089V17.947L11.574 13.9129L11.2613 16.9126L14.5156 12.39L12.9076 11.5511L13.2384 8.01732L10.4038 12.3879L11.2863 13.0084L7.48992 16.7585V6.62012H17.4774H17.4781Z"
                  fill="#FF6565"
                />
                <path
                  d="M19.0272 13.257C23.6512 12.8486 26.6512 17.9468 23.9695 21.7404C21.8367 24.7573 17.285 24.7497 15.1453 21.7404C12.7561 18.3807 14.9021 13.6211 19.0265 13.257H19.0272ZM16.9834 16.1377C16.613 16.1004 16.1898 16.1654 15.8131 16.1377V17.3014C15.8131 18.4623 17.2224 19.8615 18.3878 19.8615H19.1418V22.7582H19.9743V19.8615H20.7283C21.8958 19.8615 23.303 18.4602 23.303 17.3014V16.1377H22.1328C21.3267 16.1377 20.2745 16.902 19.9132 17.6034L19.5324 18.673C19.4191 17.365 18.2933 16.2704 16.9827 16.1384L16.9834 16.1377Z"
                  fill="#FF6565"
                />
                <path
                  d="M17.4797 2.48242H7.49219V5.793H17.4797V2.48242Z"
                  fill="#FF6565"
                />
                <path
                  d="M13.3699 19.0342C13.3845 20.2372 13.8918 21.3711 14.5665 22.3448H7.49219V19.0342H13.3699Z"
                  fill="#FF6565"
                />
                <path d="M14.982 0H9.98828V1.65495H14.982V0Z" fill="#FF6565" />
                <path
                  d="M25.7985 2.06885V2.89667H20.155L18.3086 4.70709V3.54275L19.843 2.06885H25.7985Z"
                  fill="#FF6565"
                />
                <path
                  d="M6.6581 8.84478L4.81167 7.03436H0V6.20654H5.1237L6.6581 7.68114V8.84478Z"
                  fill="#FF6565"
                />
                <path
                  d="M25.7996 0.414062H22.0547V1.24188H25.7996V0.414062Z"
                  fill="#FF6565"
                />
                <path
                  d="M2.91313 4.55176H0V5.37958H2.91313V4.55176Z"
                  fill="#FF6565"
                />
                <path
                  d="M2.91313 17.7935H0V18.6213H2.91313V17.7935Z"
                  fill="#FF6565"
                />
                <path
                  d="M21.2231 0.414062H20.3906V1.24188H21.2231V0.414062Z"
                  fill="#FF6565"
                />
                <path
                  d="M4.57862 4.55176H3.74609V5.37958H4.57862V4.55176Z"
                  fill="#FF6565"
                />
                <path
                  d="M4.57862 17.7935H3.74609V18.6213H4.57862V17.7935Z"
                  fill="#FF6565"
                />
              </svg>

              <span>
                Production solaire anuelle est de{" "}
                <strong>{stats?.produced_energy} kWh</strong>.
              </span>
            </li>
            <li className="flex items-start gap-2">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8.25075 6.95126V7.89187C8.25075 8.46542 7.34332 8.52134 6.93859 8.67046C6.6733 8.76868 6.05119 9.11281 5.85526 9.1465C5.37759 9.22751 5.03864 8.52851 4.74546 8.25105C4.51378 8.36433 3.37181 9.26623 3.38754 9.44188C3.54486 9.77238 4.08187 10.1287 4.05828 10.5194C4.04326 10.7725 3.58776 11.303 3.44331 11.559C3.29887 11.8149 2.99925 12.5749 2.81834 12.7061C2.46581 12.9613 1.9009 12.6616 1.50404 12.6731C1.38819 12.7534 1.10932 14.4052 1.18511 14.479C1.48258 14.535 2.119 14.5507 2.3371 14.7465C2.59452 14.978 2.5316 15.563 2.59095 15.89C2.65244 16.2291 2.88127 16.8793 2.89485 17.1453C2.91845 17.6213 2.19408 17.9196 1.82796 18.0845C1.75932 18.1777 2.6217 19.6682 2.71609 19.6904C3.16086 19.5685 3.52841 19.0789 4.02538 19.2352C4.20058 19.2904 4.61675 19.8066 4.79981 19.9621C5.07154 20.1937 5.82236 20.6167 5.88314 20.9171C5.95966 21.2963 5.68793 21.6935 5.59569 22.0577L5.62572 22.1402L7.28612 22.7603C7.43843 22.4363 7.5464 21.6656 7.92611 21.5473C8.49102 21.5308 9.0502 21.591 9.61726 21.5795C9.87468 21.5745 10.0949 21.4498 10.3774 21.5645C10.7542 21.7172 10.7993 22.4076 11.0159 22.7101C11.1826 22.7488 12.4318 22.2893 12.6063 22.1624C12.6563 22.1258 12.6892 22.1215 12.6842 22.0455C12.6706 21.8168 12.3925 21.3716 12.3731 21.1142C12.3367 20.6253 13.0296 20.3507 13.3599 20.0704C13.5766 19.8861 14.0629 19.2825 14.2845 19.2251C14.8137 19.0868 15.4051 19.7671 15.5831 19.6725C15.7547 19.5033 16.517 18.1992 16.4362 18.0866C16.1359 17.9318 15.4809 17.6579 15.4258 17.2994C15.3807 17.0077 15.6203 16.2993 15.6825 15.9502C15.744 15.6068 15.6889 15.039 15.9042 14.7895C16.0965 14.5665 16.7887 14.5256 17.0826 14.479C17.0869 14.1127 16.9861 13.5685 17.418 13.3915C18.3383 13.0129 18.3118 14.4633 18.2632 14.9931C18.2024 15.6634 17.3686 15.5609 16.8695 15.6627L16.6807 16.9015C17.0204 17.1682 17.782 17.3726 17.7798 17.8945C17.7798 18.0752 17.4902 18.7104 17.3965 18.9082C17.2013 19.3212 16.2953 20.8941 15.9085 21.0153C15.4251 21.1673 15.0218 20.6654 14.5935 20.5163L13.6624 21.2619C13.8884 22.1932 14.4004 22.5123 13.3721 23.0915C12.8365 23.3934 11.2133 24.0157 10.6348 23.9985C10.0241 23.9805 9.97336 23.1482 9.75169 22.7281C9.35411 22.7474 8.94151 22.8148 8.5518 22.7022C8.3101 23.1066 8.29008 23.9734 7.6887 23.9999C7.21533 24.0207 5.95608 23.5654 5.48556 23.3604C5.21741 23.2442 4.43155 22.8865 4.33144 22.6384C4.15052 22.1918 4.70399 21.4404 4.58743 21.2548L3.67643 20.5156C3.09865 20.7522 2.67819 21.3903 2.09826 20.7952C1.65206 20.3371 1.05497 19.3061 0.803265 18.7089C0.668116 18.3892 0.46146 17.997 0.568721 17.6479C0.675982 17.2987 1.59986 17.0492 1.60272 16.8299L1.39678 15.6663C0.224771 15.4999 -0.0612585 15.581 0.0102488 14.2912C0.0460025 13.6424 0.274111 12.4007 0.543693 11.8164C0.876917 11.0937 1.5498 11.5755 2.14403 11.5697L2.72181 10.5273C2.52373 10.0814 1.86872 9.77668 2.04034 9.23397C2.16905 8.82675 3.22593 7.91123 3.60063 7.62804C3.81873 7.46315 4.57814 6.92115 4.80553 6.8982C5.28677 6.84945 5.58424 7.57427 5.93606 7.80441L7.07017 7.42945C7.13452 6.93979 6.87066 6.13396 7.46489 5.91672C7.89965 5.75828 9.30763 5.70666 9.77386 5.77262C10.3845 5.85937 10.4875 6.67094 9.92831 6.8853C9.59866 7.01148 9.44993 6.8939 9.13673 6.89319C8.84784 6.89319 8.55394 6.99427 8.25361 6.95054L8.25075 6.95126Z"
                  fill="#FF6565"
                />
                <path
                  d="M23.0275 6.95211L22.9796 7.5063C23.0426 7.69413 23.729 7.77013 23.659 8.36231C23.6153 8.73368 22.7665 10.217 22.4655 10.4479C22.2524 10.612 22.0458 10.5991 21.8112 10.5102C21.3464 10.3332 21.4294 10.1281 20.9546 10.6336C21.4751 11.6193 20.7887 11.8997 19.9885 12.2087C19.1504 12.5327 18.1822 12.9241 17.992 11.6846L17.4936 11.6889C17.209 12.0796 17.2676 12.5764 16.6319 12.5456C16.1979 12.5248 14.8249 12.0187 14.4881 11.7376C14.0305 11.3569 14.4953 10.8701 14.451 10.6422C14.4381 10.5748 14.1521 10.3346 14.0841 10.3109C13.8953 10.2457 13.4685 10.7497 13.0408 10.5031C12.7055 10.3102 11.8409 8.8147 11.7909 8.4254C11.7129 7.81888 12.0741 7.80382 12.4724 7.51132L12.3701 6.95569C11.6579 6.90479 11.3854 6.75997 11.4119 6.01078C11.4262 5.60141 11.65 4.26577 11.9003 3.97469C12.1878 3.63989 12.5646 3.84134 12.9279 3.83561C12.9765 3.71947 13.1245 3.55672 13.1567 3.45779C13.2325 3.2255 12.7226 2.99035 12.7519 2.57596C12.7813 2.16157 13.7609 1.36578 14.1027 1.13278C14.8114 0.650282 15.0681 0.586475 15.6108 1.34714L16.0685 1.17149C16.21 1.0532 15.944 0.299702 16.5583 0.10398C16.9981 -0.0358218 18.8043 -0.0774038 19.1204 0.255252C19.3907 0.539874 19.1998 1.06108 19.3321 1.17149L19.7883 1.34355C19.957 1.33137 20.1022 0.6087 20.7686 0.803705C21.1312 0.909811 22.2967 1.89631 22.5134 2.22251C22.9575 2.89141 22.2117 3.1875 22.2467 3.40473L22.4898 3.8055C22.7637 3.94028 23.1934 3.5431 23.531 3.9962C23.8213 4.3855 24.0615 5.95557 23.9857 6.43664C23.9185 6.86393 23.3794 6.94064 23.0261 6.95139L23.0275 6.95211ZM18.095 1.20519C17.8561 1.11055 17.5758 1.11844 17.3306 1.19157C17.2362 1.25896 17.4271 1.78017 17.0588 2.05834C16.7399 2.29851 15.9233 2.3745 15.5457 2.63905C15.1317 2.77312 14.9672 2.25693 14.7849 2.1372C14.727 2.0992 14.7306 2.10709 14.6755 2.1372C14.6154 2.16946 14.1592 2.5817 14.1499 2.63116C14.2479 2.94016 14.5589 3.0477 14.5146 3.42481C14.4939 3.60404 14.0748 4.15464 13.9518 4.36829C13.8725 4.50594 13.8496 4.68015 13.7581 4.81924C13.5128 5.19204 13.1238 5.07375 12.7519 5.01711L12.5889 5.82223C12.9264 5.89679 13.3977 5.92331 13.4885 6.34487C13.5178 6.48109 13.4813 6.6216 13.5063 6.7571C13.6501 7.5572 14.0348 8.11641 13.0766 8.53725C13.196 8.68852 13.3612 9.23625 13.5678 9.20327C13.7044 9.18177 13.8875 8.95307 14.1192 8.94446C14.5046 8.93012 14.7413 9.34379 14.9973 9.56031C15.2855 9.80406 15.7631 9.97111 15.7502 10.4178C15.7445 10.6221 15.5793 10.825 15.5872 11.0372L16.3867 11.2501C16.4553 10.9031 16.6048 10.531 17.0052 10.4973C17.1833 10.4823 17.3248 10.5554 17.4843 10.5561C17.7889 10.5576 18.2888 10.4615 18.5319 10.526C18.9545 10.6371 18.9674 11.2049 19.0997 11.2494L19.8104 11.0336C19.7754 10.5282 19.4622 10.3941 19.8941 9.96179C20.2366 9.81912 20.8301 9.06132 21.109 8.97601C21.4644 8.86775 21.8212 9.25704 21.9557 9.1875L22.3239 8.58169C22.3361 8.43616 21.7976 8.29707 21.7132 7.97445C21.6439 7.71062 21.837 7.16647 21.8963 6.86464C21.9213 6.73631 21.8899 6.59293 21.9149 6.45313C22.0071 5.94625 22.3804 5.91399 22.8066 5.81577L22.7051 5.01496C21.6746 5.26732 21.7097 4.81494 21.3228 4.1188C21.2134 3.92164 21.006 3.73524 20.941 3.53163C20.7786 3.02332 21.3414 2.73655 21.3135 2.62399L20.6971 2.12143C20.0335 2.92511 19.8977 2.60464 19.1468 2.32575C18.4597 2.07052 18.0492 2.19384 18.095 1.20519Z"
                  fill="#FF6565"
                />
                <path
                  d="M8.77773 9.7094C12.047 9.45848 14.7093 12.38 14.2409 15.6277C13.6181 19.9429 8.16133 21.5854 5.28173 18.2975C2.46935 15.0857 4.55665 10.0335 8.77773 9.7094ZM8.67046 10.8923C5.61639 11.2064 4.10973 14.8899 5.98322 17.3246C7.86601 19.7715 11.761 19.2317 12.8579 16.3353C13.9434 13.4697 11.6816 10.5826 8.67046 10.8923Z"
                  fill="#FF6565"
                />
                <path
                  d="M15.8723 5.89386C16.4351 5.32964 17.0071 6.27957 17.4054 6.57495L18.7791 4.70089C19.226 4.39333 19.8102 4.75036 19.6958 5.29881C19.6522 5.50744 18.2292 7.40946 17.9932 7.67544C17.7429 7.95719 17.4733 8.18661 17.0872 7.94357C16.9442 7.85324 15.9653 6.86531 15.8537 6.70901C15.6656 6.44518 15.6242 6.14192 15.8716 5.89386H15.8723Z"
                  fill="#FF6565"
                />
                <path
                  d="M9.2164 14.3177H10.4742C10.6587 14.3177 11.0105 14.8554 10.8346 15.0562L8.94753 17.6235C8.51706 17.9454 7.93499 17.6171 7.98576 17.0923C8.02151 16.7216 8.94825 15.857 9.10914 15.4469H7.85133C7.83559 15.4469 7.61178 15.3143 7.58246 15.287C7.4087 15.1236 7.42586 14.8562 7.47448 14.6382L9.29577 12.1669C9.58037 11.8995 10.0938 11.9698 10.2461 12.3426C10.5 12.9649 9.3494 13.7392 9.2164 14.3185V14.3177Z"
                  fill="#FF6565"
                />
              </svg>

              <span>
                Puissance crête de votre solution solaire est de{" "}
                <strong>{stats?.systeme_max_capacity} kWc</strong>.
              </span>
            </li>
            <li className="flex items-start gap-2">
              <svg width="24" height="24" viewBox="0 0 20 24" fill="none">
                <path
                  d="M11.6574 13.5143L11.6489 3.9741C11.4883 2.98207 10.7444 2.11793 9.71912 1.9494C9.6213 1.50478 9.93287 0.21992 9.45464 0.0191235L2.24501 0C1.70399 0.168526 2.04213 1.4988 1.93827 1.9494C0.86588 2.12988 0.0652128 3.0753 0 4.14502L0.00362293 21.8665C0.0966115 22.861 1.03495 23.8661 2.03609 24H9.34958C8.66122 23.4633 8.12261 22.8693 7.61178 22.1761H2.27279C2.0059 22.1761 1.78973 21.9633 1.78973 21.6992V18.612C1.78973 18.3478 2.0059 18.1339 2.27279 18.1339H6.94637L7.11423 17.4669H2.27279C2.0059 17.4669 1.78973 17.253 1.78973 16.9888V13.9028C1.78973 13.6386 2.0059 13.4247 2.27279 13.4247H9.28316C9.54884 13.4247 9.76501 13.6386 9.76501 13.9028V14.1813C10.3592 13.8622 10.9871 13.6231 11.6574 13.5143ZM1.78973 4.23347C1.78973 3.97052 2.0059 3.75657 2.27279 3.75657H9.28316C9.54884 3.75657 9.76501 3.97052 9.76501 4.23347V7.32072C9.76501 7.58486 9.54884 7.7988 9.28316 7.7988H2.27279C2.0059 7.7988 1.78973 7.58486 1.78973 7.32072V4.23347ZM9.28316 12.5629H2.27279C2.0059 12.5629 1.78973 12.349 1.78973 12.0849V8.99761C1.78973 8.73466 2.0059 8.52072 2.27279 8.52072H9.28316C9.54884 8.52072 9.76501 8.73466 9.76501 8.99761V12.0849C9.76501 12.349 9.54884 12.5629 9.28316 12.5629Z"
                  fill="#FF6565"
                />
                <path
                  d="M17.9676 9.10391V13.5788C17.9676 13.7772 17.6283 13.8967 17.4544 13.9123C16.5124 13.9971 15.4388 13.8465 14.4812 13.9063C14.352 13.9218 14.0827 13.6744 14.0827 13.58V9.10511H12.9595C12.7096 9.10511 12.4862 8.52304 12.7192 8.32702L15.6586 5.40949C15.8072 5.2565 16.0113 5.22065 16.2093 5.28758L19.3287 8.32702C19.5617 8.52304 19.3383 9.10511 19.0883 9.10511H17.9652L17.9676 9.10391Z"
                  fill="#FF6565"
                />
                <path
                  d="M7.16135 21.1174H2.91406V19.1943H6.79784C6.81475 19.8481 6.90774 20.5115 7.16135 21.1174Z"
                  fill="#FF6565"
                />
                <path
                  d="M8.73974 4.77832H2.91406V6.70023H8.73974V4.77832Z"
                  fill="#FF6565"
                />
                <path
                  d="M8.73974 9.58447H2.91406V11.5064H8.73974V9.58447Z"
                  fill="#FF6565"
                />
                <path
                  d="M8.73974 4.77832H2.91406V6.70023H8.73974V4.77832Z"
                  fill="#FF6565"
                />
                <path
                  d="M8.73974 9.58447H2.91406V11.5064H8.73974V9.58447Z"
                  fill="#FF6565"
                />
                <path
                  d="M7.55624 16.3116H2.91406V14.3896H8.73974C8.16852 14.8462 7.48982 15.61 7.55624 16.3116Z"
                  fill="#FF6565"
                />
                <path
                  d="M12.068 14.4073C11.1924 14.4922 10.4123 14.8065 9.765 15.2786C9.00781 15.8308 8.43176 16.5993 8.09966 17.4671C8.01633 17.6834 7.9487 17.9069 7.89677 18.134C7.66974 19.1236 7.74703 20.1945 8.20472 21.1985C8.36896 21.5595 8.56822 21.8846 8.79768 22.1762C10.8386 24.7794 15.1668 24.6169 16.8853 21.5177C18.803 18.0587 15.9916 14.0272 12.068 14.4073ZM13.6886 18.7137C13.9253 18.7137 14.2079 19.187 14.0159 19.3985L12.0003 21.9384C11.6513 22.2384 11.1079 21.973 11.1804 21.5224C11.2444 21.1185 12.3759 20.1396 12.5655 19.6746C12.2612 19.6388 11.853 19.7248 11.5644 19.6746C11.2903 19.628 11.0632 19.236 11.2371 18.991L13.2527 16.4511C13.6017 16.1511 14.1451 16.4165 14.0727 16.8671C14.0087 17.271 12.8771 18.2499 12.6875 18.7137H13.6886Z"
                  fill="#FF6565"
                />
                <path
                  d="M6.79784 19.1943C6.81475 19.8481 6.90774 20.5115 7.16135 21.1174H2.91406V19.1943H6.79784Z"
                  fill="#FF6565"
                />
              </svg>

              <span>
                Rendement annuel de votre solution est de{" "}
                <strong>{stats?.annual_yield} kWh/kWc</strong>.
              </span>
            </li>
            <li className="flex items-start gap-2">
              <svg width="24" height="24" viewBox="0 0 19 19" fill="none">
                <path d="M19 18.3291H0V18.9996H19V18.3291Z" fill="#FF6565" />
                <path
                  d="M16.9571 17.6591H2.03906V7.20516L9.50947 2.0498L16.9571 7.20516V17.6591ZM8.16198 4.89493V6.01347L8.10583 6.06986H6.91767C6.90249 6.06986 6.66425 6.16891 6.62783 6.18872C6.34559 6.33959 6.16805 6.6337 6.11949 6.9461V16.0407C6.15135 16.5101 6.50644 16.8652 6.95712 16.949C8.57169 16.8591 10.3319 17.0785 11.9283 16.9536C12.4488 16.9124 12.816 16.5619 12.8752 16.0407V6.9461C12.8251 6.6337 12.6491 6.33959 12.3653 6.18872C12.3274 6.16891 12.0907 6.06986 12.0755 6.06986H10.8873L10.8312 6.01347V4.89493C10.8312 4.80959 10.5702 4.69987 10.4791 4.69073C10.0148 4.63739 9.06638 4.65568 8.58534 4.6892C8.45636 4.69834 8.19991 4.75321 8.16046 4.89645L8.16198 4.89493Z"
                  fill="#FF6565"
                />
                <path
                  d="M18.4025 7.33149L9.51779 1.29532L9.2659 1.37913L0.704441 7.31625L0.598219 7.33149L0.117188 6.49639L9.51172 0L18.8729 6.4781L18.4025 7.33149Z"
                  fill="#FF6565"
                />
                <path
                  d="M4.74829 1.29883V2.49204L3.375 3.38505V1.29883H4.74829Z"
                  fill="#FF6565"
                />
                <path
                  d="M7.04096 6.74742L11.8953 6.73828C12.0243 6.73828 12.1138 6.83581 12.1912 6.92572L12.2124 16.0006C12.2109 16.1194 12.1001 16.188 12.0152 16.2505L7.10621 16.2794C6.97723 16.2794 6.8877 16.1819 6.81031 16.092L6.78906 7.01715C6.78906 6.90743 6.93929 6.77028 7.03944 6.74742H7.04096ZM7.40515 11.1728C7.68284 11.2277 8.12745 11.1317 8.42639 11.1728L7.81638 15.2538C7.81941 15.5038 8.08952 15.6882 8.31258 15.548C9.52503 14.021 10.7405 12.4712 11.871 10.8833C11.9423 10.7675 11.7268 10.5008 11.6373 10.5008H10.7284L11.8619 7.8553C11.9287 7.53223 11.698 7.43013 11.4173 7.40727C10.5933 7.33717 9.66311 7.45451 8.827 7.41794C8.70864 7.45147 8.58572 7.48652 8.53261 7.6069L7.15477 10.734C7.07131 10.9001 7.24733 11.1424 7.40818 11.1744L7.40515 11.1728Z"
                  fill="#FF6565"
                />
                <path
                  d="M10.1674 5.36133H8.83203V6.06994H10.1674V5.36133Z"
                  fill="#FF6565"
                />
                <path
                  d="M10.8354 11.1736L8.68369 13.9684L9.16169 10.8521C9.19204 10.7423 9.01298 10.5031 8.92345 10.5031H7.97656L9.07064 8.08008H10.9629L11.0024 8.15627L9.84606 10.7621C9.79295 10.9008 9.97808 11.1736 10.1101 11.1736H10.8339H10.8354Z"
                  fill="#FF6565"
                />
              </svg>

              <span>
                Taux de couverture électrique de votre solution est de{" "}
                <strong>{stats?.energy_covrage} %</strong>.
              </span>
            </li>
            <li className="flex items-start gap-2">
              <svg width="24" height="24" viewBox="0 0 30 20" fill="none">
                <path
                  d="M7.74786 3.21109C14.6547 2.65588 19.4512 9.80701 15.9951 15.7758C12.498 21.8147 3.40945 21.2437 0.664236 14.8566C-1.55855 9.68772 2.04845 3.66983 7.74786 3.21109ZM8.94543 4.39169C8.88023 4.32854 8.63197 4.23732 8.53463 4.24258C7.79787 4.27854 8.08721 5.40301 8.01934 5.8749C5.03836 6.37222 4.36947 10.4131 7.08968 11.763C8.15865 12.2936 9.26871 11.8568 10.135 12.7769C11.7996 14.5452 9.6295 17.2502 7.4719 16.0486C6.59404 15.56 6.5869 14.9759 6.40829 14.1382C6.27165 13.4953 5.32503 13.5523 5.35271 14.382C5.40094 15.8065 6.60655 17.0844 8.01934 17.316C8.08721 17.7879 7.79787 18.9115 8.53463 18.9483C9.32676 18.9878 9.04634 17.7879 9.10617 17.316C12.0907 16.8231 12.7534 12.7769 10.0358 11.4279C8.9615 10.8946 7.88985 11.3323 6.98966 10.4148C5.18303 8.5729 7.82376 5.69333 9.89294 7.30021C10.6056 7.85367 10.5529 8.28433 10.7172 9.05181C10.8548 9.69386 11.8014 9.6386 11.7728 8.80797C11.7237 7.38441 10.5234 6.10119 9.10617 5.87402C9.0383 5.49949 9.22584 4.66623 8.94543 4.39082V4.39169Z"
                  fill="#FF6565"
                />
                <path
                  d="M29.9548 20.0001H26.7461V6.7048C26.7461 6.48815 27.1194 6.35746 27.3114 6.3408C27.7865 6.29869 29.0716 6.2794 29.5163 6.34781C29.6663 6.37062 29.9548 6.56797 29.9548 6.7048V20.0001Z"
                  fill="#FF6565"
                />
                <path
                  d="M26.4592 0.055293C26.828 -0.0183848 28.7213 -0.0148763 29.1133 0.0447676C29.5054 0.104411 29.9082 0.547355 29.9546 0.925392C30.0189 1.45254 30.0046 2.68489 29.9555 3.22782C29.8474 4.42509 28.3998 4.59963 27.847 3.58744L24.2052 7.16607C23.0183 8.06336 22.2253 6.25387 21.3716 5.75742L17.853 9.1352C17.6315 8.44403 17.3841 7.76251 17.0117 7.1345L20.7259 3.42079C21.8609 2.56209 22.6924 4.21107 23.4684 4.80488H23.6006L26.3404 2.10862C25.4135 1.71743 25.476 0.251767 26.4574 0.055293H26.4592Z"
                  fill="#FF6565"
                />
                <path
                  d="M25.6775 20H22.4688V9.85617C22.4688 9.63952 22.842 9.50883 23.034 9.49216C23.5091 9.45006 24.7942 9.43077 25.239 9.49918C25.389 9.52199 25.6775 9.71934 25.6775 9.85617V20Z"
                  fill="#FF6565"
                />
                <path
                  d="M21.4001 19.9999H18.1914V13.0075C18.1914 12.7909 18.5647 12.6602 18.7567 12.6435C19.2318 12.6014 20.5169 12.5821 20.9616 12.6505C21.1117 12.6734 21.4001 12.8707 21.4001 13.0075V19.9999Z"
                  fill="#FF6565"
                />
              </svg>

              <span>
                Gains financiers annuels par votre solution est de{" "}
                <strong>{stats?.yearly_savings} DH</strong>.
              </span>
            </li>
            <li className="flex items-start gap-2">
              <svg width="24" height="24" viewBox="0 0 30 20" fill="none">
                <path
                  d="M7.74786 3.21109C14.6547 2.65588 19.4512 9.80701 15.9951 15.7758C12.498 21.8147 3.40945 21.2437 0.664236 14.8566C-1.55855 9.68772 2.04845 3.66983 7.74786 3.21109ZM8.94543 4.39169C8.88023 4.32854 8.63197 4.23732 8.53463 4.24258C7.79787 4.27854 8.08721 5.40301 8.01934 5.8749C5.03836 6.37222 4.36947 10.4131 7.08968 11.763C8.15865 12.2936 9.26871 11.8568 10.135 12.7769C11.7996 14.5452 9.6295 17.2502 7.4719 16.0486C6.59404 15.56 6.5869 14.9759 6.40829 14.1382C6.27165 13.4953 5.32503 13.5523 5.35271 14.382C5.40094 15.8065 6.60655 17.0844 8.01934 17.316C8.08721 17.7879 7.79787 18.9115 8.53463 18.9483C9.32676 18.9878 9.04634 17.7879 9.10617 17.316C12.0907 16.8231 12.7534 12.7769 10.0358 11.4279C8.9615 10.8946 7.88985 11.3323 6.98966 10.4148C5.18303 8.5729 7.82376 5.69333 9.89294 7.30021C10.6056 7.85367 10.5529 8.28433 10.7172 9.05181C10.8548 9.69386 11.8014 9.6386 11.7728 8.80797C11.7237 7.38441 10.5234 6.10119 9.10617 5.87402C9.0383 5.49949 9.22584 4.66623 8.94543 4.39082V4.39169Z"
                  fill="#FF6565"
                />
                <path
                  d="M29.9548 20.0001H26.7461V6.7048C26.7461 6.48815 27.1194 6.35746 27.3114 6.3408C27.7865 6.29869 29.0716 6.2794 29.5163 6.34781C29.6663 6.37062 29.9548 6.56797 29.9548 6.7048V20.0001Z"
                  fill="#FF6565"
                />
                <path
                  d="M26.4592 0.055293C26.828 -0.0183848 28.7213 -0.0148763 29.1133 0.0447676C29.5054 0.104411 29.9082 0.547355 29.9546 0.925392C30.0189 1.45254 30.0046 2.68489 29.9555 3.22782C29.8474 4.42509 28.3998 4.59963 27.847 3.58744L24.2052 7.16607C23.0183 8.06336 22.2253 6.25387 21.3716 5.75742L17.853 9.1352C17.6315 8.44403 17.3841 7.76251 17.0117 7.1345L20.7259 3.42079C21.8609 2.56209 22.6924 4.21107 23.4684 4.80488H23.6006L26.3404 2.10862C25.4135 1.71743 25.476 0.251767 26.4574 0.055293H26.4592Z"
                  fill="#FF6565"
                />
                <path
                  d="M25.6775 20H22.4688V9.85617C22.4688 9.63952 22.842 9.50883 23.034 9.49216C23.5091 9.45006 24.7942 9.43077 25.239 9.49918C25.389 9.52199 25.6775 9.71934 25.6775 9.85617V20Z"
                  fill="#FF6565"
                />
                <path
                  d="M21.4001 19.9999H18.1914V13.0075C18.1914 12.7909 18.5647 12.6602 18.7567 12.6435C19.2318 12.6014 20.5169 12.5821 20.9616 12.6505C21.1117 12.6734 21.4001 12.8707 21.4001 13.0075V19.9999Z"
                  fill="#FF6565"
                />
              </svg>

              <span>
                Temps de retour sur investissement de votre installation est de{" "}
                <strong>{stats?.tri} ans</strong>.
              </span>
            </li>
            <li className="flex items-start gap-2">
              <svg width="24" height="24" viewBox="0 0 24 25" fill="none">
                <path
                  d="M18.938 6.55665C19.0976 6.71845 19.5495 6.56909 19.7458 6.56702C23.5025 6.53175 25.343 10.8132 22.8787 13.6219C22.9237 13.1033 22.5066 12.9913 22.0976 12.8523C19.9421 12.1221 17.9012 12.7009 17.0935 14.9681C16.9299 14.7233 16.8501 14.4329 16.7049 14.1778C15.7254 12.4748 13.6579 12.2673 11.9585 12.8813C11.7376 12.9622 11.298 13.0971 11.2898 13.3771C11.2755 13.792 11.5209 14.4226 11.6845 14.8042L11.6211 14.9059L4.09142 14.9142C-1.45459 14.8997 -1.29713 6.33677 4.1998 6.56702C4.47179 6.57739 5.09755 6.7869 5.28365 6.70185C5.54541 6.58154 5.42066 5.79744 5.4677 5.48836C5.72332 3.82681 7.66811 2.75645 9.21207 3.36423C9.54336 3.49491 9.97485 3.98653 10.2959 3.7625C10.3798 3.70442 10.662 2.70251 10.7744 2.47226C13.0689 -2.21368 20.2141 0.258937 19.0587 5.65223C19.0096 5.87834 18.7417 6.35336 18.938 6.55457V6.55665ZM8.34909 6.53175C5.32455 6.87195 5.80921 11.5392 8.81126 11.255C9.22434 11.2156 10.5802 10.7136 10.298 10.1245C9.95236 9.72832 9.63948 10.2759 9.2816 10.4398C8.04847 11.0061 6.81739 10.027 6.92577 8.6953C7.02598 7.47143 8.48406 6.75993 9.49428 7.44239C9.6906 7.57515 10.0096 8.06055 10.2775 7.67887C10.6702 7.11879 9.25297 6.56702 8.86238 6.52346C8.69265 6.50479 8.52087 6.51309 8.35113 6.53175H8.34909ZM12.5413 6.52968C10.9769 6.73711 11.0014 8.53557 11.1221 9.77188C11.3123 11.7259 14.2264 11.7653 14.4125 9.66401C14.5311 8.31777 14.4043 6.28076 12.5434 6.52968H12.5413ZM16.6947 10.6078C17.0976 10.0291 17.6988 9.32797 17.1957 8.61855C16.9176 8.22649 16.3593 8.07922 15.9135 8.22649C15.5475 8.34681 14.8767 9.21388 15.4309 9.3902C15.9033 9.54163 15.8297 8.82805 16.2959 8.82598C17.0751 8.82183 16.6231 9.59764 16.3757 9.93368C16.2305 10.1328 15.662 10.6535 15.6088 10.7696C15.5372 10.9273 15.5822 11.1534 15.7417 11.226C15.844 11.2716 17.3184 11.282 17.4554 11.2426C17.7928 11.1451 17.6252 10.6078 17.4657 10.6078H16.6967H16.6947Z"
                  fill="#FF6565"
                />
                <path
                  d="M7.14075 15.5615V19.3576L7.2205 19.4385H8.28185C8.37592 19.4385 8.55997 19.845 8.41887 19.955L5.91989 23.2345C5.73584 23.442 5.50067 23.3922 5.33707 23.1806L2.89126 19.955C2.75016 19.845 2.93421 19.4385 3.02827 19.4385H4.08962L4.16938 19.3576V15.5615H7.14279H7.14075Z"
                  fill="#FF6565"
                />
                <path
                  d="M12.4473 21.3777H13.3757C13.5413 21.3777 13.6968 21.7636 13.5863 21.915C12.8256 22.7696 12.1978 23.9084 11.437 24.7423C11.1937 25.0078 11.0689 25.1219 10.7745 24.8253C10.0035 24.0474 9.34911 22.7613 8.58019 21.9357C8.41659 21.7926 8.59655 21.3777 8.7581 21.3777H9.68653V17.5278C9.68653 17.4075 10.0178 17.2934 10.1344 17.283C10.5557 17.2436 11.7581 17.2249 12.1507 17.2892C12.255 17.3058 12.4452 17.422 12.4452 17.5278V21.3777H12.4473Z"
                  fill="#FF6565"
                />
                <path
                  d="M16.3225 14.9707C15.6293 14.4417 14.7724 14.1389 13.9074 14.0559C13.7295 14.0393 13.1426 13.9999 13.0178 14.0663C12.7499 14.2094 12.7806 14.5454 13.0465 14.6595C13.2673 14.7549 13.6456 14.6782 13.9013 14.7072C14.9442 14.8276 15.8604 15.2486 16.5352 16.0742C16.5577 16.1883 16.4902 16.1468 16.4186 16.1697C15.7295 16.3792 14.6845 16.3833 13.9994 16.1697C12.842 15.8066 12.1692 14.7467 11.9688 13.5726C13.6109 12.9316 15.662 13.0519 16.3205 14.9728L16.3225 14.9707Z"
                  fill="#FF6565"
                />
                <path
                  d="M22.1559 13.5764C21.9167 15.9183 19.6406 16.7543 17.5977 16.1548C18.0721 15.406 19.021 14.9206 19.8676 14.7692C20.1723 14.7152 21.0516 14.7339 21.203 14.6177C21.3359 14.5161 21.3563 14.207 21.2234 14.1054C21.0803 13.9975 20.4279 14.0348 20.2255 14.0556C19.3645 14.1406 18.5097 14.4663 17.8103 14.9704C18.5015 13.0827 20.5077 12.8981 22.1559 13.5764Z"
                  fill="#FF6565"
                />
                <path
                  d="M12.6487 7.17541C13.0782 7.1277 13.5362 7.39529 13.6937 7.81016C13.8307 8.16695 13.8225 9.64596 13.6773 10.0007C13.3604 10.7723 12.2009 10.7848 11.8512 10.0546C11.7121 9.76834 11.7223 8.52166 11.7551 8.15658C11.798 7.66081 12.1395 7.23142 12.6487 7.17541Z"
                  fill="#FF6565"
                />
              </svg>

              <span>
                Quantité des émissions CO₂ réduites par votre solution est de{" "}
                <strong>{stats?.reduced_CO2} tonnes</strong>.
              </span>
            </li>
          </ul>
        </div>
      </aside>
      <aside className="flex flex-col justify-center rounded-lg space-y-2">
        <Swiper
          modules={[Navigation]}
          slidesPerView={1}
          spaceBetween={10}
          navigation
          onActiveIndexChange={({ activeIndex }) => {
            setStats(solutionsStats[activeIndex]);
          }}
          className="flex-shrink-0 h-full w-[280px] max-w-[280px]"
        >
          {solutions?.map((solution) => (
            <SwiperSlide key={solution.id}>
              <div
                key={solution.id}
                className="relative h-full flex flex-col p-2 bg-secondary rounded-xl overflow-hidden"
              >
                <Link href={`/products/${solution.slug}`}>
                  <img
                    src={solution.full_image}
                    className="truncate bg-secondary rounded-lg aspect-square w-full"
                    alt={solution.title}
                  />
                </Link>
                <div className="flex flex-col py-2 px-2.5 h-full">
                  <Link href={`/products/${solution.slug}`} className="mb-2">
                    <h4 className="text-lg font-black line-clamp-2">
                      {solution.title}
                    </h4>
                  </Link>
                  <div className="space-y-3 mt-auto">
                    <ProductPrice product={solution} />
                    <Button
                      variant="brand-ternary"
                      className="w-full animate-pulse"
                      asChild
                    >
                      <Link href={`/products/${solution.slug}`}>
                        Acheter maintenant !
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))}
          <ReactConfetti />
        </Swiper>
      </aside>
    </div>
  );
}
