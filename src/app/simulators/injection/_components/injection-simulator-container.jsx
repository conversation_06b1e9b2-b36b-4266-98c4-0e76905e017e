"use client";

import { useState } from "react";

import DimensioningStep from "./dimensioning-step";
import EquipmentsInventoryStep from "./equipments-inventory-step";
import InvoicesInventoryStep from "./invoices-inventory-step";
import SolutionStep from "./solution-step";
import Step from "./step";
import SuperSimuStep from "./supersimu-step";
import TensionStep from "./tension-step";

export default function InjectionSimulatorContainer() {
  const [state, setState] = useState({
    activeStep: 0,
    zone: 2,
    type: null,
    dimensioning: null,
    consumption: {
      method: 0,
      monthlyConsumptions: {},
      items: [],
    },
  });

  const steps = [
    {
      title: "Localisation",
      component: SuperSimuStep,
      isEnabled: (state) => true,
      isValid: (state) => true,
    },
    {
      title: "Type d'installation",
      component: TensionStep,
      isEnabled: (state) => Boolean(state.zone),
      isValid: (state) => Boolean(state.type),
    },
    {
      title: "Dimensionnement",
      component: DimensioningStep,
      isEnabled: (state) => state.type !== null,
      isValid: (state) => Boolean(state.dimensioning),
    },
    {
      title: "Consommation",
      component:
        state.dimensioning === 1
          ? InvoicesInventoryStep
          : EquipmentsInventoryStep,
      isEnabled: (state) => state.dimensioning !== null,
      isValid: (state) => {
        if (state.dimensioning === 1) {
          const monthlyConsumptions = Object.entries(
            state.consumption.monthlyConsumptions
          );

          if (monthlyConsumptions.length !== 12) return false;

          if (state.type === 1) {
            return monthlyConsumptions.every(
              ([month, consumption]) => consumption > 0
            );
          } else {
            return monthlyConsumptions.every(([month, consumptions]) =>
              consumptions?.every((c) => c > 0)
            );
          }
        } else {
          return state.consumption.items.length > 0;
        }
      },
    },
    {
      title: "Solution Solaire",
      component: SolutionStep,
      isEnabled: (state) => {
        if (state.dimensioning === 1) {
          const monthlyConsumptions = Object.entries(
            state.consumption.monthlyConsumptions
          );

          if (monthlyConsumptions.length !== 12) return false;

          if (state.type === 1) {
            return monthlyConsumptions.every(
              ([month, consumption]) => consumption > 0
            );
          } else {
            return monthlyConsumptions.every(([month, consumptions]) =>
              consumptions?.every((c) => c > 0)
            );
          }
        } else {
          return state.consumption.items.length > 0;
        }
      },
      isValid: (state) => false,
    },
  ];

  return (
    <section className="container-fluid">
      <div className="w-full flex flex-col xl:flex-row items-center shadow">
        {steps.map((step, key) => (
          <Step
            key={key}
            number={key}
            step={step}
            state={state}
            setState={setState}
          />
        ))}
      </div>
    </section>
  );
}
