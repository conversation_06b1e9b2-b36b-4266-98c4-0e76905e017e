"use client";

import TensionIllustration from "../illustrations/tension-illustration";
import SelectableArea from "./selectable-area";
import StepQuestion from "./step-question";

export default function TensionStep({ state, setState }) {
  return (
    <div className="flex">
      <aside className="flex items-center justify-center w-full md:w-1/2">
        <div className="flex flex-col gap-4">
          <StepQuestion
            number={2}
            question="Quel type d'installation vous convient le mieux ?"
          />
          <div className="flex flex-nowrap gap-3 sm:gap-4">
            <SelectableArea
              selected={state.type === 1}
              className="relative rounded bg-secondary text-brand-primary"
              onClick={() =>
                setState((state) => ({
                  ...state,
                  type: 1,
                  consumption: {
                    ...state.consumption,
                    monthlyConsumptions: {},
                    items: [],
                  },
                }))
              }
            >
              <div className="py-3 px-4 sm:px-6 [&>svg]:w-28 [&>svg]:h-28">
                <svg
                  width="145"
                  height="129"
                  viewBox="0 0 145 129"
                  fill="none"
                  className="[&_path]:fill-brand-primary"
                >
                  <path d="M71.5553 3.63757C72.3561 3.47099 73.2346 3.54072 74.0432 3.63757L143.2 45.7776C147.049 49.1401 144.106 54.9974 139.064 53.9592L72.6748 13.7096L8.10513 53.2038C2.89601 56.4114 -2.12651 49.9187 2.39842 45.7776C24.0862 33.0751 45.0199 19.0323 66.7116 6.34928C67.8389 5.69072 70.4901 3.8545 71.5553 3.63757Z" />
                  <path d="M105.684 115.209C106.908 110.952 110.582 108.298 115.009 108.12C116.498 103.122 121.276 100.322 126.438 101.554C127.43 100.244 127.869 98.9929 129.276 97.9818C136.157 93.0426 144.227 102.421 138.276 108.399V108.829C141.498 109.417 143.73 112.245 144.674 115.209H105.684Z" />
                  <path d="M39.9203 115.209H0.925781C1.87042 112.242 4.10179 109.417 7.32833 108.829V108.399C1.40393 102.464 9.39254 93.1007 16.3315 97.9741C17.731 98.958 18.1781 100.279 19.1655 101.554C24.3279 100.322 29.1055 103.122 30.5944 108.12C35.061 108.317 38.6763 110.901 39.9203 115.209Z" />
                  <path d="M33.2261 13.1401V21.4031L14.6016 32.8582V13.1401H33.2261Z" />
                  <path d="M145 119.703V126.699C145 126.831 144.988 126.958 144.965 127.086C144.782 128.171 143.834 129 142.691 129H2.30912C2.24303 129 2.17306 128.996 2.10697 128.988C0.925201 128.888 0 127.904 0 126.699V119.703H145Z" />
                  <path d="M32.8488 0H13.8006C11.3316 0 9.33008 1.99455 9.33008 4.45496C9.33008 6.91536 11.3316 8.90991 13.8006 8.90991H32.8488C35.3178 8.90991 37.3194 6.91536 37.3194 4.45496C37.3194 1.99455 35.3178 0 32.8488 0Z" />
                  <path d="M42.7342 71.6665H26.2205C25.3059 71.6665 24.5645 72.4054 24.5645 73.3168V89.773C24.5645 90.6844 25.3059 91.4233 26.2205 91.4233H42.7342C43.6488 91.4233 44.3902 90.6844 44.3902 89.773V73.3168C44.3902 72.4054 43.6488 71.6665 42.7342 71.6665Z" />
                  <path d="M119.243 71.6666H102.729C101.816 71.6666 101.073 72.4065 101.073 73.3169V89.7731C101.073 90.6835 101.816 91.4234 102.729 91.4234H119.243C120.156 91.4234 120.899 90.6835 120.899 89.7731V73.3169C120.899 72.4065 120.156 71.6666 119.243 71.6666ZM119.243 71.6666H102.729C101.816 71.6666 101.073 72.4065 101.073 73.3169V89.7731C101.073 90.6835 101.816 91.4234 102.729 91.4234H119.243C120.156 91.4234 120.899 90.6835 120.899 89.7731V73.3169C120.899 72.4065 120.156 71.6666 119.243 71.6666ZM72.8935 18.9819L10.2402 57.3604V92.2989C10.2402 92.2989 15.9975 90.4007 21.3543 96.5911C21.3543 96.5911 28.6626 96.5098 33.8251 103.738C33.8251 103.738 43.738 107.252 44.5737 115.209H53.9346V68.5675H92.2566V115.209H101.027C101.027 115.209 102.033 106.152 111.32 104.021C111.32 104.021 116.199 96.4981 124.246 96.5911C124.246 96.5911 129.42 90.6564 135.656 92.3027V57.6509L72.8935 18.9819ZM46.3853 91.4272C46.3853 92.5197 45.4951 93.4107 44.3988 93.4107H24.5614C23.4652 93.4107 22.5711 92.5197 22.5711 91.4272V71.6627C22.5711 70.5703 23.4652 69.6793 24.5614 69.6793H44.3988C45.4951 69.6793 46.3853 70.5703 46.3853 71.6627V91.4272ZM72.9479 58.631C66.2149 58.631 60.7609 53.196 60.7609 46.4864C60.7609 39.7769 66.2149 34.3418 72.9479 34.3418C79.6808 34.3418 85.1349 39.7769 85.1349 46.4864C85.1349 53.196 79.6808 58.631 72.9479 58.631ZM122.893 91.4272C122.893 92.5197 121.999 93.4107 120.903 93.4107H101.069C99.9731 93.4107 99.079 92.5197 99.079 91.4272V71.6627C99.079 70.5703 99.9731 69.6793 101.069 69.6793H120.903C121.999 69.6793 122.893 70.5703 122.893 71.6627V91.4272ZM119.243 71.6666H102.729C101.816 71.6666 101.073 72.4065 101.073 73.3169V89.7731C101.073 90.6835 101.816 91.4234 102.729 91.4234H119.243C120.156 91.4234 120.899 90.6835 120.899 89.7731V73.3169C120.899 72.4065 120.156 71.6666 119.243 71.6666ZM72.9479 37.7896C68.1275 37.7896 64.2207 41.6828 64.2207 46.4864C64.2207 51.29 68.1275 55.1833 72.9479 55.1833C77.7682 55.1833 81.6751 51.29 81.6751 46.4864C81.6751 41.6828 77.7682 37.7896 72.9479 37.7896Z" />
                  <rect
                    x="58.6992"
                    y="73.2163"
                    width="28.378"
                    height="42.2252"
                    fill="url(#pattern0_2941_915)"
                  />
                  <path d="M42.7342 71.6665H26.2205C25.3059 71.6665 24.5645 72.4054 24.5645 73.3168V89.773C24.5645 90.6844 25.3059 91.4233 26.2205 91.4233H42.7342C43.6488 91.4233 44.3902 90.6844 44.3902 89.773V73.3168C44.3902 72.4054 43.6488 71.6665 42.7342 71.6665Z" />
                  <path d="M119.242 71.6665H102.728C101.814 71.6665 101.072 72.4054 101.072 73.3168V89.773C101.072 90.6844 101.814 91.4233 102.728 91.4233H119.242C120.157 91.4233 120.898 90.6844 120.898 89.773V73.3168C120.898 72.4054 120.157 71.6665 119.242 71.6665Z" />
                  <path d="M119.242 71.6665H102.728C101.814 71.6665 101.072 72.4054 101.072 73.3168V89.773C101.072 90.6844 101.814 91.4233 102.728 91.4233H119.242C120.157 91.4233 120.898 90.6844 120.898 89.773V73.3168C120.898 72.4054 120.157 71.6665 119.242 71.6665Z" />
                  <path d="M42.7342 71.6665H26.2205C25.3059 71.6665 24.5645 72.4054 24.5645 73.3168V89.773C24.5645 90.6844 25.3059 91.4233 26.2205 91.4233H42.7342C43.6488 91.4233 44.3902 90.6844 44.3902 89.773V73.3168C44.3902 72.4054 43.6488 71.6665 42.7342 71.6665Z" />
                  <path d="M58.832 73.4526V115.209H86.7669V73.4526H58.832ZM68.4184 96.847C68.4184 98.3462 67.2016 99.5587 65.6972 99.5587C64.1928 99.5587 62.976 98.3462 62.976 96.847V91.8109C62.976 90.3117 64.1928 89.0992 65.6972 89.0992C66.4513 89.0992 67.1277 89.4014 67.6214 89.8934C68.1151 90.3853 68.4184 91.0594 68.4184 91.8109V96.847Z" />
                  <path d="M72.9479 55.1832C77.7678 55.1832 81.6751 51.2895 81.6751 46.4864C81.6751 41.6833 77.7678 37.7896 72.9479 37.7896C68.128 37.7896 64.2207 41.6833 64.2207 46.4864C64.2207 51.2895 68.128 55.1832 72.9479 55.1832Z" />
                  <path d="M72.9479 55.1832C77.7678 55.1832 81.6751 51.2895 81.6751 46.4864C81.6751 41.6833 77.7678 37.7896 72.9479 37.7896C68.128 37.7896 64.2207 41.6833 64.2207 46.4864C64.2207 51.2895 68.128 55.1832 72.9479 55.1832Z" />
                  <defs>
                    <pattern
                      id="pattern0_2941_915"
                      patternContentUnits="objectBoundingBox"
                      width="1"
                      height="1"
                    >
                      <use
                        xlinkHref="#image0_2941_915"
                        transform="scale(0.0136986 0.00917431)"
                      />
                    </pattern>
                    <image
                      id="image0_2941_915"
                      width="73"
                      height="109"
                      preserveAspectRatio="none"
                      xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEkAAABtCAYAAADtTkz8AAAACXBIWXMAAAsSAAALEgHS3X78AAABW0lEQVR4nO3awWnDMABAUbl0g4zTpTpKlso4mSE9GUJp6LPdUCL+P8oYrIcwQvZy+vi8jXrY9XJe3v77IV6hkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCg9yM3Xy/n5fvYjD/P715JPwGt44+uvWq7kARhJqjNSDNNXnvqi3sW0E1Is0x6a20BoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKBNSDMe8ktPXUmzoG5G0onPAjTGzpX0G8BMQGMc+Di5Qtyfe8+Gs3boC+4Y88Lc1xYACgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQoJCgkKCQqp/qYvocks3uK6oGEAAAAASUVORK5CYII="
                    />
                  </defs>
                </svg>
              </div>
              <div className="bg-brand-primary rounded-b text-white px-3 py-2.5">
                <span className="text-sm font-medium">Résidentielle</span>
              </div>
            </SelectableArea>
            <SelectableArea
              selected={state.type === 2}
              className="rounded bg-secondary text-brand-primary"
              onClick={() =>
                setState((state) => ({
                  ...state,
                  type: 2,
                  consumption: {
                    ...state.consumption,
                    monthlyConsumptions: {},
                    items: [],
                  },
                }))
              }
            >
              <div className="py-3 px-4 sm:px-6 [&>svg]:w-28 [&>svg]:h-28">
                <svg
                  width="134"
                  height="134"
                  viewBox="0 0 134 134"
                  fill="none"
                  className="[&_path]:fill-brand-primary"
                >
                  <g clipPath="url(#clip0_2905_971)">
                    <path d="M90.6864 70.166C90.12 70.4527 88.9837 70.1625 88.9837 69.4877V52.0825L43.8345 70.2988C43.5828 70.3058 42.922 69.8758 42.922 69.7499V52.3448L0.0419569 69.9212L0 134H88.8788V119.899C88.8543 118.221 88.8473 116.658 88.8788 115.396V112.976C88.8788 112.179 89.5256 111.536 90.3228 111.536H107.169C107.966 111.536 108.613 112.179 108.613 112.976V112.997C108.62 113.056 108.623 113.116 108.627 113.182L108.613 134H134V52.3448L90.6864 70.166ZM81.4699 109.074C81.4699 109.759 80.914 110.315 80.2287 110.315H9.02774C8.34244 110.315 7.78651 109.759 7.78651 109.074L7.78301 81.9803L7.86343 81.8929C8.03825 81.4209 8.49279 81.0818 9.02774 81.0818H80.2287C80.914 81.0818 81.4699 81.6377 81.4699 82.323V109.074ZM129.99 97.5359C129.99 98.0533 129.574 98.4694 129.06 98.4694H114.48C113.962 98.4694 113.546 98.0533 113.546 97.5359V82.9593C113.546 82.4419 113.962 82.0258 114.48 82.0258H129.06C129.574 82.0258 129.99 82.4419 129.99 82.9593V97.5359Z" />
                    <path d="M59.4108 37.4258V61.6349L45.016 67.5229V50.1178C45.016 49.6772 43.642 49.1912 43.1769 49.5828L5.49609 65.1698V37.4258H59.4108Z" />
                    <path d="M108.874 0L112.493 58.4705L96.9688 65.1696L101.021 0H108.874Z" />
                    <path d="M85.5808 5.49658L86.6367 50.5968L79.1719 53.6562L80.0844 5.49658H85.5808Z" />
                    <path d="M127.458 84.5361H116.465V95.5289H127.458V84.5361Z" />
                    <path d="M20.9753 84H12V92.9788H20.9753V84Z" />
                    <path d="M34.9753 84H26V92.9788H34.9753V84Z" />
                    <path d="M48.9788 84H40V92.9788H48.9788V84Z" />
                    <path d="M62.9788 84H54V92.9788H62.9788V84Z" />
                    <path d="M76.9788 84H68V92.9788H76.9788V84Z" />
                    <path d="M20.9753 98H12V106.979H20.9753V98Z" />
                    <path d="M34.9753 98H26V106.979H34.9753V98Z" />
                    <path d="M48.9788 98H40V106.979H48.9788V98Z" />
                    <path d="M62.9788 98H54V106.979H62.9788V98Z" />
                    <path d="M76.9788 98H68V106.979H76.9788V98Z" />
                    <path d="M127.458 84.5361H116.465V95.5289H127.458V84.5361Z" />
                    <path d="M106.518 114.109H91.0781V134H106.518V114.109Z" />
                  </g>
                  <defs>
                    <clipPath id="clip0_2905_971">
                      <rect width="134" height="134" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div className="bg-brand-primary rounded-b text-white px-3 py-2.5">
                <span className="text-sm font-medium">Industrielle</span>
              </div>
            </SelectableArea>
          </div>
        </div>
      </aside>
      <aside className="hidden md:flex items-center justify-center w-full md:w-1/2">
        {/* <img src="/assets/images/simulators/injection/tension.svg" /> */}
        <TensionIllustration />
      </aside>
    </div>
  );
}
