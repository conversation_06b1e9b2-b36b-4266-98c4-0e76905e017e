"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { MoveLeftIcon, MoveRightIcon } from "lucide-react";

export default function Step({ step, state, setState, number }) {
  const enabled = step.isEnabled(state);
  const valid = step.isValid(state);
  const active = state.activeStep === number;

  return (
    <div
      id={`step-${number}`}
      className={cn(
        "flex flex-col xl:flex-row self-stretch",
        active && "flex-1"
      )}
    >
      <button
        disabled={!enabled}
        className={cn(
          "relative xl:rotate-180 text-center rounded-none p-[20px] border-y xl:border-y xl:border-x border-white text-white xl:[writing-mode:vertical-lr] disabled:opacity-90 disabled:cursor-not-allowed",
          active ? "bg-brand-primary" : "bg-brand-ternary"
        )}
        onClick={() => setState((state) => ({ ...state, activeStep: number }))}
      >
        {active && (
          <div className="absolute top-full xl:top-[unset] xl:!bottom-10 translate-x-1/2 xl:translate-x-0 right-1/2 xl:right-full w-0 h-0 border-[10px] xl:border-l-0 border-transparent border-t-brand-primary xl:border-t-transparent  xl:border-r-brand-primary" />
        )}
        {/* <div className="absolute bottom-4 lg:rotate-90  left-10 lg:left-1/2 -translate-x-1/2 rounded-full w-8 h-8 grid place-items-center bg-white">
          <span className="text-brand-primary font-semibold text-sm">
            {number + 1}
          </span>
        </div> */}
        <h2>{step.title}</h2>
      </button>

      {active && (
        <div className="w-full relative">
          <div className="w-full px-3 sm:px-6 py-6">
            <step.component state={state} setState={setState} />
          </div>
          {number < 4 && (
            <div className="flex justify-center gap-2 mb-4">
              <Button
                variant="secondary"
                size="sm"
                onClick={() =>
                  setState((state) => ({ ...state, activeStep: number - 1 }))
                }
                disabled={number === 0}
              >
                <MoveLeftIcon /> Précedent
              </Button>
              <Button
                size="sm"
                variant="brand-ternary"
                onClick={() =>
                  setState((state) => ({ ...state, activeStep: number + 1 }))
                }
                disabled={!valid}
              >
                Suivant <MoveRightIcon />
              </Button>
            </div>
          )}
          <p className="text-center text-xs">
            Cette simulation est optimisée pour une habitation principale en
            Maroc métropolitaine occupée à l'année.
          </p>
        </div>
      )}
    </div>
  );
}
