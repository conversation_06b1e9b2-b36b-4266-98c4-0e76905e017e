import { cn } from "@/lib/utils";
import { CheckIcon } from "lucide-react";

export default function SelectableArea({
  selected,
  className,
  children,
  ...props
}) {
  return (
    <button
      className={cn(
        "relative",
        selected && "ring-2 ring-brand-primary ring-offset-1",
        className
      )}
      {...props}
    >
      {selected && (
        <div className="absolute top-0 right-0 translate-x-1/2 -translate-y-1/2 bg-brand-primary text-white border-2 border-white p-1 rounded-full">
          <CheckIcon size={12} />
        </div>
      )}
      {children}
    </button>
  );
}
