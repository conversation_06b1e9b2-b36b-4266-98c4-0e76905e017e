"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { MinusIcon, PlusIcon, Trash2Icon } from "lucide-react";
import StepQuestion from "./step-question";

export default function EquipmentsInventoryStep({ state, setState }) {
  function handleAddDeviceConsumption(device) {
    setState((state) => ({
      ...state,
      consumption: {
        ...state.consumption,
        items: [
          ...state.consumption.items,
          {
            id: Math.random() * 100,
            device: { ...device },
            qty: 1,
            hoursUsagePerDay: device.defaultHoursUsagePerDay,
          },
        ],
      },
    }));
  }

  const totalConsumptionPerDay = state.consumption.items.reduce(
    (total, consumption) =>
      total +
      consumption.device.whPerHour *
        consumption.hoursUsagePerDay *
        consumption.qty,
    0
  );

  return (
    <div className="flex">
      <aside className="flex items-center justify-center w-full">
        <div className="flex flex-col items-start gap-4 w-full">
          <StepQuestion
            number={3}
            question="Quels sont les équipements électriques que vous utilisez au
              quotidien ?"
          />
          <div className="w-full space-y-4">
            <div className=" rounded">
              <ul className="flex items-stretch border-2 border-brand-primary rounded divide-x divide-brand-primary justify-between overflow-x-auto">
                {equipments[state.type].map((equipment) => (
                  <li
                    key={equipment.label}
                    role="button"
                    className="flex flex-col justify-center text-center items-center min-w-[130px] gap-2 [&>svg]:h-6 [&>svg]:text-brand-primary w-full p-3 bg-secondary hover:bg-secondary/60 transition-colors"
                    onClick={() => handleAddDeviceConsumption(equipment)}
                  >
                    {equipment.icon}
                    <h6 className="font-medium">{equipment.label}</h6>
                  </li>
                ))}
              </ul>
            </div>
            <div className="flex justify-center">
              <Badge>Total = {totalConsumptionPerDay} Wh/j</Badge>
            </div>
            <div>
              <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
                {state.consumption.items.toReversed().map((equipment) => (
                  <EquipmentCard
                    key={equipment.id}
                    equipment={equipment}
                    state={state}
                    setState={setState}
                  />
                ))}
              </div>
              {state.consumption.items?.length === 0 && (
                <div className="flex flex-col gap-2 items-center text-center p-20">
                  <span className="text-sm">Ajoutons des appareils!</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </aside>
    </div>
  );
}

function EquipmentCard({ equipment, state, setState }) {
  function handleDeleteDeviceConsumption() {
    setState((state) => ({
      ...state,
      consumption: {
        ...state.consumption,
        items: state.consumption.items.filter((c) => c.id !== equipment.id),
      },
    }));
  }

  function handleChangeConsumptionQty(qty, force = false) {
    setState((state) => ({
      ...state,
      consumption: {
        ...state.consumption,
        items: state.consumption.items.map((c) => {
          if (c.id !== equipment.id) return c;

          if (c.qty + qty < 1) return c;

          return {
            ...c,
            qty: force ? qty : c.qty + qty,
          };
        }),
      },
    }));
  }

  function handleChangeHoursUsagePerDay(hours, force = false) {
    setState((state) => ({
      ...state,
      consumption: {
        ...state.consumption,
        items: state.consumption.items.map((c) => {
          if (c.id !== equipment.id) return c;

          if (c.hoursUsagePerDay + hours < 1) return c;

          return {
            ...c,
            hoursUsagePerDay: force ? hours : c.hoursUsagePerDay + hours,
          };
        }),
      },
    }));
  }

  function handleChangeWhPerHour(e) {
    const whPerHour = e.target.value;

    setState((state) => ({
      ...state,
      consumption: {
        ...state.consumption,
        items: state.consumption.items.map((c) => {
          if (c.id !== equipment.id) return c;

          if (c.device.whPerHour + whPerHour < 0) return c;

          return {
            ...c,
            device: {
              ...c.device,
              whPerHour: whPerHour,
            },
          };
        }),
      },
    }));
  }

  return (
    <div className="relative p-2.5 bg-secondary rounded">
      <Button
        size="icon"
        variant="danger"
        className="absolute right-0 top-0 -translate-y-1/3 translate-x-1/3 rounded-full h-6 w-6 [&>svg]:!size-3 border-2 border-white"
        onClick={handleDeleteDeviceConsumption}
      >
        <Trash2Icon />
      </Button>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 [&>svg]:h-6 [&>svg]:text-brand-primary">
            {equipment.device.icon}
            <span className="font-medium line-clamp-1">
              {equipment.device.label}
            </span>
          </div>
          <Badge className="flex-shrink-0">
            {equipment.device.whPerHour *
              equipment.hoursUsagePerDay *
              equipment.qty}{" "}
            Wh/j
          </Badge>
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="font-medium">Quantité:</label>
            <div className="flex items-stretch bg-white p-0.5 rounded">
              <Button
                variant="outline"
                className="[&_svg]:size-2 h-6 w-6 p-2"
                onClick={() => handleChangeConsumptionQty(-1)}
              >
                <MinusIcon />
              </Button>
              <input
                type="number"
                value={equipment.qty.toString()}
                onChange={(e) =>
                  handleChangeConsumptionQty(parseInt(+e.target.value), true)
                }
                className="min-w-0 w-8 text-center rounded"
              />
              <Button
                variant="outline"
                className="[&_svg]:size-2 h-6 w-6 p-2"
                onClick={() => handleChangeConsumptionQty(1)}
              >
                <PlusIcon />
              </Button>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <label className="font-medium">Utilisation (h/j):</label>
            <div className="flex items-stretch bg-white p-0.5 rounded">
              <Button
                variant="outline"
                className="[&_svg]:size-2 h-6 w-6 p-2"
                onClick={() => handleChangeHoursUsagePerDay(-1)}
              >
                <MinusIcon />
              </Button>
              <input
                type="number"
                value={equipment.hoursUsagePerDay.toString()}
                onChange={(e) =>
                  handleChangeHoursUsagePerDay(parseInt(+e.target.value), true)
                }
                className="min-w-0 w-8 text-center"
              />
              <Button
                variant="outline"
                className="[&_svg]:size-2 h-6 w-6 p-2"
                onClick={() => handleChangeHoursUsagePerDay(1)}
              >
                <PlusIcon />
              </Button>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-start gap-2">
          <div className="flex items-stretch border-2 border-brand-primary rounded overflow-hidden">
            <input
              value={equipment.device.whPerHour}
              onChange={handleChangeWhPerHour}
              className="w-10 text-center"
            />
            <span className="bg-brand-primary p-1 text-xs font-medium text-white">
              Watt
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

export const equipments = {
  1: [
    {
      icon: (
        <svg width="122" height="122" viewBox="0 0 122 122" fill="none">
          <path
            d="M54.531 121.942C54.191 121.572 53.731 121.442 53.331 121.112C50.681 118.912 45.521 113.762 43.331 111.112C43.001 110.712 42.651 110.342 42.441 109.862L42.271 89.8021C35.011 85.6221 28.911 78.8521 25.841 70.9921C14.761 42.5921 39.251 13.6521 69.051 19.9221C71.021 20.3321 77.821 22.4221 79.051 23.7321C80.481 25.2421 79.621 27.8221 77.491 27.9221C76.331 27.9721 72.511 25.9021 70.951 25.4021C45.841 17.4721 22.401 40.4121 29.171 65.7521C32.171 76.9821 41.731 86.3321 53.101 88.8421V64.5621H48.931C46.261 64.5621 42.741 61.4121 41.971 58.9021C39.951 52.2421 46.011 46.2621 52.611 48.5021C55.041 49.3321 58.091 52.7821 58.091 55.4021V59.5721H63.801V55.4021C63.801 55.2221 64.221 53.8621 64.331 53.5521C67.521 44.3221 82.081 47.4021 80.121 58.1521C79.601 61.0221 75.911 64.5721 72.971 64.5721H68.801V88.8521C88.031 84.2421 98.761 63.8821 91.831 45.2221C91.161 43.4221 88.101 38.7321 91.791 38.1521C94.381 37.7421 95.011 40.2721 95.821 42.1921C103.351 60.1021 96.071 80.3021 79.561 89.9521L79.531 108.972C79.571 109.762 79.141 110.402 78.691 110.992C76.801 113.452 71.041 119.022 68.571 121.112C68.171 121.442 67.711 121.572 67.371 121.942H54.511H54.531ZM53.101 59.5621V55.1621C53.101 55.0721 52.301 53.9121 52.131 53.7521C49.001 50.8521 44.391 55.4621 47.291 58.5921C47.451 58.7621 48.611 59.5621 48.701 59.5621H53.101ZM68.821 59.5621H73.221C73.311 59.5621 74.471 58.7621 74.631 58.5921C77.531 55.4621 72.921 50.8521 69.791 53.7521C69.621 53.9121 68.821 55.0721 68.821 55.1621V59.5621ZM63.821 64.5621H58.111V89.8021H63.821V64.5621ZM74.771 92.1821C68.381 94.5321 61.711 95.2421 54.961 94.2521C52.281 93.8621 49.721 92.9721 47.151 92.1821V103.372H74.771V92.1821ZM74.531 108.132H47.391L56.071 116.952C59.201 117.392 62.541 117.202 65.721 117.052L74.531 108.132Z"
            fill="#223C71"
          />
          <path
            d="M121.908 54.5622V56.2322C121.448 57.0122 120.848 57.7222 119.868 57.8822C118.188 58.1622 112.148 58.0922 110.358 57.8922C107.088 57.5222 107.328 53.4322 109.868 53.1222C111.718 52.8922 118.328 52.8622 120.098 53.1422C120.998 53.2822 121.358 53.9322 121.898 54.5522L121.908 54.5622Z"
            fill="#223C71"
          />
          <path
            d="M28.5191 87.9319C29.3991 87.7819 30.9091 88.6919 31.1491 89.4819C31.5191 90.7119 30.9391 91.6019 30.2391 92.5219C28.3191 95.0419 20.2591 103.102 17.7391 105.022C15.0391 107.082 12.3991 104.972 13.8191 102.052L27.1491 88.7119C27.5091 88.4319 28.0891 88.0019 28.5291 87.9219L28.5191 87.9319Z"
            fill="#223C71"
          />
          <path
            d="M92.8099 87.9321C93.5999 87.8321 94.1799 88.2621 94.7699 88.7221C99.2899 92.2121 103.46 98.2121 107.99 101.932C109.92 104.652 106.44 107.352 104.18 105.032L91.1999 91.8221C89.9599 90.3821 91.0699 88.1621 92.8099 87.9321Z"
            fill="#223C71"
          />
          <path
            d="M15.1889 7.95179C15.9389 7.80179 16.6989 7.84179 17.2789 8.37179L29.5389 20.6318C31.2689 23.1318 28.6289 25.8318 25.7489 23.7018C23.4589 22.0118 16.2389 14.7218 14.4189 12.4218C13.1689 10.8318 12.7189 8.45179 15.1889 7.95179Z"
            fill="#223C71"
          />
          <path
            d="M105.41 7.95204C106.5 7.70204 107.7 8.09204 108.23 9.07204C108.97 10.462 108.46 11.202 107.61 12.292C105.91 14.512 98.38 22.072 96.17 23.702C93.53 25.652 90.92 23.732 92.27 20.762L104.52 8.48204C104.77 8.24204 105.06 8.02204 105.41 7.94204V7.95204Z"
            fill="#223C71"
          />
          <path
            d="M13.2618 53.6919C14.7718 55.1919 13.8218 57.5819 11.7718 57.8719C10.1218 58.1019 3.6318 58.1419 2.0418 57.8719C-0.598204 57.4319 -0.698204 53.5319 1.8018 53.1319C3.5718 52.8519 10.1818 52.8819 12.0318 53.1119C12.4818 53.1619 12.9318 53.3719 13.2518 53.6819L13.2618 53.6919Z"
            fill="#223C71"
          />
          <path
            d="M60.1814 0.101788C61.8314 -0.328212 63.1414 0.641788 63.3414 2.30179C63.5214 3.85179 63.5614 9.88179 63.2814 11.3018C62.7714 13.8418 58.9114 13.6718 58.5614 11.1318C58.3714 9.69179 58.3414 3.19179 58.5814 1.85179C58.7014 1.21179 59.5614 0.271788 60.1714 0.111788L60.1814 0.101788Z"
            fill="#223C71"
          />
          <path
            d="M85.8682 29.4018C88.2782 29.2518 89.3682 32.6618 87.1282 33.9418C83.7082 35.9018 81.9382 29.6318 85.8682 29.4018Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Lampe",
      whPerHour: 9,
      defaultHoursUsagePerDay: 5,
    },
    {
      icon: (
        <svg width="122" height="118" viewBox="0 0 122 118" fill="none">
          <path
            d="M121.791 100.042C121.191 101.962 119.871 103.842 117.991 104.682C117.771 104.782 116.301 105.272 116.211 105.272H96.8306L105.791 108.562C108.581 109.642 111.071 112.752 110.871 115.872C110.741 117.882 108.471 118.502 107.551 117.032C106.711 115.692 107.601 113.772 105.321 112.372L85.7106 105.342L44.3806 105.292C41.9906 105.122 41.8706 101.692 44.3806 101.712L114.561 101.732C116.111 101.612 117.421 101.292 118.011 99.7121C118.431 79.0521 118.301 58.2321 118.071 37.5521C118.011 36.6521 116.531 35.3721 115.741 35.3721H105.871V38.9421H113.361C113.651 38.9421 114.851 39.9721 114.681 40.4721V96.1421C114.721 97.1421 114.121 97.9821 113.111 98.1421L8.19064 97.9821C7.49064 97.6221 7.13064 96.9521 7.17064 96.1521L7.31064 39.9021C7.75064 39.3721 8.18064 38.8321 8.95064 38.9321H73.4306C74.6706 39.1021 75.3406 40.3921 74.8406 41.5621C74.7906 41.6821 73.9606 42.5121 73.8906 42.5121H10.7306V94.5921H111.091V42.5121H105.861V64.0321C105.861 71.6221 97.5006 77.1921 90.3406 75.9821C85.5806 75.1721 79.7006 69.7621 79.7006 64.7421V35.3721H6.09064C5.30064 35.3721 3.82064 36.6621 3.76064 37.5521L3.70064 99.3521C4.20064 101.062 5.37064 101.552 7.05064 101.712L36.8906 101.842C38.2006 102.672 38.2306 104.322 36.8306 105.102L16.3706 112.462C14.1406 113.302 15.2606 118.242 12.5806 117.852C8.93064 117.322 12.1306 110.502 14.3206 109.462L25.0106 105.282H5.62064C3.17064 105.282 -0.299359 101.582 0.0206411 98.9921V37.6321C-0.0693589 35.3821 3.24064 31.8021 5.38064 31.8021H79.7006V24.5521C79.7006 24.4421 80.3206 23.1321 80.4506 22.9221C81.2106 21.7421 82.5306 20.8821 83.9206 20.6821C89.1106 19.9421 95.6606 21.1221 100.991 20.6121C102.571 20.7621 104.101 21.4621 104.951 22.8321C105.171 23.1821 105.861 24.7221 105.861 25.0221V31.8021H116.441C118.501 31.8021 121.431 35.0421 121.791 37.0321V100.052V100.042ZM84.8806 24.2521C83.4506 24.4721 83.3806 24.7921 83.2606 26.2021C83.8706 38.2021 82.4806 50.9421 83.2606 62.8421C84.0406 74.8321 99.4006 75.9521 102.031 64.7121L102.051 25.2621C101.921 24.4521 100.971 24.2621 100.271 24.1821C97.2206 23.8421 87.8206 23.7921 84.8806 24.2421V24.2521Z"
            fill="#223C71"
          />
          <path
            d="M109.349 9.3721C107.789 11.2021 105.469 8.1821 104.119 7.2421C97.5192 2.6421 88.9192 2.4621 82.0892 6.7221C80.5092 7.7121 77.9492 11.1221 76.2292 9.4521C74.2292 7.4921 78.4292 4.7821 79.8992 3.8221C87.3892 -1.0779 97.0292 -1.2679 104.739 3.3021C106.319 4.2421 111.179 7.2221 109.349 9.3721Z"
            fill="#223C71"
          />
          <path
            d="M91.7804 7.13181C95.7104 6.89181 100.13 8.27181 103.12 10.8718C104.18 11.7918 105.2 12.6018 104.5 14.1418C103.44 16.4518 100.88 13.6318 99.8604 12.9518C95.6204 10.1418 90.1204 10.0818 85.8204 12.8118C84.4604 13.6718 83.3104 15.6818 81.5204 14.6418C79.1804 13.2818 82.5904 10.6318 83.7804 9.81181C86.0204 8.28181 89.0804 7.29181 91.8004 7.13181H91.7804Z"
            fill="#223C71"
          />
          <path
            d="M16.1396 45.642C17.0796 45.452 20.4996 45.472 21.3996 45.742C23.4696 46.372 23.1096 49.052 21.0696 49.372C20.1096 49.522 16.4196 49.492 15.5996 49.162C13.9096 48.482 14.2496 46.012 16.1496 45.632L16.1396 45.642Z"
            fill="#223C71"
          />
          <path
            d="M91.5314 27.332C97.1014 26.442 101.461 32.652 98.0114 37.382C93.9714 42.932 85.4914 39.402 86.3814 32.642C86.7014 30.202 89.1014 27.722 91.5314 27.332ZM92.4914 30.882C88.1814 31.362 89.9814 38.172 94.1414 36.122C96.6814 34.872 95.4914 30.542 92.4914 30.882Z"
            fill="#223C71"
          />
          <path
            d="M88.9187 63.4818C90.4787 63.2518 95.1187 63.2218 96.6487 63.4818C99.2287 63.9118 99.1187 66.8618 96.6887 67.2118C95.4087 67.3918 89.5687 67.4418 88.4387 67.1818C86.5087 66.7518 86.4087 63.8618 88.9287 63.4918L88.9187 63.4818Z"
            fill="#223C71"
          />
          <path
            d="M88.1909 57.0518C89.1109 56.8418 96.2009 56.8218 97.1709 57.0018C98.9609 57.3418 98.9709 60.2318 96.9209 60.5518C95.7409 60.7318 89.4909 60.7518 88.4209 60.5118C86.6309 60.1118 86.5509 57.4218 88.1809 57.0418L88.1909 57.0518Z"
            fill="#223C71"
          />
          <path
            d="M88.1905 43.7322C90.5505 43.0222 91.6104 46.4622 89.4604 47.1522C86.6604 48.0422 86.3605 44.2922 88.1905 43.7322Z"
            fill="#223C71"
          />
          <path
            d="M88.2016 50.392C90.7716 49.822 91.4917 53.152 89.4717 53.802C86.8817 54.632 86.2116 50.832 88.2016 50.392Z"
            fill="#223C71"
          />
          <path
            d="M96.0387 50.3918C98.4887 49.6618 99.5187 53.1118 97.3087 53.8018C94.8687 54.5718 94.0487 50.9818 96.0387 50.3918Z"
            fill="#223C71"
          />
          <path
            d="M96.2783 43.7222C98.6283 43.2422 99.2683 46.6322 97.3583 47.2022C94.7083 48.0022 94.0183 44.1922 96.2783 43.7222Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "TV",
      whPerHour: 80,
      defaultHoursUsagePerDay: 2,
    },
    {
      icon: (
        <svg width="70" height="122" viewBox="0 0 70 122" fill="none">
          <path
            d="M61.45 0.00195312C65.65 1.03195 68.89 4.07195 69.54 8.46195V113.252C68.97 118.012 65.17 121.592 60.38 121.942H8.71C4.19 121.342 0.52 117.812 0 113.242L0.11 65.232C1.03 63.532 3.47 64.132 3.58 66.072V112.752C3.95 115.892 6.5 118.172 9.64 118.362H60.13C63.2 118.052 65.58 115.832 65.96 112.752V9.63195C65.88 6.29195 63.18 3.75195 59.9 3.56195H9.65C6.4 3.78195 3.86 6.10195 3.58 9.39195V57.752C3.43 60.132 -0.01 60.272 0.01 57.752L0.04 8.24195C0.8 3.95195 3.97 1.02195 8.11 0.00195312H61.45Z"
            fill="#223C71"
          />
          <path
            d="M11.4411 17.382V94.542H58.1111V35.132C58.1111 33.562 61.5511 32.322 61.9211 35.612V93.962C61.8111 96.412 60.2311 98.152 57.7611 98.372H11.7911C9.32109 98.152 7.75109 96.422 7.62109 93.962L7.65109 17.772C8.02109 15.172 9.69109 13.792 12.2611 13.572C14.8111 13.352 19.7411 13.332 22.2811 13.572C25.7711 13.902 24.7011 17.392 22.9911 17.392H11.4411V17.382Z"
            fill="#223C71"
          />
          <path
            d="M58.1211 17.3817H30.8511C29.0911 17.3817 28.3211 13.9817 31.1111 13.5917C39.5611 14.1017 48.9511 12.8417 57.2911 13.5617C59.7011 13.7717 61.5011 15.0617 61.8611 17.5617C62.1411 19.5317 62.1511 24.7817 61.9211 26.7917C61.5511 30.0817 58.1111 28.8417 58.1111 27.2717V17.3917L58.1211 17.3817Z"
            fill="#223C71"
          />
          <path
            d="M33.2881 100.552C43.8281 98.872 44.3581 114.322 34.6981 114.212C26.2181 114.112 25.7281 101.752 33.2881 100.552ZM33.9981 104.122C30.5581 104.832 30.8381 110.242 34.4281 110.502C39.5081 110.872 38.9081 103.112 33.9981 104.122Z"
            fill="#223C71"
          />
          <path
            d="M26.6103 7.43201C27.8103 7.16201 35.6803 7.18201 37.0203 7.39201C39.3003 7.75201 39.4703 10.792 36.7703 11.162C34.8303 11.432 29.2803 11.392 27.2903 11.162C24.5403 10.832 24.6103 7.87201 26.6003 7.43201H26.6103Z"
            fill="#223C71"
          />
          <path
            d="M41.2795 10.6419C39.2095 8.87191 42.7495 5.95191 44.0795 8.20191C45.2395 10.1519 42.8495 11.9919 41.2795 10.6419Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Téléphone",
      whPerHour: 5,
      defaultHoursUsagePerDay: 2,
    },
    {
      icon: (
        <svg width="62" height="63" viewBox="0 0 62 63" fill="none">
          <path
            d="M39.6088 40.3673H44.4789C46.0628 40.3673 48.1496 42.2703 48.6713 43.7247C49.4054 45.7792 48.2625 48.3959 50.2713 49.9503C52.2802 51.5046 55.2705 50.3503 55.5905 47.7471V31.825C53.458 31.3627 51.6159 29.4813 51.425 27.2511C51.2233 24.8939 51.5621 22.2231 51.4573 19.8334C51.6993 18.9954 52.8476 19.3792 52.9713 19.0062C53.1111 17.9438 52.8476 16.6544 52.982 15.6244C53.1353 14.4377 54.808 14.9513 54.808 15.3703V19.2224H58.3146V15.6325C58.3146 15.0297 59.5624 14.5945 60.0626 15.1811C60.5278 15.7244 60.1272 18.3953 60.2616 19.2252C60.9447 19.1711 61.4664 19.1549 61.7622 19.8712C61.547 23.599 63.1256 29.1731 59.0838 31.2924C58.7368 31.4736 57.6235 31.7547 57.5509 31.971L57.5375 47.885C57.048 52.4697 51.3201 54.3268 48.3163 50.7504C46.3398 48.3986 47.765 46.3982 46.7781 44.2032C46.4877 43.5598 45.1646 42.3325 44.4842 42.3325H39.6142V54.0159C39.6142 54.2592 39.3802 55.2243 39.2861 55.5135C38.6864 57.368 36.7018 58.4925 34.8274 58.5385C34.2842 60.5821 34.6957 62.7664 32.0226 62.9637C28.4218 63.2313 28.723 61.4769 28.0722 58.5952L11.4344 58.5223C10.9772 61.5688 11.0014 63.3313 7.34412 62.9583C4.85395 62.7042 5.29229 60.5227 4.74907 58.6466C4.0472 58.3493 3.31844 58.3817 2.60312 58.0627C1.11601 57.4004 0.0779859 55.7839 0 54.1511V4.26538C0.180174 2.2866 1.55165 0.64573 3.4771 0.194288C13.9944 -0.189573 24.5844 0.132113 35.1286 0.0293898C37.4225 0.096971 39.6115 2.18928 39.6115 4.5303V40.3646L39.6088 40.3673ZM37.6618 21.9581V4.40054C37.6618 3.2003 36.1962 1.92437 35.0049 1.98113L4.48285 1.98654C3.56315 1.89733 1.94965 3.10298 1.94965 4.00857V21.9581H37.6645H37.6618ZM59.869 21.1769H53.3746V27.5079C53.3746 28.0242 54.4557 29.2082 54.9155 29.484C56.3489 30.3436 58.4034 29.957 59.3285 28.5297C59.4602 28.3243 59.869 27.4268 59.869 27.2457V21.1742V21.1769ZM15.5837 23.918H1.94696V27.5728H13.1796C14.0348 27.5728 15.1266 24.61 15.581 23.918H15.5837ZM37.6618 23.918H17.8561C16.6702 25.3426 16.1538 28.2405 14.457 29.1163C14.2849 29.2055 13.4297 29.5326 13.3114 29.5326H1.94696V54.5323C1.94696 55.4379 3.56315 56.6435 4.48016 56.5543L35.0022 56.5597C36.1935 56.6165 37.6591 55.3405 37.6591 54.1403V23.918H37.6618ZM9.47932 58.5114H6.75251L7.14244 60.9903L8.96838 60.9336L9.47932 58.5087V58.5114ZM32.8563 58.5114H30.0004L30.5248 60.9849L32.1463 60.9984L32.3749 60.8308L32.8563 58.5087V58.5114Z"
            fill="#223C71"
          />
          <path
            d="M20.1323 34.6239C20.3206 34.8375 21.3882 33.3805 21.762 33.3156C22.2917 33.2264 22.9533 33.5967 22.9882 34.1752C23.042 35.0862 20.6944 36.8082 20.1323 37.5597V41.1496L23.3082 39.3168C23.8622 38.3652 23.8165 34.6645 25.4945 35.17C26.7907 35.562 25.6048 37.7435 25.7177 37.8868C26.6213 37.5678 27.7884 36.1891 28.6973 37.0461C30.0231 38.295 27.1027 39.087 26.8875 39.5222C26.7692 39.7601 27.0731 39.7169 27.1753 39.752C27.9175 39.9953 29.0362 39.9602 28.9609 41.082C28.8345 42.9527 25.3305 40.9658 24.3624 41.0306L21.173 42.7878L24.3516 44.6233C25.3735 44.5746 28.6436 42.8445 28.9447 44.5151C29.1572 45.6937 27.463 45.7073 26.7584 46.0506C27.4469 46.6615 29.4019 47.0508 28.8668 48.3024C28.1864 49.8946 26.0189 47.5617 25.7204 47.8158C25.9383 48.616 26.6052 50.0325 25.5295 50.4379C23.8515 51.0678 23.8864 47.213 23.2598 46.3074L20.1377 44.5503V48.1402C20.6056 48.7863 22.9587 50.6326 22.9936 51.2841C23.0259 51.8626 22.4746 52.4167 21.8991 52.3843C21.3236 52.3518 20.6702 51.2327 20.1377 50.9489V53.3629C20.1377 54.1576 18.1908 54.1657 18.1908 53.1006V51.0759C17.5211 51.5544 16.7305 52.8952 15.7839 52.1248C14.4878 51.0678 17.6664 49.1025 18.1908 48.2699V44.5503L15.1063 46.2803C14.6975 47.3535 14.6007 48.789 14.1355 49.8081C13.8558 50.419 13.3852 50.7488 12.7479 50.3487C11.8524 49.7892 12.4924 48.5889 12.7371 47.8158C12.4521 47.5806 10.1905 49.8378 9.55852 48.3348C9.0153 47.04 10.9811 46.6858 11.6964 46.0533C10.9165 45.6532 9.44826 45.8289 9.47784 44.6233C9.52625 42.7715 13.084 44.5584 14.079 44.6503L17.2791 42.7905L14.0925 41.0333C12.998 41.1198 11.6157 41.8768 10.5723 41.9362C9.70911 41.9849 9.1659 41.0982 9.68491 40.3792C10.0614 39.8574 11.0268 39.9385 11.5646 39.6574C11.5539 39.0843 9.21161 38.8383 9.5128 37.4678C9.91618 35.6296 12.5408 38.0571 12.7317 37.8949C12.5247 37.192 11.8309 35.6944 12.7613 35.2511C14.5658 34.3942 14.5389 38.4112 15.1359 39.3276L18.1854 41.0279V37.438C17.6717 36.7325 15.4505 35.0997 15.4505 34.305C15.4505 33.6508 16.034 33.1993 16.6875 33.3264C17.1581 33.4183 17.704 34.3996 18.1854 34.6294V32.6046C18.1854 32.2965 18.7286 31.7071 19.0594 31.645C19.3901 31.5828 20.1323 31.9964 20.1323 32.2127V34.6266V34.6239Z"
            fill="#223C71"
          />
          <path
            d="M9.21023 19.8124C6.70124 19.8124 4.66016 17.7606 4.66016 15.2385V8.36686C4.66016 5.84473 6.70124 3.79297 9.21023 3.79297C11.7192 3.79297 13.7603 5.84473 13.7603 8.36686V15.2385C13.7603 17.7606 11.7192 19.8124 9.21023 19.8124ZM9.21023 5.68795C7.73926 5.68795 6.54258 6.89089 6.54258 8.36957V15.2412C6.54258 16.7199 7.73926 17.9228 9.21023 17.9228C10.6812 17.9228 11.8779 16.7199 11.8779 15.2412V8.36957C11.8779 6.89089 10.6812 5.68795 9.21023 5.68795Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Réfrigérateur",
      whPerHour: 20,
      defaultHoursUsagePerDay: 24,
    },
    {
      icon: (
        <svg width="53" height="59" viewBox="0 0 53 59" fill="none">
          <path
            d="M50.2075 0C51.7099 0.452804 52.8786 1.55093 53.0012 3.165L52.9966 33.2595C53.0276 34.3484 51.294 36.1981 50.2649 36.1981H32.4317C32.486 38.2788 32.6117 40.3704 33.0246 42.4111C33.1984 42.5574 36.436 42.274 37.0848 42.4758C37.8205 42.7037 37.822 43.7094 37.1934 44.0944L16.2172 44.1607C15.3155 44.1915 15.0144 42.8639 15.8463 42.5112C16.5152 42.2278 19.787 42.5805 19.9872 42.4126C20.4001 40.3719 20.5258 38.2804 20.5801 36.1996H2.7484C1.72094 36.1996 -0.0127231 34.3484 0.0167662 33.261C0.0912654 23.0021 -0.127576 12.7124 0.128515 2.47194C0.561542 1.16281 1.50985 0.408139 2.80583 0H50.206H50.2075ZM1.76129 3.16963V27.8982H3.96833V5.82176C3.96833 4.99624 4.88405 3.9243 5.76873 3.91968L47.3502 3.93046C48.075 4.02749 48.6834 4.60196 48.896 5.27963C49.1801 6.18986 49.1413 9.70755 49.0497 10.7841C48.9814 11.5912 48.6306 12.3705 47.6528 11.9315C47.5473 11.8838 47.1872 11.4294 47.1872 11.3571V5.6508H5.82771V27.9013H47.1872V14.9317C47.1872 14.8594 47.5473 14.405 47.6528 14.3573C48.2768 14.077 49.045 14.4651 49.045 15.1628V27.9013H51.2521V3.16963C51.2521 2.74454 50.521 1.89438 50.0399 1.83586C34.4075 1.63718 18.7006 1.6957 3.06037 1.80659C2.46127 1.94059 1.86217 2.57667 1.76129 3.16963ZM51.2521 29.6278H1.76129V33.144C1.76129 33.1948 2.19121 33.9279 2.28123 34.0111C2.483 34.1975 3.0433 34.4377 3.32733 34.4716L50.0725 34.3915C50.5552 34.3191 51.2536 33.4505 51.2536 33.0285V29.6278H51.2521ZM31.1543 42.4249L30.5801 36.2505H22.4333L21.859 42.4249H31.1528H31.1543Z"
            fill="#223C71"
          />
          <path
            d="M3.96724 57.2956H49.0439L46.2983 48.7802L16.8028 48.7663C15.676 48.6554 15.8126 47.0983 16.9192 47.0321L46.9331 47.1122C47.5198 47.334 47.8007 47.6374 48.0553 48.1887C49.2441 50.7762 49.7346 54.1553 50.8505 56.8274C51.0973 57.7685 50.4842 58.6155 49.6291 58.9698L3.47213 58.9975C2.63246 58.7033 2.01163 57.9348 2.11717 57.0184C2.84975 54.8376 3.46592 52.6136 4.19849 50.4327C4.62687 49.1606 4.8783 47.2138 6.46606 47.0336C7.93898 46.8673 11.3582 46.8935 12.8621 47.029C13.9765 47.1291 14.1969 48.1518 13.2967 48.7401L6.92392 48.757L6.57937 48.8772L3.96724 57.2941V57.2956Z"
            fill="#223C71"
          />
          <path
            d="M35.187 53.7475C35.9026 53.5889 37.3956 53.5488 37.5214 54.4991C37.6688 55.6049 36.109 55.5387 35.3919 55.4556C34.2682 55.3262 34.2729 53.9508 35.187 53.7491V53.7475Z"
            fill="#223C71"
          />
          <path
            d="M37.2742 50.8767C37.7056 51.3449 37.5303 52.1719 36.8799 52.3152C36.2296 52.4584 34.6962 52.5246 34.541 51.6791C34.2895 50.296 36.8163 50.3807 37.2742 50.8782V50.8767Z"
            fill="#223C71"
          />
          <path
            d="M16.2487 53.7478C16.9642 53.5907 18.4883 53.5445 18.5815 54.4994C18.6886 55.6191 17.1955 55.539 16.4536 55.4543C15.3485 55.328 15.3796 53.9388 16.2487 53.7478Z"
            fill="#223C71"
          />
          <path
            d="M16.2489 50.635C17.0684 50.4533 19.0923 50.5457 18.5196 51.8486C18.2402 52.4832 16.6121 52.4801 16.0797 52.2537C15.3782 51.9565 15.5303 50.7952 16.2489 50.6365V50.635Z"
            fill="#223C71"
          />
          <path
            d="M30.4228 53.7482C31.2547 53.5572 33.2755 53.6296 32.6919 54.9619C32.4188 55.5872 30.7581 55.5995 30.2505 55.3669C29.6344 55.0835 29.7042 53.913 30.4228 53.7482Z"
            fill="#223C71"
          />
          <path
            d="M30.4252 50.6332C30.7279 50.5454 31.999 50.5732 32.2892 50.6964C33.0466 51.0167 32.8495 52.2026 32.1418 52.3366C31.8314 52.3952 30.5245 52.3782 30.256 52.255C29.5995 51.9531 29.7795 50.8211 30.4268 50.6348L30.4252 50.6332Z"
            fill="#223C71"
          />
          <path
            d="M25.6594 53.7459C25.9186 53.6705 27.3077 53.6828 27.5498 53.7829C28.2637 54.0771 28.1504 55.303 27.3759 55.4493C27.0981 55.5017 25.8192 55.4909 25.5802 55.3923C24.9004 55.112 24.9718 53.9461 25.6594 53.7475V53.7459Z"
            fill="#223C71"
          />
          <path
            d="M23.1024 53.9863C23.4066 54.3606 23.3119 55.2339 22.7951 55.4002C21.9849 55.6605 19.6925 55.508 20.4778 54.1388C20.8177 53.5459 22.7578 53.5628 23.1008 53.9863H23.1024Z"
            fill="#223C71"
          />
          <path
            d="M11.6025 50.6354C11.9796 50.5522 13.0987 50.5584 13.423 50.7401C14.106 51.1206 13.8716 52.2125 13.2011 52.3388C12.8876 52.3973 11.5885 52.3804 11.3153 52.2572C10.6805 51.9692 10.8156 50.8125 11.6025 50.637V50.6354Z"
            fill="#223C71"
          />
          <path
            d="M21.0138 50.6348C21.8737 50.4454 24.014 50.6441 23.1262 52.0395C22.8748 52.4353 21.1861 52.4461 20.8167 52.2797C20.1509 51.9825 20.2083 50.812 21.0138 50.6348Z"
            fill="#223C71"
          />
          <path
            d="M25.775 50.6358C26.6395 50.4433 28.5144 50.5572 27.9541 51.8771C27.7027 52.4686 26.0513 52.4747 25.5779 52.2807C24.8748 51.9911 24.9354 50.8221 25.7735 50.6358H25.775Z"
            fill="#223C71"
          />
          <path
            d="M39.8341 50.6348C40.694 50.4454 42.8343 50.6441 41.9465 52.0395C41.6951 52.4353 40.0064 52.4461 39.637 52.2797C38.9712 51.9825 39.0286 50.812 39.8341 50.6348Z"
            fill="#223C71"
          />
          <path
            d="M11.0912 55.1886C10.8863 54.936 10.905 54.4709 10.9903 54.1736C11.2216 53.3666 14.6299 53.4436 13.7142 55.0361C13.3743 55.6291 11.4342 55.6121 11.0912 55.1886Z"
            fill="#223C71"
          />
          <path
            d="M41.9234 53.9863C42.1283 54.2389 42.1096 54.704 42.0243 55.0013C41.793 55.8083 38.3847 55.7313 39.3004 54.1388C39.6403 53.5459 41.5804 53.5628 41.9234 53.9863Z"
            fill="#223C71"
          />
          <path
            d="M21.8329 10.6253C22.609 10.4974 23.0684 11.42 22.6804 12.053L13.9034 22.2503C13.4471 22.7016 13.1243 23.196 12.4538 22.7616C11.7259 22.2904 11.9882 21.779 12.401 21.2215C14.9976 17.7146 18.5953 14.4941 21.2307 10.961C21.3735 10.8332 21.6498 10.6561 21.8314 10.6253H21.8329Z"
            fill="#223C71"
          />
          <path
            d="M15.0887 12.9351C16.0246 12.7288 16.5399 13.5327 16.0308 14.3351C15.8275 14.6555 13.7074 17.0011 13.4373 17.1782C12.6846 17.6757 11.7642 16.9857 12.073 16.1741C12.2003 15.8383 13.9914 13.8623 14.3639 13.488C14.5641 13.2878 14.8 12.9983 15.0887 12.9351Z"
            fill="#223C71"
          />
          <path
            d="M24.7335 31.1543C25.3435 31.0449 27.6716 31.0449 28.28 31.1543C29.4952 31.3714 29.3773 32.7961 28.3001 32.9655C27.7709 33.0487 25.0967 33.0548 24.614 32.947C23.6129 32.7237 23.5741 31.3607 24.7335 31.1543Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "PC",
      whPerHour: 50,
      defaultHoursUsagePerDay: 3,
    },
    {
      icon: (
        <svg width="122" height="122" viewBox="0 0 122 122" fill="none">
          <path
            d="M65.4196 0.00195312C67.2996 0.431953 69.2696 0.461953 71.1796 0.781953C97.0596 5.11195 117.83 26.442 121.29 52.502C121.47 53.842 121.43 55.352 121.8 56.632C121.67 59.442 121.97 62.402 121.8 65.202C119.11 108.882 70.8896 135.332 32.2396 114.922L7.19956 121.312C4.94956 121.352 4.76956 120.112 5.11956 118.262C6.50956 110.932 9.42956 103.372 10.9196 96.002C-5.56044 72.162 -3.22044 39.812 16.7696 18.802C17.8996 17.612 19.5196 15.412 21.3096 16.412C24.0696 17.972 19.3196 21.342 18.1996 22.602C4.00956 38.652 -0.240437 60.732 7.19956 80.972C9.12956 86.212 11.8996 90.592 14.7496 95.332L9.27956 116.842L32.4696 111.112C33.6396 111.092 36.7596 112.972 38.1396 113.552C82.3696 132.092 128.44 92.532 116.25 46.112C106.77 9.98195 65.0496 -7.44805 32.4396 11.142C31.2196 11.842 27.6496 14.562 26.5896 14.462C25.0396 14.312 24.4396 12.422 25.4796 11.312C26.1896 10.552 29.4496 8.67195 30.5396 8.04195C38.1896 3.60195 47.7996 0.541953 56.6196 0.00195312H65.4196Z"
            fill="#223C71"
          />
          <path
            d="M57.5203 12.442C87.7603 10.442 112.34 36.132 109.18 66.272C109.02 67.772 108.22 73.042 107.49 74.102C106.58 75.422 104.2 74.732 104.18 72.952C113.38 39.262 82.3703 8.12198 48.5303 17.732C20.3103 25.742 7.1003 58.962 22.5103 84.232C40.1003 113.072 81.6203 112.922 99.3003 84.232C100.23 82.732 101.52 78.832 103.34 79.022C106.39 79.332 104.13 83.062 103.37 84.502C88.1303 113.382 47.3803 117.882 25.3403 94.022C-2.3097 64.072 17.0803 15.112 57.5203 12.442Z"
            fill="#223C71"
          />
          <path
            d="M65.8098 80.3519C61.7298 84.3419 55.0098 81.9719 53.9998 76.5119C54.4098 70.6119 53.4598 63.9919 53.9998 58.1819C54.1698 56.3319 55.1698 54.5619 56.6498 53.4519C60.2998 50.7219 64.8398 53.3919 68.1498 48.0519C73.7598 39.0019 61.7198 30.0419 54.5998 37.3519C51.4998 40.5419 53.1598 44.3119 51.0298 47.3419C47.3198 52.6219 38.9698 50.5019 38.3198 44.1419C37.4398 35.5019 43.8098 26.2619 51.4798 22.8019C69.0998 14.8619 87.6898 30.9019 82.6798 49.4919C82.2298 51.1819 80.9498 55.7719 78.4998 53.6719C77.0098 52.3919 78.8898 49.8019 79.3098 48.0219C84.7798 24.9819 52.3098 15.0419 43.5098 35.7619C42.3398 38.5219 40.0998 46.1419 44.8598 46.6419C48.8098 47.0519 48.3398 43.6619 48.8598 41.1119C51.5698 27.7819 70.2798 27.9019 72.9498 40.9119C74.2398 47.2019 70.2698 53.7619 63.9798 55.2919C61.4098 55.9119 58.4698 55.0919 57.6098 58.4319C58.0898 63.8219 56.8998 70.5319 57.6098 75.7719C58.1198 79.5419 63.5898 79.5419 64.1898 75.9719C64.8198 72.2519 63.7998 67.2319 64.2398 63.4119C64.4698 61.4419 67.7798 61.1819 69.4898 60.3319C70.7398 59.7119 73.2698 57.4419 73.9898 57.2119C75.5398 56.7119 76.6898 58.0019 76.3298 59.5619C75.9198 61.3519 69.6998 64.2619 67.9298 64.9619C67.0798 69.5519 69.4498 76.8219 65.8298 80.3519H65.8098Z"
            fill="#223C71"
          />
          <path
            d="M59.8912 87.1618C69.5512 85.9018 70.6112 100.742 61.2512 101.142C52.4712 101.522 51.3512 88.2818 59.8912 87.1618ZM60.8212 90.7518C56.3012 90.8818 56.4612 98.1418 61.7212 97.3218C65.3412 96.7518 64.9612 90.6318 60.8212 90.7518Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Autre",
      whPerHour: 75,
      defaultHoursUsagePerDay: 1,
    },
  ],
  2: [
    {
      icon: (
        <svg width="46" height="46" viewBox="0 0 46 46" fill="none">
          <path
            d="M37.0972 5.93891H40.0632V10.3806H41.5469V13.3412H44.2813C44.8844 13.3412 46.0362 14.5626 45.9965 15.2376L46.0002 40.781C45.7687 43.8901 42.1611 42.7392 40.0632 42.9506C40.2626 44.3561 39.5926 45.7579 38.0694 45.9112C36.8656 46.0324 34.3665 46.0237 33.1541 45.9162C31.663 45.7839 30.9361 44.3277 31.1652 42.9518H14.8489C15.0273 44.523 14.3474 45.7926 12.6743 45.9174C11.4173 46.0101 9.18191 46.0274 7.93853 45.9174C6.44747 45.7851 5.72052 44.3289 5.94962 42.953H1.73155C1.08386 42.953 -0.137227 41.6167 0.0126218 40.875L0.0163371 10.7972C-0.0232925 10.121 1.12844 8.90083 1.73155 8.90083H4.46599V5.94015H13.3653V1.72844C13.3653 1.08191 14.704 -0.13698 15.4471 0.0125991L35.1962 0.0163077C35.8737 -0.0232505 37.096 1.12641 37.096 1.72844V5.93891H37.0972ZM14.8489 2.09806V5.93767H20.7822V8.89836H26.4839C27.0127 8.89836 28.1991 10.0826 28.1991 10.6105V13.34H31.1652V10.3793H32.6488V5.93767H35.6148V2.09806C35.6148 1.79025 35.0575 1.43546 34.7393 1.49109L15.6366 1.49356C15.296 1.50716 14.9109 1.75934 14.8502 2.09806H14.8489ZM19.2986 7.41987H5.94962V8.90083H19.2986V7.41987ZM38.5796 7.41987H34.13V10.3806H38.5796V7.41987ZM1.49997 10.9813V31.1078H26.7143V10.9813C26.7143 10.6735 26.157 10.3187 25.8387 10.3744H2.37553C2.00401 10.3571 1.56437 10.6093 1.49997 10.9813ZM40.0632 11.8615H32.6476V13.3425H40.0632V11.8615ZM43.911 14.8222H28.1979V31.1078H44.5129V15.423C44.5129 15.2252 44.1116 14.8568 43.9098 14.821L43.911 14.8222ZM26.7143 32.5888H1.49997V34.0697H26.7143V32.5888ZM44.5129 32.5888H28.1979V34.0697H44.5129V32.5888ZM26.7143 35.5495H1.49997V40.87C1.49997 41.0493 1.92351 41.4721 2.10308 41.4721H26.7155V35.5495H26.7143ZM44.5129 35.5495H28.1979V41.4721H43.911C44.0906 41.4721 44.5141 41.0493 44.5141 40.87V35.5495H44.5129ZM13.3653 42.9518H7.43202C7.41344 43.5303 7.34657 44.2832 8.05618 44.4105C8.87726 44.5576 11.5944 44.5193 12.4885 44.4377C13.4421 44.3512 13.3876 43.7739 13.3641 42.9518H13.3653ZM38.5796 42.9518H32.6463C32.6277 43.5303 32.5609 44.2832 33.2705 44.4105C34.0916 44.5576 36.8087 44.5193 37.7028 44.4377C38.6564 44.3512 38.6019 43.7739 38.5784 42.9518H38.5796Z"
            fill="#223C71"
          />
          <path
            d="M25.2327 11.8613V29.6279H2.98438V11.8613H25.2327ZM5.9504 13.3411H4.46677V28.1469H5.9504V22.2243H13.3661V19.2637H9.14801C8.97834 19.2637 8.30093 18.8372 8.14365 18.6925C7.37087 17.9854 7.38078 17.1597 7.43898 16.1744C6.21047 15.6836 5.79436 14.6131 5.9504 13.3411ZM20.783 13.3411H7.43403C7.4105 13.9777 7.35229 14.7058 8.13498 14.8158C11.9221 14.5674 16.1748 15.1657 19.9062 14.827C20.8598 14.7404 20.8053 14.1631 20.7818 13.3411H20.783ZM23.749 13.3411H22.2654C22.4214 14.6131 22.0053 15.6836 20.7768 16.1744C20.866 17.2684 20.757 18.3118 19.7539 18.9299C19.604 19.0226 19.1111 19.2624 18.9749 19.2624H14.8497V22.2231H22.2654V28.1457H23.749V13.3398V13.3411ZM19.2994 16.303H8.91642C8.89289 16.9396 8.83469 17.6677 9.61737 17.7778C12.4496 17.5936 15.6312 18.0423 18.4226 17.7889C19.3761 17.7023 19.3217 17.125 19.2981 16.303H19.2994ZM20.783 23.7053H7.43403V28.1469H20.783V23.7053Z"
            fill="#223C71"
          />
          <path
            d="M43.0287 16.3027V26.667H29.6797V16.3027H43.0287ZM41.5463 17.7825H31.1633V19.2634H33.0643L34.1764 21.3909L36.309 17.8752L38.2558 20.7431H41.5463V17.7825ZM37.374 22.2241L36.4006 20.7456L34.0835 24.4431L32.2308 20.8346L31.1633 20.7431V25.1848H41.5463V22.2241H37.3752H37.374Z"
            fill="#223C71"
          />
          <path
            d="M31.1633 28.1465H29.6797V29.6274H31.1633V28.1465Z"
            fill="#223C71"
          />
          <path
            d="M34.1321 28.1465H32.6484V29.6274H34.1321V28.1465Z"
            fill="#223C71"
          />
          <path
            d="M37.0969 28.1465H35.6133V29.6274H37.0969V28.1465Z"
            fill="#223C71"
          />
          <path
            d="M40.0618 28.1465H38.5781V29.6274H40.0618V28.1465Z"
            fill="#223C71"
          />
          <path
            d="M43.0305 28.1465H41.5469V29.6274H43.0305V28.1465Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Machines professionnelles",
      whPerHour: 9,
      defaultHoursUsagePerDay: 5,
    },
    {
      icon: (
        <svg width="51" height="50" viewBox="0 0 51 50" fill="none">
          <path
            d="M0.651289 22.2021C0.396826 22.1481 0.114852 21.8146 0.0804655 21.55C-0.0268218 20.7536 -0.0268218 17.9929 0.0804655 17.1951C0.134109 16.7968 0.468351 16.5741 0.856236 16.5417L7.91244 16.5444C7.60158 13.8242 9.18614 13.0426 11.7115 13.3004C11.8037 12.8954 11.9247 12.4837 12.0843 12.1003C12.2535 11.6926 12.7294 10.9164 12.8146 10.5829C12.8518 10.4398 12.8394 10.4425 12.761 10.3278C12.4832 9.92145 11.7762 9.49621 11.4722 9.06017C10.9261 8.27854 10.9866 7.38351 11.5657 6.63968C12.032 6.0403 14.0966 4.0086 14.7059 3.55771C15.3153 3.10682 16.2451 3.03528 16.9425 3.38897C17.3922 3.61576 18.2423 4.79428 18.5724 4.82668L21.4884 3.69811C21.4403 2.0174 21.3371 0.230048 23.5062 0.0707524C24.5709 -0.00754561 26.6974 -0.0399448 27.7317 0.0748022C29.6973 0.292147 29.5996 2.2118 29.5473 3.69946C30.3589 3.73726 31.6573 4.70653 32.2873 4.80913C32.8031 4.89283 33.4716 3.80881 33.9076 3.51046C34.5871 3.04743 35.5981 3.04877 36.2776 3.51046C36.7837 3.8547 39.0684 6.12535 39.4714 6.64103C40.7369 8.26774 39.2926 9.35446 38.1991 10.4466L39.3256 13.3018C41.8551 13.0372 43.4232 13.8377 43.1247 16.5457L50.1809 16.543C50.3996 16.5417 50.7462 16.7118 50.8549 16.8927C51.2166 17.4934 50.7834 20.7414 50.9484 21.6459C50.9663 21.8214 50.4821 22.2048 50.3845 22.2048H42.9198V27.9651H46.06C46.3447 27.9651 46.5799 28.4781 46.617 28.7319C46.7216 29.4581 46.7119 31.5155 46.6267 32.2593C46.6033 32.4591 46.5551 32.7116 46.4245 32.8695C46.2374 33.0936 45.7271 33.0652 45.8041 33.2691C47.9953 35.9258 49.6981 38.7715 48.5276 42.3112C46.0723 49.7333 33.9956 47.3276 35.6572 38.4705C36.0575 36.3335 37.4426 34.9187 38.5952 33.1692C38.6145 33.1111 37.8621 33.1422 37.77 32.3646C37.6847 31.6464 37.6696 29.3164 37.7782 28.6293C37.814 28.4025 38.1235 27.9664 38.338 27.9664H41.4782V22.2061H26.2407V27.9664H29.3809C29.6656 27.9664 29.9008 28.4794 29.938 28.7332C30.0425 29.4595 30.0329 31.5168 29.9476 32.2607C29.9242 32.4605 29.8761 32.7129 29.7454 32.8708C29.5583 33.0949 29.048 33.0666 29.1251 33.2704C29.3053 33.7456 30.4167 34.939 30.7743 35.4925C32.7907 38.6068 32.8884 42.677 29.8458 45.1974C25.718 48.6155 19.2601 46.0087 18.8296 40.7479C18.5793 37.6794 20.0909 35.5465 21.9162 33.2704C21.9932 33.0666 21.4843 33.0963 21.2959 32.8708C21.1638 32.7129 21.1157 32.4618 21.0937 32.2607C21.0111 31.5425 20.9905 29.3123 21.0992 28.6293C21.1349 28.4025 21.4444 27.9664 21.659 27.9664H24.7992V22.2061H9.56164V27.9664H12.7019C12.9164 27.9664 13.2259 28.4025 13.2617 28.6293C13.3703 29.3164 13.3552 31.6464 13.2699 32.3646C13.1778 33.1435 12.4254 33.1111 12.4446 33.1692C13.5973 34.9187 14.9824 36.3335 15.3827 38.4705C17.0442 47.3303 4.95654 49.7292 2.51231 42.3112C1.34453 38.7661 3.04187 35.9299 5.23576 33.2691C5.31279 33.0652 4.80386 33.0949 4.61542 32.8695C4.48337 32.7116 4.43523 32.4605 4.41322 32.2593C4.32794 31.5155 4.31832 29.4581 4.42285 28.7319C4.45999 28.4781 4.6952 27.9651 4.97992 27.9651H8.12014V22.2048H0.651289V22.2021ZM41.5773 16.543C41.5319 16.1731 41.7176 15.0743 41.3723 14.8745C40.9019 14.6018 39.0382 15.0824 38.3917 14.6679C37.8841 13.866 37.6751 12.8347 37.2707 11.9275C37.0396 11.4078 36.4729 10.7665 36.5224 10.2252C36.5843 9.55696 38.642 8.45403 38.4453 7.74665C38.2899 7.18912 35.8869 5.36531 35.4564 4.71463C35.367 4.63498 35.2583 4.61473 35.1414 4.60798C34.378 4.56614 33.3313 6.63428 32.4881 6.51953C32.1442 6.47229 30.8444 5.67311 30.352 5.48006C29.9242 5.31267 28.502 4.98732 28.2902 4.77538C27.6093 4.095 28.7867 1.64076 27.4209 1.48552C26.5667 1.38832 24.5805 1.40452 23.7071 1.48282C22.0991 1.62726 23.5599 4.28939 22.6259 4.86718C22.2779 5.08317 21.0139 5.35992 20.4719 5.58266C20.0579 5.75275 18.7498 6.52088 18.4637 6.52898C17.7347 6.54788 16.8104 4.92793 16.2465 4.67278C16.0745 4.59448 15.8833 4.58639 15.7032 4.64038C15.5078 4.69843 13.0278 7.14862 12.7968 7.44831C12.7074 7.56441 12.5822 7.67375 12.5574 7.8263C12.4625 8.41893 14.2094 9.47596 14.4432 9.97814C14.5505 10.209 14.5134 10.4533 14.4487 10.6882C14.3304 11.1175 13.6496 12.13 13.4185 12.7078C13.2465 13.1357 12.9921 14.3385 12.7528 14.5802C12.1902 15.1499 10.1724 14.5788 9.65792 14.8772C9.31268 15.077 9.49837 16.1758 9.45298 16.5457H16.4033L16.5408 16.377C16.8063 12.8401 19.615 9.65415 23.0688 8.73753C28.4979 7.29846 33.9434 10.9029 34.4894 16.377L34.627 16.5457H41.5773V16.543ZM33.0314 16.543C31.9627 7.694 19.069 7.68995 17.9988 16.543H33.0314ZM49.5055 18.059H1.52609V20.7873H49.5055V18.059ZM11.7198 29.3758H5.95376V31.7004H11.7198V29.3758ZM28.3988 29.3758H22.6328V31.7004H28.3988V29.3758ZM45.0779 29.3758H39.3119V31.7004H45.0779V29.3758ZM6.94548 33.1287C6.6525 33.5904 6.4283 34.0872 6.12157 34.5435C4.60579 36.7871 3.11477 38.1695 3.73374 41.2055C5.03081 47.5733 15.2355 46.1653 13.9577 38.8498C13.5547 36.5441 11.7294 35.2009 10.7941 33.1638L6.94548 33.1287ZM23.6245 33.1287C23.5461 33.1557 23.0565 34.156 22.9217 34.3585C21.4059 36.6535 19.7416 38.1128 20.3922 41.2244C21.7112 47.545 31.9132 46.1774 30.6381 38.8485C30.2337 36.5212 28.3988 35.2184 27.4759 33.1597L23.6245 33.1273V33.1287ZM40.3063 33.1314C39.9157 33.7605 39.5512 34.3963 39.1289 35.0065C37.6902 37.0868 36.4866 38.5393 37.0932 41.3054C38.488 47.6611 48.9691 46.0776 47.2099 38.4503C46.7271 36.3592 44.9706 35.0767 44.1563 33.1611L40.3077 33.1314H40.3063Z"
            fill="#223C71"
          />
          <path
            d="M38.046 46.0712C38.6017 45.9065 39.1629 46.2967 39.0639 46.8636C39.0254 47.0837 38.3088 48.2798 38.1313 48.4782C37.5481 49.1248 36.6871 48.7171 36.8631 47.8424C36.9195 47.5602 37.782 46.1495 38.0474 46.0712H38.046Z"
            fill="#223C71"
          />
          <path
            d="M45.7674 46.0708C46.0026 46.002 46.2667 46.0209 46.4744 46.1437C46.6656 46.2571 47.5142 47.6989 47.5445 47.9284C47.627 48.5575 46.9696 49.0394 46.4084 48.6331C46.2309 48.5048 45.4386 47.2575 45.3602 47.032C45.2378 46.6837 45.3712 46.1869 45.7674 46.0708Z"
            fill="#223C71"
          />
          <path
            d="M4.68696 46.0706C5.5205 45.8249 5.88637 46.508 5.55901 47.2288C5.32655 47.7418 4.68971 48.8866 4.05286 48.7786C2.72277 48.5518 4.28944 46.188 4.68696 46.0706Z"
            fill="#223C71"
          />
          <path
            d="M20.2968 48.5455C19.8883 48.0487 20.6517 46.8553 21.0052 46.4206C21.5705 45.7227 22.4756 46.094 22.4096 46.7824C22.3766 47.1213 21.4123 48.7183 21.0327 48.779C20.8126 48.8155 20.4358 48.7142 20.2968 48.5468V48.5455Z"
            fill="#223C71"
          />
          <path
            d="M14.0567 48.5459C13.9192 48.7133 13.5409 48.8146 13.3209 48.7781C12.9495 48.7174 12.0279 47.2 11.9688 46.8625C11.845 46.1565 12.7294 45.6935 13.3071 46.3603C13.6716 46.7815 14.4652 48.0505 14.0567 48.5459Z"
            fill="#223C71"
          />
          <path
            d="M29.0889 46.0708C29.3241 46.002 29.5882 46.0209 29.7959 46.1437C29.9871 46.2571 30.8357 47.6989 30.866 47.9284C30.9485 48.5575 30.2911 49.0394 29.7299 48.6331C29.43 48.4157 28.5442 46.9038 28.6446 46.5528C28.6859 46.4097 28.9362 46.1154 29.0875 46.0708H29.0889Z"
            fill="#223C71"
          />
          <path
            d="M8.60316 47.0793C9.05844 46.9807 9.47383 47.1319 9.55086 47.622C9.6045 47.9554 9.60863 49.2028 9.53573 49.5133C9.37067 50.218 8.25103 50.1113 8.11899 49.4323C8.0172 48.9045 7.95255 47.2197 8.60316 47.0793Z"
            fill="#223C71"
          />
          <path
            d="M41.9625 47.0793C42.4178 46.9807 42.8332 47.1319 42.9102 47.622C42.9556 47.9095 42.9666 49.2757 42.8951 49.5146C42.7149 50.1208 41.609 50.2234 41.4811 49.4282C41.3931 48.8775 41.2941 47.2237 41.9612 47.0793H41.9625Z"
            fill="#223C71"
          />
          <path
            d="M25.2842 47.0793C25.7395 46.9807 26.1549 47.1319 26.2319 47.622C26.2773 47.9095 26.2883 49.2757 26.2168 49.5146C26.0215 50.1748 24.9348 50.1343 24.7987 49.4323C24.6969 48.9045 24.6322 47.2197 25.2828 47.0793H25.2842Z"
            fill="#223C71"
          />
          <path
            d="M28.6803 39.5025C29.8288 39.1664 29.7752 40.3908 29.6088 41.1198C29.3295 42.3455 28.2952 43.5578 27.0916 44.0046C26.4768 44.2328 24.9005 44.658 24.7987 43.6644C24.6969 42.6709 25.972 42.8815 26.6199 42.6331C27.2677 42.3847 27.8578 41.7529 28.0682 41.1252C28.225 40.6594 28.1329 39.6631 28.6789 39.5025H28.6803Z"
            fill="#223C71"
          />
          <path
            d="M12.1015 39.5021C13.4743 39.2064 12.997 41.0802 12.6944 41.8038C12.2955 42.7555 11.3822 43.6316 10.407 44.0042C9.79074 44.2404 8.21719 44.663 8.1154 43.6654C8.01362 42.6677 9.22542 42.8959 9.86364 42.6623C10.5349 42.4166 11.1566 41.7943 11.3863 41.1247C11.5734 40.578 11.4193 39.6479 12.1015 39.5007V39.5021Z"
            fill="#223C71"
          />
          <path
            d="M45.3561 39.5025C46.5046 39.1664 46.451 40.3908 46.2845 41.1198C46.0053 42.3455 44.9709 43.5578 43.7674 44.0046C43.2076 44.2125 41.6395 44.627 41.4758 43.7711C41.2626 42.6601 42.6024 42.899 43.297 42.6331C43.9228 42.3928 44.5349 41.7529 44.7454 41.1252C44.9022 40.6594 44.81 39.6631 45.3561 39.5025Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Lampes industrielles",
      whPerHour: 80,
      defaultHoursUsagePerDay: 2,
    },
    {
      icon: (
        <svg width="41" height="41" viewBox="0 0 41 41" fill="none">
          <path
            d="M40.9996 1.04457V9.37209C40.6578 10.1 40.1919 10.3686 39.3984 10.4589L39.4009 12.015C40.1696 11.9155 40.7116 12.3906 40.9996 13.0554V36.3559C40.6644 37.1526 40.0339 37.5182 39.1617 37.3972V40.0799C39.1617 40.4272 38.7356 40.8517 38.4426 41.0009H34.0471C33.8857 40.894 33.4074 40.5068 33.4074 40.3203V37.398H25.9353C25.7904 37.398 25.5563 36.9213 25.5761 36.7248C25.5935 36.5474 25.8591 36.1967 26.0155 36.1967H39.6806L39.8005 36.0765V34.1946H1.28209V36.0765L1.40207 36.1967H23.538C23.855 36.1967 24.0751 36.7522 23.9361 37.0763C23.9195 37.1153 23.6407 37.398 23.6183 37.398H7.67521V40.3203C7.67521 40.5524 7.20603 40.8625 7.03557 41.0009H2.64081C2.34788 40.8517 1.92173 40.4272 1.92173 40.0799V37.3972C1.03715 37.5331 0.210498 37.0755 0.083066 36.1561L0.100443 30.4881C0.26263 29.7619 1.28209 30.0545 1.28209 30.4707V32.9926H39.8005V13.295C39.6541 13.2577 39.5184 13.2229 39.3645 13.2113C37.1816 13.0388 34.7761 13.3497 32.57 13.2138C31.8683 13.0795 32.1389 12.0142 32.5684 12.0142H38.2027V10.4125H2.88078V12.0142H30.0917C30.5162 12.0142 30.8315 13.111 30.0164 13.2196L1.71817 13.2113L1.28209 13.295V28.0682C1.28209 28.0906 0.999916 28.3699 0.961024 28.3865C0.569625 28.5548 0.13437 28.3301 0.0789285 27.9123L0.0905133 13.1823C0.230358 12.4395 0.920478 11.9122 1.68259 12.0142V10.4133C0.873311 10.4349 0.231185 9.98308 0.0822385 9.17395C0.284144 6.64958 -0.180901 3.72065 0.0838934 1.24436C0.153402 0.591092 0.731812 0.0215546 1.39876 0H39.6847C40.3318 0.0149224 40.7555 0.501558 41.0004 1.04457H40.9996ZM2.88078 1.20457H1.40207L1.28209 1.32478V9.01146L1.40207 9.13167H39.6814L39.8014 9.01146V1.32478L39.6814 1.20457H38.2027V4.84812C38.2027 4.97496 37.8493 5.30408 37.6789 5.28335L33.256 5.29579C32.9159 6.14222 32.7736 7.69415 31.683 7.84337C30.0354 7.74555 28.1959 7.99757 26.5724 7.85332C25.2369 7.73477 25.3304 6.22761 24.8082 5.28252L16.2355 5.29745C15.7936 6.26326 15.8094 7.73809 14.5119 7.85332C13.2144 7.96855 10.9247 7.94949 9.63055 7.85332C8.22714 7.74886 8.26521 6.34865 7.8283 5.29579L3.40541 5.28335C3.23495 5.30408 2.88161 4.97496 2.88161 4.84812V1.20457H2.88078ZM19.9021 1.20457H4.07898V3.96687L4.19896 4.08708H10.8321C11.3244 4.08708 11.3244 5.28833 10.8321 5.28833H9.11421L9.5627 6.57414L14.4184 6.55839L14.9488 5.28833H13.2309C12.7038 5.28833 12.7038 4.08708 13.2309 4.08708H19.9037V1.20457H19.9021ZM37.0045 1.20457H21.1814V4.08708H36.8845L37.0045 3.96687V1.20457ZM31.9693 5.28833H26.1355C26.2332 5.51051 26.5046 6.50367 26.6659 6.55839L31.5216 6.57414L31.9701 5.28833H31.9693ZM6.47702 37.3963H3.20019V39.7988H6.47702V37.3963ZM37.8833 37.3963H34.6064V39.7988H37.8833V37.3963Z"
            fill="#223C71"
          />
          <path
            d="M21.1794 18.1813C22.8642 17.9367 22.865 19.7059 21.1794 19.4613L21.1894 20.253C21.7967 20.3591 22.2742 20.7438 22.7368 21.1194L23.4128 20.7032C23.1331 20.3169 22.7326 19.6943 23.2614 19.3453C24.0707 18.8105 24.304 20.1345 24.563 20.0939C24.7086 19.9173 25.0984 19.8493 25.2118 19.6976C25.5188 19.2839 25.3665 17.9293 26.1766 17.9484C27.2805 17.9741 26.4397 19.6056 26.5341 19.7009C27.0752 19.7332 28.151 19.9737 27.8332 20.7206C27.4873 21.5339 26.376 20.7148 25.8663 20.7645C25.6387 20.7869 25.3417 21.1036 25.0967 21.1442C25.0123 21.2644 25.7182 21.8597 25.4195 22.3878C25.009 23.1132 24.146 22.382 24.0558 21.7826C23.8878 21.9152 23.3483 22.0959 23.3425 22.2957C23.3359 22.5071 23.5038 22.8379 23.5038 23.104C23.5038 23.3702 23.3756 23.7001 23.3524 23.9778L24.0558 24.4247C24.1724 23.8369 25.0065 23.0667 25.4054 23.8344C25.7099 24.4214 25.0082 24.874 25.1323 25.0672C25.3226 25.1343 25.7008 25.4493 25.8605 25.4651C26.3429 25.5132 27.4865 24.6709 27.834 25.4858C28.2006 26.3455 27.0761 26.3488 26.5688 26.5834C26.4488 26.7567 27.2887 28.2249 26.1683 28.2639C25.3822 28.2912 25.5121 26.9175 25.2341 26.5287C25.2051 26.4881 24.6077 26.1523 24.5415 26.1366L24.4223 26.1855C23.7877 27.7466 22.3354 26.7426 23.4178 25.5041L22.7847 25.0953C22.2816 25.4328 21.8323 25.8829 21.1902 25.9526L21.1819 26.7459C22.8716 26.5262 22.8658 28.2614 21.1819 28.0259C21.2167 28.2995 21.124 28.6775 21.1985 28.9304C21.2729 29.1833 22.064 29.6972 22.179 29.9493C22.2941 30.2013 22.2121 30.7252 21.9258 30.8131C21.2018 31.0336 21.0487 30.3024 20.5133 30.0338C20.0599 30.3629 19.8828 30.959 19.2258 30.8272C18.8981 30.7617 18.793 30.295 18.8807 30.0048C18.9576 29.7503 19.7479 29.238 19.8621 28.9859C19.9763 28.7339 19.8662 28.3086 19.9043 28.0259C18.2154 28.2589 18.2195 26.5246 19.9043 26.7459L19.896 25.9526C19.2539 25.8821 18.8054 25.4328 18.3015 25.0953L17.6684 25.5041C17.9407 25.8854 18.3288 26.4458 17.867 26.8263C17.0776 27.478 16.764 26.13 16.5894 26.115C16.4355 26.2369 15.9448 26.3977 15.8521 26.5279C15.5865 26.8993 15.6924 28.2166 14.9849 28.2639C13.7975 28.3435 14.62 26.7302 14.5174 26.5818C14.006 26.3397 12.898 26.3588 13.2522 25.4842C13.5823 24.6692 14.7499 25.5107 15.2257 25.4634C15.3341 25.4527 15.9994 25.0779 15.9804 24.9561C15.9349 24.6684 15.3622 24.3542 15.6651 23.8178C16.0821 23.08 16.9138 23.8303 17.0288 24.4222L17.7297 23.9215C17.5369 23.327 17.5898 22.8238 17.7379 22.2319L17.0288 21.7793C16.9386 22.3778 16.0755 23.1098 15.6651 22.3844C15.3664 21.8564 16.0722 21.2603 15.9878 21.1409C15.7429 21.1011 15.4458 20.7844 15.2183 20.7612C14.7102 20.7115 13.6005 21.5297 13.2522 20.7181C12.9236 19.9529 14.0035 19.744 14.5505 19.6976C14.6448 19.6014 13.8041 17.9716 14.9079 17.945C15.7172 17.926 15.5641 19.2798 15.8728 19.6943C15.9853 19.846 16.3759 19.914 16.5215 20.0905C16.793 20.1328 16.8823 19.0982 17.6089 19.23C18.3354 19.3618 18.0449 20.3169 17.6726 20.6999L18.3039 21.1061C18.8244 20.7911 19.2597 20.3401 19.896 20.2489L19.9059 19.4572C18.2212 19.7017 18.2204 17.9326 19.9059 18.1772C19.8687 17.8953 19.9788 17.4708 19.8637 17.2172C19.7487 16.9635 18.9593 16.4528 18.8823 16.1983C18.788 15.8866 18.8906 15.4721 19.2431 15.3917C19.8977 15.2416 20.0607 15.8468 20.5158 16.1693C20.9039 15.9231 21.6701 14.8445 22.1352 15.5823C22.6383 16.3815 21.3433 16.791 21.201 17.2719C21.1257 17.5247 21.2192 17.9036 21.1844 18.1763L21.1794 18.1813ZM20.3635 21.4029C18.1715 21.6317 18.4926 24.9909 20.6598 24.8301C22.827 24.6692 22.6333 21.1658 20.3635 21.4029Z"
            fill="#223C71"
          />
          <path
            d="M3.33878 22.5197L9.3132 22.5039C9.93298 22.7253 9.95367 23.6214 9.23624 23.7043C7.43978 23.9132 5.24529 23.5493 3.4058 23.6977C2.74464 23.5941 2.69748 22.707 3.33795 22.5188L3.33878 22.5197Z"
            fill="#223C71"
          />
          <path
            d="M31.708 22.5194L37.6808 22.5078C38.3816 22.6181 38.4032 23.6137 37.6071 23.7057C35.8107 23.9147 33.6162 23.5507 31.7767 23.6991C31.1643 23.6029 31.185 22.7549 31.708 22.5194Z"
            fill="#223C71"
          />
          <path
            d="M3.33852 19.3172C4.23386 19.1166 5.71422 19.4291 6.67576 19.3015C7.26741 19.4781 7.30547 20.3958 6.67245 20.4961C6.2314 20.5657 3.73572 20.5732 3.33852 20.4837C2.73612 20.3485 2.73612 19.4515 3.33852 19.3164V19.3172Z"
            fill="#223C71"
          />
          <path
            d="M34.3447 19.3174C35.36 19.3954 36.7055 19.1525 37.6803 19.3058C38.3779 19.4153 38.3746 20.3894 37.6803 20.4971C37.1557 20.5792 34.9372 20.5792 34.4134 20.4971C33.8011 20.401 33.8217 19.5529 34.3447 19.3174Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Groupes frigorifiques",
      whPerHour: 5,
      defaultHoursUsagePerDay: 2,
    },
    {
      icon: (
        <svg width="48" height="38" viewBox="0 0 48 38" fill="none">
          <path
            d="M37.0967 10.1853C37.1569 9.90146 37.5711 9.63889 37.8595 9.63967L37.8759 7.9597C37.2569 7.70657 36.6121 7.15549 36.4253 6.48255C36.2385 5.80962 36.2299 2.96696 36.3308 2.2162C36.4714 1.17457 37.4843 0.382929 38.4941 0.306674C39.7321 0.213124 42.5356 0.158095 43.7126 0.319252C44.6145 0.442676 45.5055 1.39783 45.6071 2.30503C45.697 3.10846 45.7032 5.39454 45.5836 6.1736C45.457 6.99669 44.795 7.66018 44.0533 7.9597L44.0698 9.63967C44.3183 9.6043 44.8326 9.96513 44.8326 10.1853V15.0978H47.3953C47.7142 15.0978 47.9064 15.6418 47.9322 15.9201C48.155 18.3933 47.7548 21.1998 47.9299 23.7099C47.9869 23.9489 47.579 24.4371 47.3961 24.4371H44.8333V29.3497C44.8333 29.8992 43.6743 29.9542 43.2859 29.885V37.1324C43.2859 37.3549 42.7317 37.7597 42.4683 37.6725C38.5113 37.391 34.076 38.031 30.176 37.6725C29.6844 37.6277 29.4171 37.4665 29.3561 36.9406C29.1725 35.3605 29.3874 33.219 31.3405 32.9981C32.8739 32.8244 36.3511 33.344 37.5492 32.822C37.9884 32.631 38.6434 31.8739 38.6434 31.3928V29.885C37.3312 30.036 36.9459 29.6492 37.0959 28.3285H34.0009C34.008 29.1209 34.158 29.8001 33.1834 29.8897C32.6559 29.9377 30.2323 29.966 29.8368 29.8418C29.2178 29.6468 29.3639 28.841 29.3585 28.3285H16.7848C16.7605 28.7231 16.2666 29.2852 15.9633 29.4967C15.8445 29.5792 15.1716 29.885 15.0919 29.885H13.1091V32.9981C16.0985 33.0272 16.2525 37.4461 13.2584 37.6717C12.0196 37.7652 9.2177 37.8203 8.0399 37.6591C7.42168 37.575 6.70734 37.0137 6.41582 36.4713C5.56235 34.8856 6.69874 32.973 8.46663 32.9981V29.885H6.48381C5.85388 29.885 4.90897 28.9212 4.73078 28.323L1.91404 28.2554L0 26.2908L0.0296992 13.1773L1.9156 11.2803L4.73234 11.2127C5.06059 10.4203 5.71554 9.7686 6.59245 9.6609C9.17706 9.34487 12.34 9.86608 14.9856 9.6609C15.7812 9.75288 16.6027 10.4101 16.7855 11.2064H18.5269V6.29388C18.5269 6.02031 19.0794 5.76796 19.3068 5.69956C19.4866 4.72633 18.7316 2.76728 20.1181 2.6415C21.6343 2.50314 24.7059 2.50235 26.2205 2.6415C27.6062 2.76885 26.8528 4.71689 27.0318 5.69956C27.2592 5.76796 27.8118 6.02031 27.8118 6.29388V11.2064H29.3593C29.3569 10.6609 29.2209 9.82599 29.9118 9.67033C30.3487 9.57207 32.8857 9.57993 33.3671 9.6554C33.5844 9.6892 34.0017 9.98164 34.0017 10.1845V12.7622H37.0967C37.2038 12.0217 36.9521 10.8676 37.0967 10.1845V10.1853ZM43.84 2.08963C43.6977 1.94577 43.3507 1.8813 43.1467 1.86244C42.0909 1.76574 39.7282 1.74923 38.6903 1.86479C38.3112 1.90725 38.0072 2.05897 37.9181 2.44811C37.7915 2.99997 37.8001 5.09817 37.8681 5.71214C37.9345 6.31275 38.2167 6.49041 38.7849 6.5423C39.84 6.63899 42.2042 6.6555 43.2413 6.53994C43.6204 6.49749 43.9244 6.34577 44.0135 5.95663C44.1401 5.40555 44.1315 3.30578 44.0635 2.6926C44.0455 2.53065 43.9478 2.19733 43.84 2.08963ZM25.4905 4.20197H20.8481V5.75852H25.4905V4.20197ZM26.2643 7.31507H20.0743V15.0978H26.2643V7.31507ZM42.5129 8.09335H39.4179V9.64989H42.5129V8.09335ZM15.2115 11.4289C15.1044 11.3212 14.773 11.2222 14.612 11.2041C12.1798 11.3778 9.45686 10.9824 7.0606 11.2017C6.41503 11.2607 6.20089 11.469 6.14149 12.1262C5.7046 16.979 6.48303 22.4883 6.14149 27.4095C6.20011 28.0588 6.40722 28.2742 7.0606 28.334C8.90664 28.5022 12.6722 28.503 14.5175 28.334C15.163 28.275 15.3772 28.0667 15.4366 27.4095C15.8735 22.5567 15.095 17.0474 15.4366 12.1262C15.435 11.9305 15.342 11.5602 15.2107 11.4289H15.2115ZM32.4542 11.2064H30.9067V15.6819L32.4542 15.3903V11.2072V11.2064ZM43.2866 11.2064H38.6442V28.3285H43.2866V11.2064ZM4.59947 12.763H2.61666C2.53381 12.763 1.53342 13.8408 1.50685 13.9815L1.49903 25.3679C1.39274 25.6257 2.52522 26.7727 2.61744 26.7727H4.60026V12.7638L4.59947 12.763ZM18.5269 12.763H16.9794V17.4326H29.3593V12.763H27.8118V16.119C27.8118 16.4397 27.2709 16.6331 26.9943 16.6591C24.5355 16.8831 21.7453 16.4806 19.2498 16.6567C19.0122 16.7141 18.5269 16.3037 18.5269 16.1198V12.7638V12.763ZM37.0967 14.3195H34.0017V15.0978C34.8771 14.8046 35.8884 14.7716 36.763 14.5192C36.895 14.4807 37.0639 14.5082 37.0967 14.3195ZM37.0967 16.071L30.9489 17.3289V22.206L37.0967 23.4639V16.0703V16.071ZM46.3816 16.6544H44.8341V22.8806H46.3816V16.6544ZM29.3593 18.9892H16.9794V20.5457H29.3593V18.9892ZM29.3593 22.1023H16.9794V23.6588H29.3593V22.1023ZM32.4542 24.1454L30.9067 23.8538V28.3293H32.4542V24.1462V24.1454ZM37.0967 26.7719V25.1666C36.1416 24.7744 35.0068 24.7578 34.0017 24.4371V26.7719H37.0967ZM29.3593 25.2154H16.9794V26.7719H29.3593V25.2154ZM11.5632 29.885H10.0157V32.9981H11.5632V29.885ZM41.7391 29.885H40.1917V31.6845C40.1917 31.756 39.972 32.3519 39.9189 32.48C38.548 35.7991 34.2362 34.094 31.5578 34.5775C30.8176 34.7111 30.8872 35.5027 30.9067 36.1112H41.7391V29.885ZM13.664 34.7771C13.5577 34.6694 13.2248 34.5704 13.0645 34.5523C12.1235 34.447 9.34197 34.4328 8.42599 34.561C7.51 34.6891 7.46858 35.9705 8.42599 36.1049C9.2857 36.226 12.2924 36.226 13.1521 36.1049C13.8101 36.0122 14.1259 35.2433 13.6632 34.7764L13.664 34.7771Z"
            fill="#223C71"
          />
          <path
            d="M11.9251 12.787C13.7047 12.4159 14.6355 14.6659 13.2022 15.6273C11.8516 16.5337 10.1971 15.0039 10.9646 13.5716C11.1248 13.2728 11.5937 12.8562 11.9251 12.787Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Groupes motopompes",
      whPerHour: 20,
      defaultHoursUsagePerDay: 24,
    },
    {
      icon: (
        <svg width="51" height="40" viewBox="0 0 51 40" fill="none">
          <path
            d="M50.6786 30.5469C50.6419 30.8924 50.4265 31.6583 50.3104 32.0168C50.0028 32.9709 49.4917 33.7666 48.9201 34.5743C49.323 35.1669 51.0795 36.1847 50.0921 36.9655C49.1375 37.7204 48.1987 35.6907 47.7759 35.7296C46.5365 36.608 45.0985 37.3419 43.5524 37.4047C43.4631 38.1307 43.9494 40.1156 42.7208 39.9891C42.5551 39.9721 42.0689 39.6505 42.0689 39.5419V37.5043H34.8445V39.4423C34.8445 39.7172 34.3086 40.019 34.0119 39.9891C33.7151 39.9592 33.2607 39.521 33.2607 39.2441V37.5043H26.1345V39.3427C26.1345 39.6037 25.679 39.9851 25.3912 39.999C25.0578 40.015 24.5517 39.6505 24.5517 39.3427V37.5043H17.4255V39.2441C17.4255 39.4851 17.0147 39.9194 16.7587 39.9721C15.4745 40.239 15.9042 38.2164 15.8417 37.5043C14.5705 37.8299 13.467 36.5741 14.9069 35.9208L43.3083 35.9188C50.9803 35.0553 51.0825 24.1541 43.4076 23.1881H7.27663C1.38498 23.7498 -0.638451 31.5687 4.37 34.8631C6.92633 36.5442 9.17006 35.6579 11.9368 35.9108C12.4826 35.9606 12.8607 36.2474 12.7713 36.852C12.7346 37.097 12.3486 37.5063 12.1303 37.5063H8.61731V39.5439C8.61731 39.6525 8.13105 39.9741 7.96533 39.9911C6.73679 40.1175 7.22304 38.1327 7.13373 37.4067C5.97068 37.3708 4.80664 36.8948 3.80138 36.3301C3.54634 36.1867 2.99955 35.6529 2.78123 35.7614C2.30093 36.2325 1.01681 37.9086 0.41643 36.7474C-0.0430335 35.8571 1.34528 35.2276 1.76505 34.5753C-0.57494 31.6643 -0.603718 27.4376 1.771 24.5336C1.35719 23.9719 -0.414177 22.8555 0.576201 22.0976C1.48818 21.3994 2.19375 23.0536 2.90924 23.3783C4.13878 22.472 5.56977 21.7201 7.13472 21.7022V20.6087H4.90687C3.97405 20.6087 2.65818 18.83 2.78024 17.8739C3.06207 13.0318 2.40016 7.75251 2.77528 2.96219C2.89734 1.39961 3.79344 0.254317 5.3971 0.128832C10.6149 -0.280486 16.3597 0.444535 21.6321 0.132816C22.9093 0.368846 23.8481 1.3538 23.9612 2.66442C24.3869 7.6091 23.6367 13.1643 23.9563 18.1727C24.0029 19.0869 22.695 20.6087 21.8296 20.6087H17.4255V21.6026H24.5517V19.7642C24.5517 18.9216 26.1355 18.9216 26.1355 19.7642V21.6026H33.2616V20.6087H28.8575C27.9922 20.6087 26.6843 19.086 26.7309 18.1727C27.0504 13.1643 26.3012 7.6091 26.7259 2.66442C26.8391 1.3538 27.7778 0.368846 29.055 0.132816L45.5749 0.141779C46.5712 0.344944 47.3949 0.982326 47.7164 1.96728C47.9615 2.71819 48.1957 4.22998 46.9672 4.11844C46.0363 4.03379 46.5156 2.76102 46.1276 2.17144C45.9798 1.94637 45.434 1.62568 45.1869 1.62568H41.3762V10.4225C41.3762 10.8378 40.9028 11.3248 40.4612 11.2511C39.8341 11.1456 37.6191 9.1667 37.2847 9.19758C36.5741 9.48838 34.5646 11.3248 33.9265 11.2482C33.741 11.2262 33.2607 10.8807 33.2607 10.7213V1.62269H29.3517C28.6511 1.62269 28.1659 2.51802 28.2105 3.15938C28.4914 7.84314 27.8483 12.9442 28.2105 17.5791C28.2859 18.5442 28.5162 19.0292 29.5462 19.1198L45.4379 19.069C46.0284 18.819 46.2695 18.5213 46.3291 17.8769C46.6704 14.2279 46.0611 10.0551 46.332 6.34928C46.5593 5.24382 47.8394 5.43902 47.9089 6.54149C47.6797 10.2084 48.2165 14.2488 47.9079 17.8709C47.8335 18.7444 47.4485 19.6377 46.7151 20.1526C46.596 20.2362 45.8597 20.6067 45.7813 20.6067H43.5544V21.7002C45.1164 21.7271 46.5593 22.4571 47.7789 23.3763C48.4815 23.0487 49.2794 21.3238 50.1546 22.1285C51.0299 22.9331 49.3339 23.945 48.9171 24.5306C49.5632 25.3681 50.1219 26.3581 50.4116 27.3868C50.491 27.6697 50.6548 28.2971 50.6806 28.558C50.7302 29.05 50.7332 30.0559 50.6806 30.5459L50.6786 30.5469ZM9.30998 1.62269H5.49931C5.18175 1.62269 4.55954 2.02205 4.45237 2.36066V18.3779L5.17878 19.0391L21.1399 19.1198C22.17 19.0282 22.4002 18.5442 22.4757 17.5791C22.8379 12.9442 22.1948 7.84314 22.4757 3.15938C22.5203 2.51802 22.035 1.62269 21.3344 1.62269H17.4255V10.7173C17.4255 10.8807 16.8827 11.281 16.6753 11.2631C15.4954 10.805 14.5288 9.80209 13.4015 9.19359C12.688 9.45153 10.6854 11.2999 10.0771 11.2462C9.77044 11.2193 9.30998 10.8199 9.30998 10.5191V1.62269ZM15.8417 1.62269H10.8938V8.97847C11.6093 8.72153 12.7118 7.47067 13.4184 7.47664C14.0436 7.48262 15.2106 8.69862 15.8417 8.97847V1.62269ZM39.7924 1.62269H34.8445V8.97847C35.4488 8.71854 36.6972 7.48162 37.2678 7.47664C37.9823 7.47067 39.0828 8.71157 39.7934 8.97847V1.62269H39.7924ZM15.8417 20.6077H8.76517C8.49426 20.8437 8.65304 21.274 8.61632 21.6016H15.8407V20.6077H15.8417ZM42.0689 21.6016C42.0321 21.274 42.1919 20.8437 41.92 20.6077H34.8435V21.6016H42.0679H42.0689Z"
            fill="#223C71"
          />
          <path
            d="M42.0449 24.8076C48.6004 23.9133 49.5253 33.714 43.2943 34.3116C37.0593 34.9091 36.1909 25.6064 42.0449 24.8076ZM42.5431 26.2995C38.4734 26.677 38.7076 32.7759 42.811 32.7759C47.2697 32.7759 46.8777 25.8972 42.5431 26.2995Z"
            fill="#223C71"
          />
          <path
            d="M7.20959 24.8095C13.7245 24.0188 14.569 33.7119 8.35775 34.3115C2.13068 34.913 1.26336 25.5315 7.20959 24.8095ZM7.60554 26.2994C3.54976 26.6719 3.79289 32.7758 7.87348 32.7758C12.3699 32.7758 11.9352 25.902 7.60554 26.2994Z"
            fill="#223C71"
          />
          <path
            d="M24.9253 26.7976C28.8472 26.2817 29.1697 32.1815 25.489 32.3388C21.8083 32.4962 21.5662 27.2388 24.9253 26.7976ZM24.9204 28.3831C23.8119 28.638 23.8318 30.7125 25.2945 30.7563C27.0956 30.8111 26.9696 27.912 24.9204 28.3831Z"
            fill="#223C71"
          />
          <path
            d="M16.905 26.7947C20.7246 26.1862 21.3846 31.9794 17.6612 32.327C14.1294 32.6577 13.5677 27.3266 16.905 26.7947ZM17.0013 28.3822C15.8849 28.6392 15.864 30.7256 17.3764 30.7545C19.1795 30.7883 19.0039 27.9221 17.0013 28.3822Z"
            fill="#223C71"
          />
          <path
            d="M32.8415 26.7961C36.1143 26.319 37.324 30.949 34.2884 32.1262C30.444 33.616 28.9247 27.3677 32.8415 26.7961ZM32.8366 28.3816C31.7589 28.6275 31.7102 30.8713 33.4072 30.7478C35.1041 30.6243 34.8084 27.9324 32.8366 28.3816Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Convoyeurs",
      whPerHour: 50,
      defaultHoursUsagePerDay: 3,
    },
    {
      icon: (
        <svg width="51" height="44" viewBox="0 0 51 44" fill="none">
          <path
            d="M3.34079 3.66707C3.57815 1.86143 5.38073 0.163703 7.15259 0.0557967C13.0043 0.370884 19.0921 -0.909608 24.5961 1.58232C28.6621 3.42392 31.8832 6.79061 33.6691 10.9716C35.4465 16.1238 36.7032 21.4673 38.2377 26.7059L39.2946 26.7677V2.64412H36.7869V0.920488H43.7375C43.8604 0.920488 44.3128 1.38665 44.3128 1.51326V6.95175H46.2466C46.3695 6.95175 46.8219 7.4179 46.8219 7.54452V31.0768H51.0023V32.8004H45.7244C45.6016 32.8004 45.1492 32.3343 45.1492 32.2076V8.67538H44.3128V29.6222C44.3128 29.7488 43.8604 30.215 43.7375 30.215H40.9674V32.2609C44.9118 33.6579 45.515 39.2388 41.9391 41.502L40.131 42.2775H43.475V44.0011H0.833093V42.2775H3.44551L2.26147 41.8286C-1.08258 40.069 -0.627401 34.8147 3.02802 33.8248C7.98197 32.4839 10.4715 39.3453 6.09143 41.8286L4.90739 42.2775H38.4569L37.0997 41.7912C32.3314 39.3424 33.9316 31.9415 39.2932 31.9386V28.4928H21.8916C19.6771 28.4928 17.5059 24.0902 16.1445 22.568C15.829 22.2155 15.0415 21.5997 14.5751 21.5997H4.17715V31.0236L4.35308 31.1646C7.65804 31.6091 9.87531 33.9716 10.7145 37.2102C10.7829 37.4764 10.6893 37.7958 10.9183 37.9699H32.6051V39.6935H9.76919C9.12132 39.6935 9.13948 38.0217 9.04174 37.5354C8.57678 35.2535 6.72114 33.4018 4.51225 32.94C3.8951 32.8105 2.75994 32.95 2.5519 32.2681V20.4099C2.69431 20.0487 3.00987 19.8545 3.38826 19.8718C6.99062 20.1005 10.9127 19.584 14.476 19.8718C14.7748 19.8962 15.1574 19.9696 15.4436 20.0588C18.1091 20.8875 20.2412 26.7677 22.2057 26.7677H35.0095L27.485 16.0432C27.3677 15.9483 27.3063 16.0533 27.2183 16.095C26.7143 16.331 25.1421 17.8877 24.8014 17.9223L23.7277 16.5885L30.5163 11.5212L31.4518 12.8233C31.4378 12.9844 29.194 14.4203 28.9399 14.7556C28.8673 14.852 28.8212 14.9081 28.8841 15.0318L35.9506 24.9362C34.8559 21.2386 34.0028 17.4388 32.8439 13.7614C31.4755 9.41777 29.0851 6.02519 25.1518 3.80951C19.6324 0.700359 13.3129 2.07005 7.27267 1.79381C6.34974 1.79957 5.01351 3.05992 5.01351 3.98935V18.151H3.34079V3.66707ZM42.6387 2.64412H40.966V28.4913H42.6387V2.64412ZM41.6613 34.6679C38.57 31.4825 33.8506 36.3139 36.8917 39.5295C39.9341 42.748 44.8043 37.9066 41.6613 34.6679ZM3.83925 35.4118C0.708825 35.8434 1.17937 40.7395 4.33074 40.5553C7.68178 40.3596 7.24056 34.9442 3.83925 35.4118Z"
            fill="#223C71"
          />
          <path
            d="M37.6208 30.2129V31.9365H34.7473L31.6057 36.7765C31.5401 36.8657 31.2636 37.106 31.1938 37.106H16.7188V35.3823H30.4622L33.6038 30.5424C33.6694 30.4532 33.9459 30.2129 34.0157 30.2129H37.6208Z"
            fill="#223C71"
          />
          <path
            d="M7.52038 6.08984V17.2905H15.0449V19.0142H6.42292C6.30005 19.0142 5.84766 18.548 5.84766 18.4214V6.08984H7.52038Z"
            fill="#223C71"
          />
          <path
            d="M7.52038 23.3223H5.84766V26.7681H7.52038V23.3223Z"
            fill="#223C71"
          />
          <path
            d="M10.8641 23.3223H9.19141V26.7681H10.8641V23.3223Z"
            fill="#223C71"
          />
          <path
            d="M14.2118 23.3223H12.5391V26.7681H14.2118V23.3223Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Chariots élévateurs électriques",
      whPerHour: 50,
      defaultHoursUsagePerDay: 3,
    },
    {
      icon: (
        <svg width="122" height="122" viewBox="0 0 122 122" fill="none">
          <path
            d="M65.4196 0.00195312C67.2996 0.431953 69.2696 0.461953 71.1796 0.781953C97.0596 5.11195 117.83 26.442 121.29 52.502C121.47 53.842 121.43 55.352 121.8 56.632C121.67 59.442 121.97 62.402 121.8 65.202C119.11 108.882 70.8896 135.332 32.2396 114.922L7.19956 121.312C4.94956 121.352 4.76956 120.112 5.11956 118.262C6.50956 110.932 9.42956 103.372 10.9196 96.002C-5.56044 72.162 -3.22044 39.812 16.7696 18.802C17.8996 17.612 19.5196 15.412 21.3096 16.412C24.0696 17.972 19.3196 21.342 18.1996 22.602C4.00956 38.652 -0.240437 60.732 7.19956 80.972C9.12956 86.212 11.8996 90.592 14.7496 95.332L9.27956 116.842L32.4696 111.112C33.6396 111.092 36.7596 112.972 38.1396 113.552C82.3696 132.092 128.44 92.532 116.25 46.112C106.77 9.98195 65.0496 -7.44805 32.4396 11.142C31.2196 11.842 27.6496 14.562 26.5896 14.462C25.0396 14.312 24.4396 12.422 25.4796 11.312C26.1896 10.552 29.4496 8.67195 30.5396 8.04195C38.1896 3.60195 47.7996 0.541953 56.6196 0.00195312H65.4196Z"
            fill="#223C71"
          />
          <path
            d="M57.5203 12.442C87.7603 10.442 112.34 36.132 109.18 66.272C109.02 67.772 108.22 73.042 107.49 74.102C106.58 75.422 104.2 74.732 104.18 72.952C113.38 39.262 82.3703 8.12198 48.5303 17.732C20.3103 25.742 7.1003 58.962 22.5103 84.232C40.1003 113.072 81.6203 112.922 99.3003 84.232C100.23 82.732 101.52 78.832 103.34 79.022C106.39 79.332 104.13 83.062 103.37 84.502C88.1303 113.382 47.3803 117.882 25.3403 94.022C-2.3097 64.072 17.0803 15.112 57.5203 12.442Z"
            fill="#223C71"
          />
          <path
            d="M65.8098 80.3519C61.7298 84.3419 55.0098 81.9719 53.9998 76.5119C54.4098 70.6119 53.4598 63.9919 53.9998 58.1819C54.1698 56.3319 55.1698 54.5619 56.6498 53.4519C60.2998 50.7219 64.8398 53.3919 68.1498 48.0519C73.7598 39.0019 61.7198 30.0419 54.5998 37.3519C51.4998 40.5419 53.1598 44.3119 51.0298 47.3419C47.3198 52.6219 38.9698 50.5019 38.3198 44.1419C37.4398 35.5019 43.8098 26.2619 51.4798 22.8019C69.0998 14.8619 87.6898 30.9019 82.6798 49.4919C82.2298 51.1819 80.9498 55.7719 78.4998 53.6719C77.0098 52.3919 78.8898 49.8019 79.3098 48.0219C84.7798 24.9819 52.3098 15.0419 43.5098 35.7619C42.3398 38.5219 40.0998 46.1419 44.8598 46.6419C48.8098 47.0519 48.3398 43.6619 48.8598 41.1119C51.5698 27.7819 70.2798 27.9019 72.9498 40.9119C74.2398 47.2019 70.2698 53.7619 63.9798 55.2919C61.4098 55.9119 58.4698 55.0919 57.6098 58.4319C58.0898 63.8219 56.8998 70.5319 57.6098 75.7719C58.1198 79.5419 63.5898 79.5419 64.1898 75.9719C64.8198 72.2519 63.7998 67.2319 64.2398 63.4119C64.4698 61.4419 67.7798 61.1819 69.4898 60.3319C70.7398 59.7119 73.2698 57.4419 73.9898 57.2119C75.5398 56.7119 76.6898 58.0019 76.3298 59.5619C75.9198 61.3519 69.6998 64.2619 67.9298 64.9619C67.0798 69.5519 69.4498 76.8219 65.8298 80.3519H65.8098Z"
            fill="#223C71"
          />
          <path
            d="M59.8912 87.1618C69.5512 85.9018 70.6112 100.742 61.2512 101.142C52.4712 101.522 51.3512 88.2818 59.8912 87.1618ZM60.8212 90.7518C56.3012 90.8818 56.4612 98.1418 61.7212 97.3218C65.3412 96.7518 64.9612 90.6318 60.8212 90.7518Z"
            fill="#223C71"
          />
        </svg>
      ),
      label: "Autre",
      whPerHour: 75,
      defaultHoursUsagePerDay: 1,
    },
  ],
};
