"use client";

import SuperSimuIllustration from "../illustrations/supersimu-illustration";
import StepQuestion from "./step-question";

export default function SuperSimuStep({ state, setState }) {
  return (
    <div className="flex">
      <aside className="hidden md:flex items-center justify-center w-full md:w-1/2">
        {/* <img src="/assets/images/simulators/injection/supersimu.svg" /> */}
        <SuperSimuIllustration />
      </aside>
      <aside className="flex items-center justify-center w-full md:w-1/2">
        <div className="flex flex-col items-center gap-4">
          <StepQuestion
            number={1}
            question="Où souhaitez-vous installer votre système solaire ?"
          />

          <div>
            <svg
              viewBox="0 0 338.92 346.14"
              className="h-[350px] xl:h-[400px] [&_circle]:fill-brand-primary"
            >
              <g className="[&_[data-selected='true']_path]:fill-brand-primary [&_[data-selected='true']>g>text]:fill-white [&_[data-selected='true']>g>circle]:fill-brand-secondary">
                {/* ZONE 1 */}
                <g
                  role="button"
                  className="group"
                  data-selected={state.zone === 1}
                  onClick={() => setState({ ...state, zone: 1 })}
                >
                  <path
                    data-zone="1"
                    id="zone-1"
                    className="fill-secondary group-hover:fill-brand-primary"
                    d="M335.82,92.96c-3.7,0-7.2.7-10.2-.1-8.7-2.3-17.2-.6-25.8,0-.5,0-1.3,1.7-1.2,2.4.3,1.6,1.1,3.1,1.9,5.1-5.3,1.2-10.1,2.3-15,3.3-1.9.4-3,1-2.7,3.1.2,1.8.3,3.4-1.7,4.8-.8.6-.4,2.8.2,3.6,1.2.9,2.6,1.8,3.5,2.9.4.6.1,1.9,0,2.9-.3,1.4-.7,2.8-1.2,4.2-.2.6-.8,1.3-1.4,1.5-1.9.8-3.9,1.9-5.9,1.9-4.7.2-8.1,2.2-11.2,5.4-1.5,1.5-3.5,2.5-5.3,3.7-3.4,2.2-7.1,4.1-10,6.9-2.3,2.1-3.4,5.4-5.2,8-1.9,2.7-2.9,2.6-5.4.1-.5-.5-1.5-.8-2.2-.7-5.3.6-10.8.8-16,2-3.7.8-5.1,1.2-8.7,2.1-1,.3-3.2,1.4-3.6,1.6-5.3,2.7-5.9,3.8-11.1,6.9-4.3,2.6-10.7,2.3-10.7,8.2,0,9.6-.6,21.8,0,30.7.6,8.8.6,19.8.5,28.7,0,.5-.2.5-.2,0-7.8,0-16.3-.1-25.1-.2h0c0-.1.1-.1.1-.1v-19c4.2-19,13.8-48.1,34.5-91.2,17-15.8,52.4-34.5,82.7-48.9l44.8-19.3v1.2c2,1,2.7,2.9,1.5,5.5-1.7,3.5-.2,6.1,2,8.8.6.7.2,2.2.7,3.1,1.1,2.1,2,4.5,3.6,6.1,1.8,1.9,4.4,3.1,6.9,4.8-2.6,2.8-8.1,4.5-3.2,10h.1Z"
                  />
                  <g data-zone="1" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="192.22"
                      cy="147.26"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(195.12 148.66)"
                    >
                      <tspan x="0" y="0">
                        Tata
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="1" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="167.72"
                      cy="229.46"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(166.32 227.3) rotate(-45.51)"
                    >
                      <tspan x="0" y="0">
                        Bir Lahlou
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="1" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="176.12"
                      cy="201.96"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(169.3 202.1) rotate(-45.51)"
                    >
                      <tspan x="0" y="0">
                        Mahbès
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="1" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="215.32"
                      cy="120.06"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(212.74 117.36)"
                    >
                      <tspan x="0" y="0">
                        Ouarzazate
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="1" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="237.02"
                      cy="133.96"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(240.62 135.36)"
                    >
                      <tspan x="0" y="0">
                        Zagora
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="1" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="270.92"
                      cy="109.16"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(261.22 105.16)"
                    >
                      <tspan x="0" y="0">
                        Erfoud
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="1" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="319.62"
                      cy="84.16"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(307.02 79.56)"
                    >
                      <tspan x="0" y="0">
                        Bouarfa
                      </tspan>
                    </text>
                  </g>
                </g>

                {/* ZONE 2 */}
                <g
                  role="button"
                  className="group"
                  data-selected={state.zone === 2}
                  onClick={() => setState({ ...state, zone: 2 })}
                >
                  <path
                    data-zone="2"
                    id="zone-2"
                    className="fill-secondary group-hover:fill-brand-primary"
                    d="M324.32,53.48v-.1c0-.2-.62.71-.62.41-9.3,3.9-25.38,10.06-44.08,18.96h0c-30.3,14.4-65.7,33.1-82.6,48.9h0c-20.8,43.1-30.4,72.2-34.6,91.2v18.9c-17.1-.3-35.4-.7-51.2-.7,0,1.5.2,2.8.2,4,0,16.3-.5,36.2-.3,52.5,0,2.9-.6,4.4-4,4.2-1.8-.1-3.9-.2-5.6.5-9.6,4-11.8,4.5-14.2,13.8-2.2,8.4-1.9,19.8-1.1,28.4,0,1,.2,2.2.3,3.6H3.72c-1,3.2-1.9,5.2-2.8,7.6,0,0-.2-.1-.3-.1.7-5.5,1.2-11.1,2.3-16.5.6-3.1,2-6.1,3.4-9,.7-1.5,1.6-3.5,4.4-2.9.7.1,2.3-1.5,2.8-2.7,1.4-2.9,2.3-6.1,3.5-9.1.3-.8,1-1.4,1.6-2.2.6-.8,1.7-1.9,1.5-2.2-1.5-2.2.3-3.6,1.2-5.1,2-3.5,4.2-6.9,6.4-10.4.4-.6,1-1.1,1-1.7-.2-3.8,3.1-5.7,5.2-8,3.1-3.2,6.7-5.8,9.9-8.8,3.1-3,4.7-6.8,4.2-11-.4-3.8.5-7,2.2-10.5,2.2-4.4,3.3-9.3,5-14,.4-1.1,1.3-2.3,2.3-2.9,3.4-2.2,6.8-4.5,10.5-6.1,6.1-2.6,8.1-7.6,9.8-13.2,1.3-4.3,2.6-8.8,5.1-12.4,2.7-3.8,5.6-7.9,11.9-7.8,4.5,0,8.8-1.2,12.9-2.8h.2-.2s28.3,4.3,52.2-7.6c0,0,2.5-1.2,5.5-4.5h0c.3-.4.6-.7.9-1.1h0c.2-.2.3-.4.4-.6h0c.1-.2.3-.4.4-.6,0,0,0-.1.1-.1.1-.2.3-.4.4-.6,0,0,0-.1.1-.1,0-.2.2-.4.3-.5v-.2c.1-.2.2-.5.4-.7,0,0,0-.1.1-.2.1-.2.2-.4.3-.6v-.2c.1-.2.2-.5.4-.7,0-.1.1-.2.1-.3.1-.2.2-.5.4-.7,0,0,0-.2.1-.2.1-.2.2-.4.3-.6,0,0,.1-.2.1-.3.1-.3.2-.5.3-.8,0,0,.1-.2.1-.3,0-.2.2-.5.2-.7,0-.1,0-.2.1-.3.1-.3.2-.6.3-.9,0-.1,0-.2.1-.4,0-.2.2-.5.2-.8,0-.1,0-.2.1-.4,0-.3.2-.6.2-.8,0-.1,0-.2.1-.4,0-.3.2-.7.3-1v-.3c0-.3.1-.6.2-.9v-.4c0-.3.1-.7.2-1.1v-.3c0-.3.1-.7.2-1v-.3c0-.4.1-.8.2-1.3v-.4c0-.5.1-.9.2-1.4,0-.8.2-1.6.3-2.3s.3-1.4.5-2.1v-.2c.1-.6.3-1.2.4-1.9v-.3c.1-.6.3-1.2.4-1.8v-.4c.1-.6.3-1.2.5-1.8v-.4c.1-.5.2-1,.4-1.5,0-.1,0-.3.1-.5.1-.5.3-1,.4-1.5,0-.1,0-.2.1-.3.1-.6.3-1.2.5-1.7v-.1c.2-.6.5-1.2.5-1.7h0c1.8-5.9,3.5-10.7,4.9-14s2.3-5.2,2.3-5.2v-.1s.1-.2.2-.4c0,0,.1-.2.1-.3.3-.6.7-1.4,1.2-2.3.2-.4.4-.7.5-1.1.3-.7.6-1.4,1-2.1,1.1-2.3,2.4-4.5,3.9-6.5,1.2-1.7,2.6-3.3,4-4.7,1.9-2,4.1-3.7,6.4-5.2,4.4-2.9,10.1-5.8,16.9-7.6,1.9-.5,3.8-1.1,5.7-1.9,1-.4,2-.8,3-1.2q.67-.27,0,0c15.8-6.7,32.4-15.5,42.9-23.2,1.2-.9,2.5-1.5,4-1.9,12.7-3.6,40.1-13.8,48.1-16.9l.6,14.8,1.3.12Z"
                  />
                  <g data-zone="2" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="1.4"
                      cy="344.74"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(4.44 334.78)"
                    >
                      <tspan x="0" y="0">
                        La Gouira
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="2" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="55.56"
                      cy="321.68"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(49.31 318)"
                    >
                      <tspan x="0" y="0">
                        Aousserd
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="2" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="32.54"
                      cy="283.16"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(37.48 283.16)"
                    >
                      <tspan x="0" y="0">
                        Dakhla
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="2" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="82.7"
                      cy="261.96"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(65.28 269.35)"
                    >
                      <tspan x="0" y="0">
                        Bir Anzarane
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="2" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="60.41"
                      cy="232.26"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(56.82 240.16)"
                    >
                      <tspan x="0" y="0">
                        Boujdour
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="2" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="123.47"
                      cy="223.15"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(112.27 219.35)"
                    >
                      <tspan x="0" y="0">
                        Samara
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="2" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="86.52"
                      cy="205.46"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(87.82 201.66)"
                    >
                      <tspan x="0" y="0">
                        Laayoune
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="2" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="193.72"
                      cy="103.26"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(190.01 115.03) rotate(-37.34)"
                    >
                      <tspan x="0" y="0">
                        Marrakesh
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="2" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="235.62"
                      cy="82.06"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(228.82 96.85) rotate(-34.55)"
                    >
                      <tspan x="0" y="0">
                        Beni Mellal
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="2" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="265.37"
                      cy="72.66"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(265.37 70.41)"
                    >
                      <tspan x="0" y="0">
                        Midlet
                      </tspan>
                    </text>
                  </g>
                </g>

                {/* ZONE 3 */}
                <g
                  role="button"
                  className="group"
                  data-selected={state.zone === 3}
                  onClick={() => setState({ ...state, zone: 3 })}
                >
                  <path
                    data-zone="3"
                    id="zone-3"
                    className="fill-secondary group-hover:fill-brand-primary"
                    d="M148.41,163.59l-1.1.8,1.2-.8h-.1ZM163.77,127.56l-10.27-5.97c0,.2-.1.5-.1.7.2.1.5.3.7.4,1.8,1,3.3,2.09,4.5,2.79.6.4.89.71,1.09.91l.26-.02.1.14c-.21.14.9,1.22,2.56,2.72,1,4.7,1.28,10.26-.22,16.46-2,9-9.2,14.7-9.2,14.7l-4.7,3,5.6.2c4.78-3.15,8.51-8.3,10.17-13.71,1.1-3.4,1.83-11.39,1.83-14.99,0-2.5-1.4-4.8-2.1-7.2l-.23-.13ZM316.91,23.59c-.9.8-1.6,1.5-1.6,1.5,0,0-12.7,14.8-28.4,13.5-14.6-1.3-19.9-3.1-36.5,8-15.4,4.5-38.6,10.7-52.9,11,10,.8,29.3-3.8,53.7-10.9,11.2-7.5,16.2-8.9,22.8-8.9s5.9.3,9.2.6c1.2.1,2.4.2,3.8.4h1.9c7.6,0,15-3.3,19.3-6.4,4.7-3.3,7.6-6.7,7.6-6.8,0,0,.7-.7,1.7-1.5-.2-.2-.4-.4-.7-.6l.1.1ZM324.01,38.69c-8.1,3-35.5,13.2-48.1,16.8-1.4.4-2.8,1.1-4,1.9-10.6,7.7-27.1,16.5-42.9,23.1h0c-1,.4-2,.8-2.9,1.2-1.9.8-3.8,1.4-5.7,1.9-6.8,1.8-12.4,4.7-16.9,7.6-6.2,4.1-11.1,9.8-14.4,16.5-1.5,3.2-3.1,6.2-3.1,6.2-5.7,10.9-9,13.3-14.2,25.7-2.3,5.5-3.3,9.8-5.8,15.2-5.4,11.5-11.7,17.8-24.9,24.3-8.6,4.3-18.9,8.3-28.5,7.7-1.5,0-3.1-.2-4.6-.4h-.2s28.3,4.3,52.2-7.6c0,0,2.5-1.2,5.5-4.5h0c.3-.4.6-.7.9-1.1h0c.2-.2.3-.4.4-.6h0c.1-.2.3-.4.4-.6,0,0,0-.1.1-.1.1-.2.3-.4.4-.6,0,0,0-.1.1-.1,0-.2.2-.4.3-.5v-.2c.1-.2.2-.5.4-.7,0,0,0-.1.1-.2.1-.2.2-.4.3-.6v-.2c.1-.2.2-.5.4-.7,0-.1.1-.2.1-.3.1-.2.2-.5.4-.7,0,0,0-.2.1-.2.1-.2.2-.4.3-.6,0,0,.1-.2.1-.3.1-.3.2-.5.3-.8,0,0,.1-.2.1-.3,0-.2.2-.5.2-.7,0-.1,0-.2.1-.3.1-.3.2-.6.3-.9,0-.1,0-.2.1-.4,0-.2.2-.5.2-.8,0-.1.15-.17.25-.37,0-.3.21-.51.21-.71,0-.1,0-.25.1-.45,0-.3.19-.63.29-.93,0,0-.03-.23-.03-.33,0-.3.16-.71.26-1.01v-.4c0-.3.26-.71.36-1.11v-.3c0-.3.1-.7.2-1v-.3c0-.4-.09-.76,0-1.26,0-.1.38-.37.38-.57,0-.5-.29-.76-.19-1.26,0-.8.18-1.6.28-2.3s.33-1.4.53-2.1v-.2c.1-.6.29-1.2.39-1.9,0-.1.07-.21.07-.31.1-.6.25-1.18.35-1.78,0-.1,0-.22,0-.42.1-.6.27-1.18.47-1.78,0-.1-.02-.31-.02-.41.1-.5.22-.97.42-1.47,0-.1-.03-.3.07-.5.1-.5.32-.93.42-1.43,0-.1,0-.28.1-.38.1-.6.26-1.06.46-1.56,0,0-.01-.12-.01-.22.3-.58.73-1.24.61-1.76v-.19c1.8-5.9,3.33-10.49,4.73-13.79s2.3-5.2,2.3-5.2v-.1s.1-.2.2-.4c0,0,.1-.2.1-.3.3-.6.7-1.4,1.2-2.3.2-.4.4-.7.5-1.1.3-.7.6-1.4,1-2.1,1.1-2.3,2.4-4.5,3.9-6.5,1.2-1.7,2.6-3.3,4-4.7,1.9-2,4.1-3.7,6.4-5.2,4.4-2.9,10.1-5.8,16.9-7.6,1.9-.5,3.8-1.1,5.7-1.9,1-.4,2-.8,3-1.2q.67-.27,0,0c15.8-6.7,32.4-15.5,42.9-23.2,1.2-.9,2.5-1.5,4-1.9,12.7-3.6,40.1-13.8,48.1-16.9h1.5ZM323.41,35.49c.2,1,.5,2.1.7,3.1-8.1,3-35.5,13.2-48.1,16.8-1.4.4-2.8,1.1-4,1.9-10.6,7.7-27.1,16.5-42.9,23.1h0c-1,.4-2,.8-2.9,1.2-1.9.8-3.8,1.4-5.7,1.9-6.8,1.8-12.4,4.7-16.9,7.6-6.2,4.1-11.1,9.8-14.4,16.5-1.5,3.2-3.1,6.2-3.1,6.2-5.7,10.9-9,13.3-14.2,25.7-2.3,5.5-3.3,9.8-5.8,15.2-5.4,11.5-11.7,17.8-24.9,24.3-8.6,4.3-18.9,8.3-28.5,7.7-1.5,0-3.1-.2-4.6-.4,3.2-1.3,6.3-2.4,9.5-3.7,1.3-.5,2.1-2.1,3.1-3.2,1.4-1.6,2.8-3.2,4.3-4.6,2-1.9,4.2-3.5,6.9-4.7,1.2-.5,2.2-1,3.2-1.7h.9c1.3,0,2.4-.6,3.1-1.7l.3-.4h0l1.4-2.1h6.6l1.2-.8,5.5.2c5.02-3.36,8.5-8.4,10.2-13.8,1.1-3.4,1.8-11.3,1.8-14.9,0-2.5-1.4-4.8-2.1-7.2l-10.5-6.1v.6h0c0-3.6.1-8.5.2-11.9,0-.4,0-.8.2-1.2,2.4-3.8,4.5-7.7,7.2-11.3,2.5-3.3,5-6.1,4.1-10.9-.9-4.5,2.9-7.1,5.7-10,3-3.1,5.5-6.6,8.3-9.9.5-.6.9-1.3,1.6-1.6,3.4-1.7,7-3.2,10.4-4.9,2.2-1.1,4.5-2.1,6.7-3.2,10,.8,29.3-3.8,53.7-10.9,11.2-7.5,16.2-8.9,22.8-8.9s5.9.3,9.2.6c1.2.1,2.4.2,3.8.4h1.9c7.6,0,15-3.3,19.3-6.4,4.7-3.3,7.6-6.7,7.6-6.8,0,0,.7-.7,1.7-1.5-.2-.2-.4-.4-.7-.6h0c2,1.8,4,3.6,6.2,5.5-.3.7-.8,1.6-1.4,2.8.8,1.1,1.7,2.3,2.6,3.5-.3,0-.6.1-.9.2l-.3.3Z"
                  />
                  <g data-zone="3" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="155.62"
                      cy="168.61"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(142.39 176.15)"
                    >
                      <tspan x="0" y="0">
                        Guelmim
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="3" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="173.32"
                      cy="130.16"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(167.49 132.11) rotate(-52.97)"
                    >
                      <tspan x="0" y="0">
                        Taroudant
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="3" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="157.12"
                      cy="107.16"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(159.11 103.65)"
                    >
                      <tspan x="0" y="0">
                        Essaouira
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="3" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="183.32"
                      cy="66.86"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(176.62 74.76)"
                    >
                      <tspan x="0" y="0">
                        El Jadida
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="3" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="239.92"
                      cy="72.66"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(226.82 68.96)"
                    >
                      <tspan x="0" y="0">
                        Khenifra
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="3" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="255.62"
                      cy="45.96"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(250.12 53.06)"
                    >
                      <tspan x="0" y="0">
                        Fès
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="3" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="276.52"
                      cy="42.26"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(271.42 49.46)"
                    >
                      <tspan x="0" y="0">
                        Taza
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="3" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="319.92"
                      cy="34.46"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(302.89 40.2)"
                    >
                      <tspan x="0" y="0">
                        Oujda
                      </tspan>
                    </text>
                  </g>
                </g>

                {/* ZONE 4 */}
                <g
                  role="button"
                  className="group"
                  data-selected={state.zone === 4}
                  onClick={() => setState({ ...state, zone: 4 })}
                >
                  <path
                    data-zone="4"
                    id="zone-4"
                    className="fill-secondary group-hover:fill-brand-primary"
                    d="M162.42,129.06c-1.3-1.4-2.21-2.28-2.31-2.58,0,0-.16-.18-.36-.28l-.17-.14c-.2-.2-.53-.34-1.13-.74-.2-.1-.24-.19-.51-.3,1.2,2.5,2.28,5.34,2.28,5.84.4,4,1.2,6.9.3,10.8-1.1,4.8-2.1,9.9-5.9,13.2-1.9,1.6-3.9,3.1-6.1,4.3l-7.3,3.9v.2h0c-.2.3-.4.7-.6,1-.5.8-.9,1.6-1.4,2.2h.2l1.4-2.1h6.6l1.2-.8,4.7-3s7.2-5.7,9.2-14.7c1.5-6.3,1.2-12,.2-16.6l-.3-.2ZM316.72,23.56c-.9.8-1.6,1.5-1.6,1.5,0,0-12.7,14.8-28.4,13.5-14.6-1.3-19.9-3.1-36.5,8-15.4,4.5-38.6,10.7-52.9,11,9.85,1.3,29.36-3.56,53.76-10.66,11.2-7.5,16.17-9.1,22.77-9.1s5.87.26,9.17.56c1.2.1,2.41.27,3.81.47.6,0,1.3-.02,1.9,0,7.67.18,15.15-3.12,19.45-6.22,4.85-3.11,7.82-6.75,7.82-6.85,0,0,.63-.88,1.63-1.68-.2-.2-.71-.42-1.01-.62l.1.1ZM274.77,16.96s-.05,0-.15,0l-.1.1c-1.8,3.7-2.85,6.25-2.85,6.25,0,0-10.45,7.68-23.11,2.06-.6-.5-1.25-1.3-1.64-2.01.5.8,1.1,1.5,1.8,2,12.3,5.5,23.1-2,23.1-2M316.72,23.56c-.9.8-1.6,1.5-1.6,1.5,0,0-12.7,14.8-28.4,13.5-14.6-1.3-19.9-3.1-36.5,8-15.4,4.5-38.6,10.7-52.9,11,4.3-2.1,9-4.5,13.1-7,3-1.8,5.7-4.5,7.6-7.4,5.9-9.1,10.4-18.8,13.5-29.1,1-3.3,2-6.5,3.2-9.8.3-.7,1.3-1.4,2.2-1.8,1.3-.5,2.6-.8,4-1.2.3,2.6.7,5.9,1.4,9.2,0,.5.2.9.3,1.4.1.5.2,1.1.4,1.6.2.6.3,1.2.5,1.8q.13.4,0,0c.2.7.4,1.3.6,2s.5,1.5.8,2.1c.3.8.6,1.5,1,2.1.2.4.4.8.7,1.2.1.2.2.4.4.6.5.8,1.1,1.5,1.8,2,12.3,5.5,23-1.9,23-1.9,0,0,1.2-2.7,3-6.5h-.1v-.1c1.1-.1,2.4.3,3.5.1,1.3-.2,2.6-.8,4.2-1.4,5.6,3.4,11.4,2.7,15.1-4.1.1,1.1.2,1.9.3,2.6,1.1,5.6,4.6,7.3,10.4,5.4,1.4-.5,3.6,0,4.8.9,1,.8,2.5,2.1,3.7,3.2v.1Z"
                  />
                  <g data-zone="4" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="217.02"
                      cy="47.86"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(219.92 45.96)"
                    >
                      <tspan x="0" y="0">
                        Rabat
                      </tspan>
                    </text>
                  </g>

                  <g data-zone="4" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="312.22"
                      cy="23.86"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(288.75 32.66) rotate(-22.6)"
                    >
                      <tspan x="0" y="0">
                        Berkane
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="4" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="277.92"
                      cy="18.76"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(261.63 37.99) rotate(-33.34)"
                    >
                      <tspan x="0" y="0">
                        El Houceima
                      </tspan>
                    </text>
                  </g>
                </g>

                {/* ZONE 5 */}
                <g
                  role="button"
                  className="group"
                  data-selected={state.zone === 5}
                  onClick={() => setState({ ...state, zone: 5 })}
                >
                  <path
                    data-zone="5"
                    id="zone-5"
                    className="fill-secondary group-hover:fill-brand-primary"
                    d="M274.66,17.1c-1.8,3.7-2.9,6.3-2.9,6.3,0,0-10.7,7.3-23,1.9-.6-.5-1.54-1.13-2.04-1.83-.1-.2-.3-.4-.4-.6-.2-.4-.4-.8-.7-1.2-.3-.7-.7-1.4-1-2.1s-.5-1.4-.8-2.1c-.2-.6-.4-1.3-.6-2-.13-.47.21-.54.34-.07-.2-.6-.64-1.13-.84-1.73-.1-.5-.3-1.1-.4-1.6s-.2-.9-.3-1.4c-.6-3.2-1-6.78-1.2-9.38,1.1-.3,2.34-.29,3.34-.79,1.6-.7,3.1-.9,3.2,1,.3,6.5,5.2,9.8,9.5,13.3,5.1,4.1,10.9,4.7,17.2,2.5.1,0,.3,0,.5-.1l.1-.1ZM160.66,141.7c-1.1,4.8-2.1,9.9-5.9,13.2-1.9,1.6-3.9,3.1-6.1,4.3l-7.3,3.9v.2h0s0-.1.1-.2h0c1.3-2.5,2.8-4.9,4.9-6.8,1-.9,2.2-1.4,3.2-2.4,1.1-1,1.9-2.1,2.6-3.4,1.2-2,1.6-4.3,2.6-6.3.8-1.5,1.8-3.2,2.4-4.8.6-1.6.7-3.1,0-4.8-.7-1.9-1.6-3.2-2.5-4.9-1.1-2-1.5-4.3-1.3-6.5h0v-.92c.2.1.5.3.7.4,1.8,1,4.16,2.39,4.02,2.37,1.45,2.2,2.38,5.44,2.38,5.94.4,4,1.2,6.9.3,10.8l-.1-.1Z"
                  />
                  <g data-zone="5" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="158.72"
                      cy="134.56"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(154.62 155.22) rotate(-64.3)"
                    >
                      <tspan x="0" y="0">
                        Agadir
                      </tspan>
                    </text>
                  </g>
                  <g data-zone="5" className="cls-3">
                    <circle
                      className="group-hover:fill-brand-secondary"
                      cx="248.02"
                      cy="8.26"
                      r="1.4"
                    />
                    <text
                      className="fill-brand-primary text-[6px] font-medium pointer-events-none group-hover:fill-secondary"
                      transform="translate(244.06 14.12) rotate(25.01)"
                    >
                      <tspan x="0" y="0">
                        Tetouan
                      </tspan>
                    </text>
                  </g>
                </g>
              </g>
            </svg>
          </div>
        </div>
      </aside>
    </div>
  );
}
