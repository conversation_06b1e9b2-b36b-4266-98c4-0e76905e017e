"use client";

import DimensioningIllustration from "../illustrations/dimenioning-illustration";
import SelectableArea from "./selectable-area";
import StepQuestion from "./step-question";

export default function DimensioningStep({ state, setState }) {
  return (
    <div className="flex">
      <aside className="hidden md:flex items-center justify-center w-full md:w-1/2">
        {/* <img src="/assets/images/simulators/injection/dimensioning.svg" /> */}
        <DimensioningIllustration />
      </aside>
      <aside className="flex items-center justify-center w-full md:w-1/2">
        <div className="flex flex-col items-center gap-4">
          <StepQuestion
            number={3}
            question="Comment souhaitez-vous dimensionner votre installation solaire ?"
          />
          <div className="flex flex-nowrap gap-3 sm:gap-4">
            <SelectableArea
              selected={state.dimensioning === 1}
              className="rounded bg-secondary text-brand-primary"
              onClick={() =>
                setState((state) => ({
                  ...state,
                  dimensioning: 1,
                  consumption: {
                    ...state.consumption,
                    monthlyConsumptions: {},
                    items: [],
                  },
                }))
              }
            >
              <div className="py-3 px-4 sm:px-6 [&>svg]:w-28 [&>svg]:h-28 [&>svg]:mx-auto">
                <svg width="104" height="118" viewBox="0 0 104 118" fill="none">
                  <g
                    clipPath="url(#clip0_2931_1017)"
                    className="[&>path]:fill-brand-primary"
                  >
                    <path d="M49.8984 17.1191V74.8032H104V17.1191H49.8984ZM95.8491 39.8873L74.4243 70.0314C73.0821 70.7509 71.6889 70.3052 71.1798 68.8709L72.7488 51.8865H59.4838C58.5489 51.8865 57.5168 49.663 58.2434 48.7996C64.0613 40.1426 68.9304 30.7986 74.739 22.1462C75.6925 20.7258 76.7987 18.4467 78.8953 19.7789C80.3301 20.6934 79.5757 22.4108 79.5664 23.562C79.5155 28.1945 79.261 32.7621 79.3119 37.3993H94.4143C95.1086 37.3993 96.3166 39.0704 95.8491 39.8873Z" />
                    <path d="M80.3621 94.8232L60.1406 114.839V94.8232H80.3621Z" />
                    <path d="M35.2226 33.4909C35.2133 33.4027 35.1994 33.3145 35.1855 33.231C35.1948 33.2727 35.2041 33.3145 35.2087 33.3609C35.2179 33.4027 35.2226 33.4491 35.2226 33.4909Z" />
                    <path d="M47.404 12.9043H83.5147V0H0V118H55.9388V92.8459C55.9388 92.3214 56.7858 90.6086 57.3828 90.6086H83.1213L83.5147 90.214V79.0179H47.6678C47.1356 79.0179 45.9692 78.0431 45.8118 77.449L45.6822 15.1324C45.3675 14.2643 46.7699 12.9043 47.404 12.9043ZM8.27094 14.5754C8.74766 14.1019 9.40027 13.8048 10.1223 13.8048H32.6163C34.065 13.8048 35.236 14.9838 35.236 16.4321C35.236 16.4553 35.236 16.4831 35.2313 16.5064C35.2174 17.2026 34.9259 17.8339 34.4676 18.2935C34.4214 18.3399 34.3797 18.3817 34.3288 18.4234C33.8706 18.8226 33.2735 19.064 32.6163 19.064H10.1223C8.67824 19.064 7.50262 17.885 7.50262 16.4321C7.50262 15.708 7.79421 15.0488 8.27094 14.5754ZM8.27094 31.9033C8.74766 31.4252 9.40027 31.1328 10.1223 31.1328H32.6163C32.9542 31.1328 33.2735 31.1978 33.5651 31.3138C33.6253 31.3324 33.6808 31.3602 33.7364 31.3881C33.7965 31.4113 33.8474 31.4391 33.903 31.4763C33.9539 31.5041 34.0094 31.5366 34.0603 31.5737C34.2594 31.6991 34.4352 31.8569 34.5834 32.0333C34.6343 32.0936 34.6805 32.1493 34.7268 32.2143C34.8101 32.3211 34.8796 32.4371 34.9351 32.5625C34.9583 32.6089 34.986 32.6553 35.0045 32.7017C35.0231 32.7296 35.0323 32.762 35.0462 32.7945C35.0601 32.8224 35.0693 32.8456 35.0786 32.8735C35.0878 32.8827 35.0925 32.892 35.0925 32.9059C35.1017 32.9245 35.1064 32.9431 35.111 32.957C35.1203 32.9756 35.1249 32.9941 35.1295 33.0173C35.1434 33.0591 35.1526 33.1055 35.1665 33.1473C35.1712 33.1752 35.1758 33.203 35.185 33.2309C35.1943 33.2726 35.2036 33.3144 35.2082 33.3608C35.2174 33.4026 35.2221 33.449 35.2221 33.4908C35.2313 33.5651 35.236 33.6394 35.236 33.7183V33.76L35.2267 33.9318C35.1897 34.5909 34.9073 35.1804 34.4676 35.6168C33.9955 36.0949 33.3429 36.3873 32.6163 36.3873H10.1223C8.67824 36.3873 7.50262 35.2129 7.50262 33.76C7.50262 33.0313 7.79421 32.3768 8.27094 31.9033ZM8.27094 48.4468C8.74766 47.9687 9.40027 47.6763 10.1223 47.6763H32.6163C34.065 47.6763 35.236 48.8553 35.236 50.3082V50.336C35.2313 50.9395 35.0184 51.4965 34.6713 51.9375H34.6667C34.6065 52.021 34.5371 52.0953 34.4676 52.1649C33.9955 52.6384 33.3429 52.9354 32.6163 52.9354H10.1223C8.67824 52.9354 7.50262 51.7564 7.50262 50.3082C7.50262 49.584 7.79421 48.9249 8.27094 48.4468ZM8.27094 65.5984C8.74766 65.1249 9.40027 64.8278 10.1223 64.8278H32.6163C34.065 64.8278 35.236 66.0069 35.236 67.4551C35.236 68.1792 34.9444 68.8384 34.4676 69.3118C33.9955 69.7899 33.3429 70.0824 32.6163 70.0824H10.1223C8.67824 70.0824 7.50262 68.908 7.50262 67.4551C7.50262 66.731 7.79421 66.0718 8.27094 65.5984ZM8.27094 82.3368C8.74766 81.8587 9.40027 81.5663 10.1223 81.5663H32.6163C34.065 81.5663 35.236 82.7406 35.236 84.1935V84.2353C35.2267 84.8063 35.0369 85.3354 34.713 85.7625C34.713 85.7718 34.7037 85.7764 34.6944 85.7857C34.6296 85.8785 34.551 85.9667 34.4676 86.0503C33.9955 86.5284 33.3429 86.8208 32.6163 86.8208H10.1223C8.67824 86.8208 7.50262 85.6464 7.50262 84.1935C7.50262 83.4694 7.79421 82.8103 8.27094 82.3368ZM47.7511 98.7735C49.1952 98.7735 50.3708 99.9479 50.3708 101.401C50.3708 102.125 50.0792 102.779 49.6025 103.258C49.1258 103.736 48.4732 104.028 47.7511 104.028H10.1223C8.67824 104.028 7.50262 102.849 7.50262 101.401C7.50262 100.672 7.79421 100.018 8.27094 99.5441C8.74766 99.066 9.40027 98.7735 10.1223 98.7735H47.7511Z" />
                    <path d="M89.5548 41.6138L76.2898 60.3157L77.2941 47.7085L64.0801 47.6714L74.8504 29.7632L74.2579 41.6138H75.3039H89.5548Z" />
                  </g>
                  <defs>
                    <clipPath id="clip0_2931_1017">
                      <rect width="104" height="118" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div className="bg-brand-primary rounded-b text-white px-3 py-2.5">
                <span className="text-sm font-medium">
                  Factures <br />
                  énergétiques
                </span>
              </div>
            </SelectableArea>
            <SelectableArea
              selected={state.dimensioning === 2}
              className="rounded bg-secondary text-brand-primary"
              onClick={() =>
                setState((state) => ({
                  ...state,
                  dimensioning: 2,
                  consumption: {
                    ...state.consumption,
                    monthlyConsumptions: {},
                    items: [],
                  },
                }))
              }
            >
              <div className="py-3 px-4 sm:px-6 [&>svg]:w-28 [&>svg]:h-28 [&>svg]:mx-auto">
                <svg width="123" height="123" viewBox="0 0 123 123" fill="none">
                  <g
                    clipPath="url(#clip0_2931_1024)"
                    className="[&>path]:fill-brand-primary"
                  >
                    <path d="M111.215 106.98C111.177 106.925 111.136 106.874 111.095 106.819H111.047L111.222 106.991C111.222 106.991 111.215 106.984 111.215 106.98ZM117.222 27.6719H91.4731V57.0073C91.4731 57.9589 89.8097 58.356 89.0499 58.4279C80.3597 59.2391 70.4409 57.7946 61.6173 58.4279C61.145 58.5443 59.4507 57.9384 59.4507 57.5208V28.0553L59.064 27.6719H52.5336L52.4001 32.0294L58.6806 35.7571L50.3465 50.2194C50.148 50.5412 50.0008 50.4453 49.7099 50.3735C48.5804 50.0928 44.196 46.7895 43.4738 46.9299C41.3552 48.7236 38.8566 49.8908 36.3923 51.1163V58.6743H27.6816V64.3121H51.8935C55.9049 64.3121 58.3726 67.985 58.6772 71.6168C59.9368 86.7159 57.7326 103.496 58.6909 118.769C58.5403 120.262 58.1741 121.703 57.4006 122.986H117.735C117.845 122.986 119.481 122.312 119.741 122.175C121.637 121.162 122.64 119.296 122.989 117.225L122.999 33.6827C123.03 31.0299 119.82 27.6719 117.222 27.6719ZM111.643 108.288C111.643 109.219 110.89 109.975 109.959 109.975H83.7687C82.8377 109.975 82.0813 109.219 82.0813 108.288V108.103C82.0813 107.172 82.8377 106.415 83.7687 106.415H109.959C110.373 106.415 110.753 106.566 111.047 106.819L111.222 106.991C111.485 107.285 111.643 107.675 111.643 108.103V108.288ZM111.643 96.7385C111.643 97.6696 110.89 98.4261 109.959 98.4261H83.7687C82.8377 98.4261 82.0813 97.6696 82.0813 96.7385V96.5537C82.0813 95.6226 82.8377 94.8661 83.7687 94.8661H109.959C110.681 94.8661 111.294 95.3214 111.533 95.958C111.612 96.1326 111.65 96.3243 111.639 96.516C111.643 96.5297 111.643 96.54 111.643 96.5537V96.7385Z" />
                    <path d="M53.5748 68.0122C53.3387 67.8855 52.0312 67.3857 51.8943 67.3857H38.0565L37.6732 67.7691V84.9391C37.6732 85.5758 36.7833 85.7777 36.2699 85.8359C30.8997 86.4144 24.561 85.3909 19.0882 85.8462C18.5816 86.0276 17.6917 84.9904 17.6917 84.6823V67.3857H3.47057C2.07071 67.3857 -0.10268 69.5731 0.010268 71.0997L0 119.286C0.208782 121.425 1.57442 122.791 3.71358 123H51.6479C53.9787 122.942 55.5463 121.011 55.6147 118.769V71.8596C55.4573 70.2714 55.0568 68.8063 53.5748 68.0122ZM50.1761 114.305C50.1761 115.236 49.4197 115.989 48.4888 115.989H32.2277C31.3002 115.989 30.5438 115.236 30.5438 114.305V114.117C30.5438 113.186 31.3002 112.429 32.2277 112.429H48.4888C49.4197 112.429 50.1761 113.186 50.1761 114.117V114.305ZM50.1761 106.716C50.1761 107.648 49.4197 108.401 48.4888 108.401H32.2277C31.3002 108.401 30.5438 107.648 30.5438 106.716V106.528C30.5438 105.597 31.3002 104.841 32.2277 104.841H48.4888C49.4197 104.841 50.1761 105.597 50.1761 106.528V106.716Z" />
                    <path d="M48.6966 33.5492C49.7303 29.8455 49.7474 25.8919 48.7309 22.1847L54.5768 18.5768L49.1724 9.25247C48.4879 8.8554 43.9357 12.5078 42.9774 12.2647C42.5872 12.1621 41.8239 11.1112 41.379 10.7689C38.8873 8.84513 36.2074 7.7121 33.3186 6.53116V0H22.3011V6.53116C18.6081 7.90379 15.271 9.53316 12.3857 12.2716C11.6635 12.4599 8.04574 9.60504 6.93338 9.32435C6.64245 9.25247 6.49528 9.1532 6.29676 9.47839L1.05668 18.5426L6.69037 21.9485C5.88262 25.7755 5.91343 29.5579 6.6493 33.3917L1.0293 36.7771L6.56031 46.3753L12.3994 43.0687C13.7547 44.3797 15.1991 45.6325 16.842 46.5807C18.6491 47.6247 20.5247 48.0629 22.3011 49.0658V55.6004H33.3186V48.6824C37.0493 47.813 40.4035 45.7112 43.1759 43.1166L49.1963 46.3753L54.3269 37.0099C54.3611 36.7737 54.2174 36.6642 54.0736 36.5101C53.2385 35.6304 49.8877 34.2988 48.6966 33.5492ZM31.1863 36.9414C17.263 41.8192 12.5979 19.857 26.3364 18.0018C38.1925 16.3998 41.8274 33.2137 31.1863 36.9414Z" />
                    <path d="M88.4007 27.6719V54.9603L88.0173 55.3437H62.5254V27.6719H88.4007Z" />
                    <path d="M34.6 67.3857V82.7586H20.7656V67.7691L21.149 67.3857H34.6Z" />
                    <path d="M111.223 106.99L111.049 106.819C111.107 106.867 111.165 106.919 111.217 106.98C111.217 106.984 111.22 106.987 111.223 106.99Z" />
                  </g>
                  <defs>
                    <clipPath id="clip0_2931_1024">
                      <rect width="123" height="123" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div className="bg-brand-primary rounded-b text-white px-3 py-2.5">
                <span className="text-sm font-medium">
                  Inventaire <br />
                  des équipements
                </span>
              </div>
            </SelectableArea>
          </div>
        </div>
      </aside>
    </div>
  );
}
