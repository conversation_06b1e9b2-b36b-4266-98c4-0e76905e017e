import SectionTitle from "@/components/section-title";
import InjectionSimulatorContainer from "./_components/injection-simulator-container";

export default function InjectionSimulatorPage() {
  return (
    <section>
      <div className="container-lg">
        <SectionTitle title="Simulateur d'injection" />
      </div>
      <InjectionSimulatorContainer />
    </section>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Simulateur D'injection",
  alternates: {
    canonical: "/simulators/injection",
  },
};
