"use client";

import CategoryBox from "@/components/category-box";
import SectionTitle from "@/components/section-title";
import { Button } from "@/components/ui/button";
import { ChevronRightCircleIcon } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

import { Autoplay, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

const Icon = "https://via.placeholder.com/50x50";

export default function FeaturedCategories({ categories }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => setMounted(true), []);

  if (!categories?.length || !mounted) return null;

  return (
    <section>
      <SectionTitle title="Nos catégories" />

      <div className="space-y-6">
        <Swiper
          modules={[Navigation, Autoplay]}
          loop={true}
          spaceBetween={20}
          slidesPerView={2}
          autoplay={{
            delay: 3000,
            pauseOnMouseEnter: true,
            disableOnInteraction: true,
          }}
          navigation
          breakpoints={{
            1448: { slidesPerView: 6 },
            1024: { slidesPerView: 5 },
            786: { slidesPerView: 4 },
            478: { slidesPerView: 3 },
            0: { navigation: { enabled: false } },
          }}
        >
          {categories.map((category) => (
            <SwiperSlide key={category.id}>
              <CategoryBox category={category} />
            </SwiperSlide>
          ))}
        </Swiper>

        <div className="flex justify-center">
          <Button size="lg" className="px-14 rounded-full" asChild>
            <Link href="/categories">
              Explorer tout <ChevronRightCircleIcon />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
