"use client";

import { useEffect, useState } from "react";
import { Autoplay, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import ProductSmBox from "./product-sm-box";

export default function SlideBestSellers({ data }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => setMounted(true), []);

  if (!data?.length || !mounted) return null;

  return (
    <section>
      <div className="title d-block">
        <h2>Nos meilleures ventes</h2>
        <span className="title-leaf"></span>
      </div>
      <div className="banner-slider product-wrapper wow fadeInUp">
        <Swiper
          modules={[Navigation, Autoplay]}
          slidesPerView={1}
          loop
          navigation
          breakpoints={{
            786: { slidesPerView: 2 },
            0: {
              navigation: { enabled: false },
            },
          }}
          pagination={{ clickable: true }}
        >
          {data.map((items, key) => (
            <SwiperSlide key={`best-seller-${key}`}>
              <div className="mx-2">
                <ul className="product-list">
                  {items.map((item, key1) => (
                    <ProductSmBox
                      product={item}
                      key={`best-seller-items-${key1}`}
                    />
                  ))}
                </ul>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
}
