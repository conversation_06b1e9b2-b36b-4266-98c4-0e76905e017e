"use client";

import Link from "next/link";
import { Autoplay, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

export default function HomeSlide({ data }) {
  if (!data?.active_items?.length) return null;

  return (
    <section className="home-section pt-0 ratio_50">
      <Swiper
        className="home-slide"
        modules={[Navigation, Autoplay]}
        loop
        navigation
        autoHeight
        slidesPerView={1}
        autoplay={{
          delay: 3000,
          pauseOnMouseEnter: true,
          disableOnInteraction: true,
        }}
        breakpoints={{
          478: { navigation: { enabled: true } },
          0: { navigation: { enabled: false } },
        }}
      >
        {data?.active_items.map((item) => (
          <SwiperSlide key={item?.id} className="h-auto">
            <>
              {item?.link ? (
                <Link href={item?.link} className="d-block h-full">
                  <picture>
                    <source
                      media="(min-width: 991px)"
                      srcSet={item.full_image_desktop}
                    />
                    {item.image_tablet && (
                      <source
                        media="(min-width: 768px)"
                        srcSet={item.full_image_tablet}
                      />
                    )}
                    {item.image_mobile && (
                      <source
                        media="(max-width: 767px)"
                        srcSet={item.full_image_mobile}
                      />
                    )}
                    <img
                      src={item.full_image_desktop}
                      alt={item.title}
                      className="w-100"
                    />
                  </picture>
                </Link>
              ) : (
                <picture>
                  <source
                    media="(min-width: 991px)"
                    srcSet={item.full_image_desktop}
                  />
                  {item.image_tablet && (
                    <source
                      media="(min-width: 768px)"
                      srcSet={item.full_image_tablet}
                    />
                  )}
                  {item.image_mobile && (
                    <source
                      media="(max-width: 767px)"
                      srcSet={item.full_image_mobile}
                    />
                  )}
                  <img
                    src={item.full_image_desktop}
                    alt={item.title}
                    className="w-100"
                  />
                </picture>
              )}
            </>
          </SwiperSlide>
        ))}
      </Swiper>
    </section>
  );
}
