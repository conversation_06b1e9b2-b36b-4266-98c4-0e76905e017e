"use client";

import BrandBox from "@/components/brand-box";
import SectionTitle from "@/components/section-title";
import { useEffect, useState } from "react";
import { Autoplay, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

const Icon = "https://via.placeholder.com/50x50";

export default function FeaturedBrands({ brands }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => setMounted(true), []);

  if (!brands?.length || !mounted) return null;

  return (
    <section>
      <SectionTitle title="Nos partenaires" />

      <div>
        <Swiper
          className="absoloute"
          modules={[Navigation, Autoplay]}
          autoplay={{
            delay: 3000,
            pauseOnMouseEnter: true,
            disableOnInteraction: true,
          }}
          loop
          spaceBetween={20}
          slidesPerView={2}
          navigation
          breakpoints={{
            1448: { slidesPerView: 6 },
            1024: { slidesPerView: 5 },
            786: { slidesPerView: 4 },
            478: { slidesPerView: 3 },
            0: { navigation: { enabled: false } },
          }}
        >
          {brands.map((brand) => (
            <SwiperSlide key={brand.id}>
              <BrandBox brand={brand} />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
}
