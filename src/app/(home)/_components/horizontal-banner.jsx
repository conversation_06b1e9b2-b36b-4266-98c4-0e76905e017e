"use client";

import { useEffect, useState } from "react";
import Link from "next/link";

export default function HorizontalBanner({ banner }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => setMounted(true), []);

  if (!banner || !mounted) return null;

  return (
    <section>
      <div className="home-contain hover-effect">
        <div className="banner-contain-3 hover-effect overflow-visible bg-size">
          <Link href={banner?.link}>
            <picture>
              <source
                media="(min-width: 991px)"
                srcSet={banner.full_image_desktop}
              />
              {banner.image_tablet && (
                <source
                  media="(min-width: 768px)"
                  srcSet={banner.full_image_tablet}
                />
              )}
              {banner.image_mobile && (
                <source
                  media="(max-width: 767px)"
                  srcSet={banner.full_image_mobile}
                />
              )}
              <img
                src={banner.full_image_desktop}
                alt={banner.title}
                className="w-100"
              />
            </picture>
          </Link>
        </div>
      </div>
    </section>
  );
}
