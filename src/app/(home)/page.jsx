import ProductsSlide from "@/components/products-slide";
import { CommonService } from "@/services/common";
import FeaturedBrands from "./_components/featured-brands";
import FeaturedCategories from "./_components/featured-categories";
import HomeSlide from "./_components/home-slide";
import Offers from "./_components/offers";

export default async function HomePage() {
  const [
    homeSlide,
    featuredCategories,
    bestOffers,
    arrivalProducts,
    offers,
    bestSellers,
    featuredBrands,
  ] = await Promise.all([
    await CommonService.getSlide("HS1"),
    await CommonService.getFeaturedCategories(),
    await CommonService.getBestOffers(),
    await CommonService.getArrivalProducts(),
    await CommonService.getOffers("HPP3"),
    await CommonService.getBestSellers(),
    await CommonService.getFeaturedBrands(),
  ]);

  return (
    <>
      <HomeSlide data={homeSlide} />

      <div className="container-lg space-y-10 mt-10 pb-40 [&>section]:!p-0">
        <FeaturedCategories categories={featuredCategories} />

        <Offers offers={offers} />

        <ProductsSlide
          title="Nos bons plans"
          products={bestOffers}
          link="/products?type=best_offer"
          linkText="Profitez-en"
        />

        <ProductsSlide
          title="Nos arrivages"
          products={arrivalProducts}
          link="/products?status=2"
          linkText="Voir les nouveautés"
        />

        {/* <HorizontalBanner banner={banner} /> */}

        <ProductsSlide
          title="Nos best-sellers"
          products={bestSellers}
          link="/products?type=best_seller"
          linkText="Voir les produits"
        />

        <FeaturedBrands brands={featuredBrands} />
      </div>
    </>
  );
}
