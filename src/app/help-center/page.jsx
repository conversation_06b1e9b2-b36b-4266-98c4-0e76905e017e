import Breadcrumb from "@/shared/components/breadcrumb";
import AccordionFaq from "./_components/accordion-faq";

export default function FaqPage() {
  const faqs = [
    {
      title: "Quels avantages de créer un compte client sur Ecowatt.ma?",
      description:
        "La création d'un compte client sur Ecowatt.ma présente de nombreux avantages. Tout d'abord, cela vous permet de suivre vos commandes en cours et passées, de gérer vos informations personnelles et de bénéficier d'offres exclusives réservées à nos membres. De plus, un compte vous offre une expérience d'achat plus rapide et plus personnalisée, car vos préférences sont enregistrées pour simplifier vos futures commandes. Génère systématiquement vos factures Cumulé des points de fidélité convertible en bons d'achat.",
    },
    {
      title: "Je ne peux pas me connecter sur Ecowatt.ma. Que faire?",
      description:
        "Si vous rencontrez des difficultés pour vous connecter, assurez-vous d'utiliser les bonnes informations d'identification. Si le problème persiste, vous pouvez réinitialiser votre mot de passe en cliquant sur 'Mot de passe oublié'. Si le problème persiste, n'hésitez pas à contacter notre service client, qui se fera un plaisir de vous aider.",
    },
    {
      title: "Y a-t-il des frais d'inscription à Ecowatt.ma?",
      description:
        "Non, l'inscription sur Ecowatt.ma est totalement gratuite. Nous ne facturons aucun frais d'inscription. Vous pouvez créer votre compte en quelques étapes simples pour profiter de tous les avantages offerts aux membres.",
    },
    {
      title: "Comment modifier mes informations personnelles sur Ecowatt.ma?",
      description:
        "Pour modifier vos informations personnelles, connectez-vous à votre compte et accédez à la section 'Mon Compte'. Vous trouverez là les options pour mettre à jour vos informations, telles que votre adresse, votre numéro de téléphone, etc.",
    },
    {
      title: "Comment passer une commande sur Ecowatt.ma?",
      description:
        "Pour passer une commande, explorez notre catalogue en ligne, ajoutez les produits désirés à votre panier, puis suivez les étapes du processus de commande. Vous aurez l'occasion de vérifier votre commande avant de finaliser l'achat. Une fois la commande passée avec succès, vous recevrez une confirmation par e-mail.",
    },
    {
      title: "Quels sont les modes de paiement sur Ecowatt.ma?",
      description:
        "Ecowatt.ma propose plusieurs modes de paiement sécurisés, notamment le paiement par carte bancaire, le virement bancaire et d'autres options populaires. Choisissez simplement le mode qui vous convient le mieux au moment du paiement lors de la finalisation de votre commande.",
    },
    {
      title: "Comment supprimer mon compte ?",
      description: [
        "Vous pouvez demander la suppression de votre compte en contactant notre service client à travers les différents moyens de communication disponibles, par exemple à l'adresse <EMAIL>.",
        "Notre équipe traitera votre demande de suppression de compte ainsi que de toutes vos données personnelles.",
        "La suppression de votre compte entraînera la perte de tout historique d'achats, remises, commandes, factures et toute autre donnée associée à votre compte ou vos transactions.",
        "Toutes vos commandes et informations connexes seront définitivement perdues.",
        "Une fois la suppression validée par nos services, il sera impossible de restaurer l'accès à votre compte.",
      ],
    },
  ];

  return (
    <>
      <Breadcrumb title="Centre d'aide" />
      <div className="static-page faq-page">
        <div className="container-sm">
          <section className="faq-section">
            <div className="faq-cards">
              <div className="faq-card col-4">
                <div className="faq-image">
                  <img src="/assets/svg/services.svg" alt="Faq" />
                </div>
                <h2>Nos services</h2>
                <p>
                  Nous proposons une gamme complète de services d'électricité
                  industrielle et d'énergie solaire pour répondre à tous vos
                  besoins énergétiques. Nos techniciens qualifiés vous
                  accompagnent à chaque étape de votre projet.
                </p>
              </div>
              <div className="faq-card col-4">
                <div className="faq-image">
                  <img src="/assets/svg/support-technique.svg" alt="Faq" />
                </div>
                <h2>Support technique</h2>
                <p>
                  notre support technique est toujours à votre disposition pour
                  optimiser et assurer le bon fonctionnement de vos
                  installationsen, garantissant une réponse rapide et efficace à
                  toutes vos préoccupations techniques
                </p>
              </div>
              <div className="faq-card col-4">
                <div className="faq-image">
                  <img src="/assets/svg/engagement.svg" alt="Faq" />
                </div>
                <h2>Notre engagement</h2>
                <p>
                  Chez Ecowatt Maroc, nous nous engageons à fournir des
                  solutions énergétiques durables et efficaces pour aider nos
                  clients à réduire leur empreinte carbone et leurs coûts
                  énergétiques. Votre satisfaction est notre priorité.
                </p>
              </div>
            </div>
          </section>

          <section className="faq-box-contain section-b-space">
            <div className="container">
              <div className="row">
                <div>
                  <div className="my-5">
                    <h2 className="text-center">
                      Questions fréquemment posées
                    </h2>
                  </div>
                  <div className="faq-accordion">
                    <div className="accordion" id="accordionExample">
                      {faqs.map((item, idx) => (
                        <AccordionFaq
                          key={idx}
                          title={item.title}
                          collapsed={idx === 0}
                        >
                          {typeof item.description === "string" ? (
                            <p>{item.description}</p>
                          ) : (
                            item.description?.map((paragraph, key) => (
                              <p key={key}>{paragraph}</p>
                            ))
                          )}
                        </AccordionFaq>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Centre d'aide",
};
