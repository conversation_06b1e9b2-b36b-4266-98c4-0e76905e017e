import '@/global.css';

import "@/css/vendors/bootstrap.css";
import "@/css/animate.min.css";
import "@/css/bulk-style.css";
import "@/css/blog.css";
import "@/css/style.css";
import "swiper/swiper-bundle.css";


import {  Roboto } from 'next/font/google'

import { getUrl } from "@/lib/helpers";

import Providers from "@/shared/components/providers";
import GlobalLayout from "@/shared/components/layouts/global-layout";
import Analytics from "@/shared/components/analytics/analytics";
import NextTopLoader from "nextjs-toploader";
import { SettingsProvider } from "@/providers/settings-provider";
import { CommonService } from "@/services/common";
import { AuthProvider } from "@/providers/auth-provider";
import { AuthService } from "@/services/auth";
import Script from 'next/script';

const roboto = Roboto({
  subsets: ['latin'],
  display: 'swap',
  weight: ['100', '300', '400', '500', '700', '900'],
  variable: '--font-roboto'
})

export default async function RootLayout({ children }) {
  const currentUser = await AuthService.getCurrentUser();
  const settings = await CommonService.getSiteSettings();


  return (
    <html lang="fr" className={roboto.className}>
      <head>
        <meta name="theme-color" content="#000000" />
        
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
          integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />

        <meta
          name="google-site-verification"
          content="BI4_LYCGyRLojo9yF0i_UpWcqAJK4kNnFvVpofj_DwM"
        />
        <meta
          name="google-site-verification"
          content="Gz9VKmc8MpR-v3zcPllVkbS-HCOs1n2GPowMKeeKsrQ"
        />
        <meta name="msvalidate.01" content="E2C1B8C0161E7FF85CEF23ECADF407BA" />
      </head>
      <body>
        <noscript>You need to enable JavaScript to run this app.</noscript>
        <div className="app-wrapper">
          <SiteJsonLD settings={settings} />
          <NextTopLoader
            color="var(--theme-color)"
            showSpinner={false}
          />
          <SettingsProvider settings={settings}>
            <AuthProvider currentUser={currentUser}>
              <Analytics />
              <Providers>
                <GlobalLayout>{children}</GlobalLayout>
              </Providers>
            </AuthProvider>
          </SettingsProvider>

          {/* <Script id="chatbot-init" type="module" strategy="afterInteractive">
            {`
              import Chatbot from "https://cdn.jsdelivr.net/npm/@denserai/embed-chat@1/dist/web.min.js";
              Chatbot.init({
                chatbotId: "39fd49fc-d209-456c-b4ff-9a580d5e0c3e",
              });
            `}
          </Script> */}
        </div>
      </body>
    </html>
  );
}

async function SiteJsonLD({settings}) {
  const jsonLD =  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "logo": settings?.store_white,
    "name": settings?.store_name,
    "url": getUrl(),
    "contactPoint": [
      {
        "@type": "ContactPoint",
        "telephone": settings?.store_phone,
        "contactOption": "None",
        "contactType": "customer support"
      }
    ],
    "sameAs": [
      settings?.sm_facebook,
      settings?.sm_instagram,
      settings?.sm_linkedin,
      settings?.sm_whatsapp
    ]
  }  

  return <script
    type="application/ld+json"
    dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLD) }}
  />
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: {
    template: "%s | Ecowatt",
    default:
      "Energie Solaire, Solutions Electriques et Hydrauliques au Maroc | Ecowatt",
  },
  description:
  "Découvrez la plateforme leader au Maroc pour l'énergie durable: solutions solaires photovoltaïques, électricité industrielle et hydraulique.",
  keywords:
    "énergie solaire, énergie durable, panneau photovoltaïque, électricité industrielle, hydraulique, énergie solaire Maroc, solutions solaires, installation solaire, Agadir, Ait Melloul, écologie, énergie renouvelable, panneaux solaires, Jinko, prix Maroc, acheter panneaux solaires, acheter panneaux solaires Maroc, Shakti pompes, tube galvanisé, panneaux solaires prix maroc, pompage solaire agadir, panneau photovoltaique maroc, pompage solaire, pompage solaire Maroc, pompe agadir, pompe immergée prix maroc, énergie solaire, pompe immergée novelli prix maroc, variateur solaire, onduleur solaire, onduleur huawei, installation solaire maroc, canadian solar prix, ecowatt hydro, ecowatt elec, ecowatt solar, prix tube galvanisé maroc, panneau solaire prix maroc, tube acier galva maroc, variateur solaire prix maroc, panneau solaire jinko maroc prix, panneau solaire jinko, programme ecowatt, plaque solaire maroc prix, solaire maroc, ecowatt energy, energie solaire, panneaux solaires maroc, panneau solaire maroc, prix panneau solaire maroc, veichi, pompe de surface prix maroc, variateur veichi fiche technique, Znshine Solar, Canadian Solar, Sunpro Power, Longi, TrinaSolar, Huawei, Solax Power, Jinko Solar, shakti fiche technique, ongrid, offgrid, kit solaire, kits solaires, kits solaires injection, kits solaires pompage, pompage solaire Fes, pompage solaire Oujda, pompage solaire Meknes, Fes, Oujda, Meknes, calcul de chute de tension, calcul de la longueur du câble électrique, calcul de section de câble suivant l'intensité, calcul de section de câble suivant la puissance, calcul rendement groupe motopompe, calcul puissance optimale du groupe monopompe, dimensionnement des conduites, électricité industrielle, photovoltaique, onduleur",
    openGraph: {
    url: getUrl(""),
    type: "website",
    title: "Énergie Solaire et Hydraulique Durable au Maroc | Ecowatt",
    description:
    "Découvrez la première plateforme au Maroc pour l'énergie durable: solutions solaires photovoltaïques, électricité industrielle et hydraulique.",
    images: ["/logo192.png"],
    siteName: "Ecowatt",
  },
  robots: "index, follow",
  authors: ["Ecowatt"],
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL),
  alternates: {
    canonical: '/'
  }
};

/** @type {import('next').Viewport} */
export const viewport = {
  initialScale: 1.0,
  width: 'device-width',
  maximumScale: 1.0,
  userScalable: 0
}
