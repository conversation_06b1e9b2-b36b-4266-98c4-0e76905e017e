import ReactConfetti from "react-confetti";
import { CheckCircle, ChevronLeft } from "react-feather";

export default function SubmitFundingRequestSuccess() {
  return (
    <div
      className="bg-theme position-relative d-flex flex-column text-center py-5 px-2"
      style={{ height: "100vh" }}
    >
      <ReactConfetti width={window.innerWidth} />
      <CheckCircle size={40} />
      <h2 className="text-success mb-1 mt-5">
        Votre demande de financement a été soumise avec succès ! 🎉
      </h2>
      <p className="p-0 text-white">
        Elle sera examinée par notre équipe dans les plus brefs délais. Nous
        vous appellerons pour plus de détails.
      </p>
      <div className="submit-blog-success-actions">
        <a href="/financement/wafasalaf" className="btn m-0 text-white">
          <ChevronLeft /> Retour à l'accueil
        </a>
      </div>
    </div>
  );
}
