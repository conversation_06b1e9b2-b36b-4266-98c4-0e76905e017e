import { formatPrice } from "@/lib/helpers";
import { useFundingProduct, useFundingSettings } from "@/services/common";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import { ChevronsRight } from "react-feather";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

export const energyTypes = [
  {
    key: "elec-bt",
    label: "Electrique BT",
    price: 1.7,
  },
  {
    key: "elec-mt",
    label: "Electrique MT",
    price: 1.1,
  },
  {
    key: "gasoil",
    label: "Gasoil",
    price: 3,
  },
  {
    key: "gaz",
    label: "Gaz butanne",
    price: 2,
  },
];

export default function Step01({ state, setState }) {
  const searchParams = useSearchParams();
  const categoryParam = searchParams.get("category");
  const powerParam = searchParams.get("p");

  const [category, setCategory] = useState(categoryParam);
  const [power, setPower] = useState(powerParam);
  const [qty, setQty] = useState(1);
  const [energyTypePrice, setEnergyTypePrice] = useState(energyTypes[0].price);

  const { product } = useFundingProduct({ category, power });

  const { fundingSettings } = useFundingSettings({
    onSuccess(data) {
      if (categoryParam) return;
      setCategory(data?.categories?.at(0)?.slug);
      setPower(data?.categories?.at(0)?.available_properties?.at(0)?.value);
    },
  });

  function handleQtyChange(value) {
    const newQty = qty + value;
    if (newQty < 1) return;

    setQty(newQty);
  }

  function handleSubmit() {
    if (!qty || !power || !product?.price_ttc) return;

    setState((state) => ({
      ...state,
      product: {
        id: product.id,
        price: product.price_ttc,
        totalPrice: product.price_ttc * qty,
        qty,
      },
    }));

    document.getElementById("step-02").scrollIntoView();
  }

  const savingsPerYear =
    Number(power?.replace(",", ".")) * 1450 * energyTypePrice;

  return (
    <div id="step-01" className="ws-simulator__step">
      <div className="ws-simulator__step-header">
        <div className="ws-simulator__step-number">01</div>
        <h2>Choisissez le produit qui vous convient !</h2>
        <div />
      </div>
      <div className="ws-simulator__step-content">
        <div className="ws-simulator__product-picker">
          {product ? (
            <div className="ws-simulator__product-preview">
              <div className="ws-simulator__product-infos">
                <div className="d-flex gap-2">
                  <div className="ws-simulator__product-image">
                    <img src={product?.full_image} alt={product?.title} />
                  </div>
                  <div className="ws-simulator__product-accessories">
                    <button id="slide-down">
                      <svg
                        width="11"
                        height="8"
                        viewBox="0 0 16 11"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8 0L15.7942 10.5H0.205771L8 0Z"
                          fill="white"
                        />
                      </svg>
                    </button>
                    <Swiper
                      modules={[Navigation]}
                      direction="vertical"
                      spaceBetween={10}
                      slidesPerView="auto"
                      className="ws-simulator__product-accessories"
                      navigation={{
                        nextEl: "#slide-up",
                        prevEl: "#slide-down",
                      }}
                      loop
                      autoHeight
                    >
                      {product?.active_accessories
                        ?.map((item) => item.related)
                        ?.map((accessory) => (
                          <SwiperSlide key={accessory.id}>
                            <Link
                              className="d-block"
                              href={`/products/${accessory.slug}`}
                              target="_blank"
                            >
                              <img
                                src={accessory.full_image}
                                alt={accessory.title}
                              />
                            </Link>
                          </SwiperSlide>
                        ))}
                    </Swiper>
                    <button id="slide-up">
                      <svg
                        width="11"
                        height="8"
                        viewBox="0 0 16 11"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8 11L0.205773 0.499999L15.7942 0.5L8 11Z"
                          fill="white"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
                <div className="d-flex gap-2">
                  <div className="ws-simulator__product-title flex-grow-1">
                    <Link href={`/products/${product.slug}`} target="_blank">
                      <h2 className="text-uppercase">{product?.title}</h2>
                    </Link>
                  </div>
                  <div style={{ width: "60px", height: "auto" }} />
                </div>
              </div>
            </div>
          ) : (
            <div className="ws-simulator__product-preview">
              <h2 className="text-center mx-auto pt-5">Chargement...</h2>
            </div>
          )}
          <div className="ws-simulator__product-settings">
            <select
              className="form-select form-control"
              value={category}
              onChange={(e) => {
                const newCategory = e.target.value;
                setCategory(newCategory);
                setPower(
                  fundingSettings?.categories
                    ?.find((c) => c.slug === newCategory)
                    ?.available_properties?.at(0)?.value
                );
              }}
            >
              {fundingSettings?.categories?.map((category) => (
                <option key={category.slug} value={category.slug}>
                  {category.name}
                </option>
              ))}
            </select>
            <select
              className="form-select form-control"
              value={power}
              onChange={(e) => setPower(e.target.value)}
            >
              {fundingSettings?.categories
                ?.find((c) => c.slug === category)
                ?.available_properties?.sort(
                  (a, b) =>
                    a?.value?.replace(",", ".") - b?.value?.replace(",", ".")
                )
                .map((property) => (
                  <option key={property.id} value={property.value}>
                    {property.value} KWC
                  </option>
                ))}
            </select>
            <select
              className="form-select form-control"
              value={energyTypePrice}
              onChange={(e) => setEnergyTypePrice(e.target.value)}
            >
              {energyTypes.map((type) => (
                <option key={type.key} value={type.price}>
                  {type.label}
                </option>
              ))}
            </select>
            <div className="qty-input">
              <button onClick={() => handleQtyChange(-1)}>-</button>
              <input
                type="number"
                value={qty}
                onChange={(e) => setQty(e.target.value)}
                readOnly
                className="fw-bold"
              />
              <button onClick={() => handleQtyChange(+1)}>+</button>
            </div>

            <div className="product-price">
              <span>
                {product?.price_ttc
                  ? `${formatPrice(product?.price_ttc * qty)} DH TTC`
                  : ""}
              </span>
            </div>

            <button
              className="ws-simulator__action-button"
              onClick={handleSubmit}
            >
              Demander mon financement
            </button>
          </div>
        </div>
        {product && (
          <div className="ws-simulator__product-saving text-center">
            <h5>
              <ChevronsRight size={14} />{" "}
              <span style={{ color: "#ff0000" }} className="fw-bold">
                {formatPrice(savingsPerYear * qty)} DH
              </span>{" "}
              économisés par an grâce à cette solution, ce qui correspond à{" "}
              <span style={{ color: "#ff0000" }} className="fw-bold">
                {formatPrice((savingsPerYear * qty) / 12)} DH
              </span>{" "}
              par mois.
            </h5>
            <h5>
              <ChevronsRight size={14} />{" "}
              <span>La mensualité comprend l'assurance décès-invalidité.</span>
            </h5>
            <h5>
              <ChevronsRight size={14} />{" "}
              <span>Les frais de dossiers sont à notre charge.</span>
            </h5>
          </div>
        )}
      </div>
    </div>
  );
}
