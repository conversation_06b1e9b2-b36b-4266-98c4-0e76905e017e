import { formatPrice } from "@/lib/helpers";
import { useEffect, useState } from "react";
import { Lock } from "react-feather";

export default function Step02({ state, setState }) {
  const [months, setMonths] = useState(6);
  const [monthlyBill, setMounthlyBill] = useState(0);

  const [monthsIndex, setMonthsIndex] = useState(0);
  const [monthlyBillIndex, setMounthlyBillIndex] = useState(0);

  const availableMonths = [6, 9, 12, 18, 24];

  const availableMonthlyBills = availableMonths.map((m) =>
    calculateMonthlyBill(m)
  );

  function handleMonthsChange(value) {
    setMonths(availableMonths[+value]);
    setMonthsIndex(+value);
    setMounthlyBillIndex(+value);
  }
  function handleMonthlyChange(value) {
    setMonths(availableMonths[+value]);
    setMonthsIndex(+value);
    setMounthlyBill(availableMonthlyBills[value]);
    setMounthlyBillIndex(+value);
  }

  function calculateMonthlyBill(nbMonths) {
    if (!state.product) return 0;

    if (state.product.price * 0.001 < 15) {
      return state.product.totalPrice / nbMonths + 15;
    }

    return (
      state.product.totalPrice / nbMonths + state.product.totalPrice * 0.001
    );
  }

  function handleSubmit() {
    setState((state) => ({
      ...state,
      fundingDetails: {
        months,
        monthlyBill,
      },
    }));

    document.getElementById("step-03").scrollIntoView();
  }

  useEffect(() => {
    setMounthlyBill(calculateMonthlyBill(months));
  }, [months, state?.product?.totalPrice]);

  return (
    <div id="step-02" className="ws-simulator__step">
      <div className="ws-simulator__step-header">
        <div className="ws-simulator__step-number">02</div>
        <h2>Simulation</h2>
        <div />
      </div>
      <div className="d-flex">
        {state.product ? (
          <div className="ws-simulator__step-content ws-simulator__step-customization">
            <div className="ws-simulator__step-customization-values"></div>
            <div className="row py-5" style={{ gap: "80px" }}>
              <div className="sw-simulator__customization-item">
                <h4 className="text-nowrap">
                  Montant (DH TTC)<i className="invisible">___</i>
                </h4>
                <div className="flex-grow-1 sw-simulator__customization-slider">
                  <span>{formatPrice(state.product.totalPrice)} DH TTC</span>
                  <input disabled type="range" className="w-100 d-block" />
                </div>
              </div>
              <div className="sw-simulator__customization-item">
                <h4 className="text-nowrap">
                  Durée (Mois)<i className="invisible">________</i>
                </h4>
                <div className="flex-grow-1 sw-simulator__customization-slider">
                  <span>{months} Mois</span>
                  <input
                    type="range"
                    min="0"
                    max={availableMonths?.length - 1}
                    step="1"
                    className="w-100 d-block"
                    value={monthsIndex}
                    onChange={(e) => handleMonthsChange(e.target.value)}
                  />
                </div>
              </div>
              <div className="sw-simulator__customization-item">
                <h4 className="text-nowrap">Mensualité (DH TTC)</h4>
                <div className="flex-grow-1 sw-simulator__customization-slider">
                  <span>{formatPrice(monthlyBill)} DH TTC</span>
                  <input
                    type="range"
                    className="w-100 d-block"
                    min="0"
                    step="1"
                    max={availableMonthlyBills?.length - 1}
                    value={monthlyBillIndex}
                    onChange={(e) => handleMonthlyChange(e.target.value)}
                  />
                </div>
              </div>
            </div>

            <div className="sw-simulator__customization-item">
              {/* <h4 className="text-nowrap invisible">
                Montant (DH TTC)<i>___</i>
              </h4> */}
              <div className="d-flex flex-column w-100 align-items-center">
                <div className="d-flex align-items-center justify-content-center gap-2">
                  <span className="ws-simulator__monthly-price">
                    {formatPrice(monthlyBill)}
                  </span>
                  <span className="ws-simulator__monthly-currency">
                    <span>DH TTC</span> <br /> Par mois
                  </span>
                </div>
                <div className="text-center mt-4">
                  <button
                    className="ws-simulator__action-button"
                    onClick={handleSubmit}
                  >
                    Valider mon financement
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div
            className="ws-simulator__step-content"
            style={{ padding: "40px 0px" }}
          >
            <p className="d-flex align-items-center gap-1 fw-bold fst-italic m-0">
              <Lock size={18} strokeWidth={2.5} />
              <span>
                Veuillez compléter l'étape précédente avant de passer à
                celle-ci.
              </span>
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
