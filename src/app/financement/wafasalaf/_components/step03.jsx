import { useSubmitFundingRequestMutation } from "@/services/common";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import { useState } from "react";
import { Lock, Mail, MapPin, Phone, User } from "react-feather";

export default function Step03({ state, onSuccess }) {
  const [formData, setFormData] = useState({});

  const { submitFundingRequest, isLoading, error } =
    useSubmitFundingRequestMutation();

  function handleFormDataChange(e) {
    setFormData((data) => ({
      ...data,
      [e.target.name]: e.target.value,
    }));
  }

  function handleSubmit(e) {
    e.preventDefault();

    submitFundingRequest(
      {
        product_id: state.product.id,
        qty: state.product.qty,
        duration: state.fundingDetails.months,
        fname: formData.fname,
        lname: formData.lname,
        city: formData.city,
        email: formData.email,
        phone: formData.phone,
      },
      {
        onSuccess() {
          onSuccess();
          window.scrollTo({ top: 0 });
        },
      }
    );
  }

  return (
    <div id="step-03" className="ws-simulator__step">
      <div className="ws-simulator__step-header">
        <div className="ws-simulator__step-number">03</div>
        <h2>Formulaire</h2>
        <div />
      </div>
      <div className="d-flex">
        {state.fundingDetails ? (
          <div className="ws-simulator__step-content">
            <form onSubmit={handleSubmit}>
              <div className="ws-simulator-form p-5">
                <div className="form-group">
                  <User size={30} className="text-white" />
                  <input
                    type="text"
                    onChange={handleFormDataChange}
                    name="fname"
                    placeholder="Nom"
                    required
                  />
                </div>
                <div className="form-group">
                  <User size={30} className="text-white" />
                  <input
                    type="text"
                    onChange={handleFormDataChange}
                    name="lname"
                    placeholder="Prénom"
                    required
                  />
                </div>
                <div className="form-group">
                  <MapPin size={30} className="text-white" />
                  <input
                    type="text"
                    onChange={handleFormDataChange}
                    name="city"
                    placeholder="Ville"
                    required
                  />
                </div>
                <div className="d-flex flex-column flex-lg-row gap-5">
                  <div className="col form-group">
                    <Phone size={30} className="text-white" />
                    <input
                      type="tel"
                      placeholder="Téléphone"
                      onChange={handleFormDataChange}
                      name="phone"
                      pattern="[0-9]{10}"
                      title="Le numéro de téléphone doit être composé de 10 chiffres et respecter ce format: 0601020304"
                      required
                    />
                  </div>
                  <div className="col form-group">
                    <Mail size={30} className="text-white" />
                    <input
                      type="email"
                      onChange={handleFormDataChange}
                      name="email"
                      placeholder="E-Mail"
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="text-center mt-5">
                {error && <ErrorSnackbar message={error.message} />}
                <div className="d-flex justify-content-center">
                  <button
                    className="ws-simulator__action-button"
                    disabled={isLoading}
                  >
                    {isLoading ? "Envoi en cours..." : "Envoyer ma demande"}
                  </button>
                </div>
              </div>
            </form>
          </div>
        ) : (
          <div
            className="ws-simulator__step-content"
            style={{ padding: "40px 0px" }}
          >
            <p className="d-flex align-items-center gap-1 fw-bold fst-italic m-0">
              <Lock size={18} strokeWidth={2.5} />
              <span>
                Veuillez compléter l'étape précédente avant de passer à
                celle-ci.
              </span>
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
