import { motion, useInView } from "framer-motion";
import { useRef } from "react";

const items = [
  {
    number: "01",
    text: "Choisissez le produit qui vous convient !",
  },
  {
    divider: true,
  },
  {
    number: "02",
    text: "Simulateur adapté à vos critères.",
  },
  {
    divider: true,
  },
  {
    number: "03",
    text: "Entrez vos informations et recevez une réponse instantanée.",
  },
];

export default function Guide() {
  const fadeInRightVariant = {
    initial: { x: -15, opacity: 0 },
    animate: (i) => ({
      x: 0,
      opacity: 1,
      transition: {
        delay: i * 0.2,
      },
    }),
  };

  const ref = useRef();
  const inView = useInView(ref, { once: true });

  return (
    <div className="ws-guide">
      <div className="ws-guide__wrapper">
        <div className="container-lg ws-guide__title">
          <h2 style={{ color: "yellow" }}>VOTRE CRÉDIT EN QUELQUES CLICS</h2>
          <div className="line" />
        </div>
      </div>
      <div ref={ref} className="container-lg">
        <div className="ws-guide_steps">
          {items.map((item, key) => (
            <>
              {item.divider ? (
                <motion.div
                  key={key}
                  custom={key}
                  variants={fadeInRightVariant}
                  initial="initial"
                  animate={inView ? "animate" : ""}
                >
                  <div className="steps-divider">
                    <span>&#x21e2;</span>
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key={key}
                  custom={key}
                  variants={fadeInRightVariant}
                  initial="initial"
                  animate={inView ? "animate" : ""}
                  className="ws-guide__step shadow-sm"
                >
                  <div className="step-number">
                    <span>{item.number}</span>
                  </div>
                  <div className="content">
                    <span>{item.text}</span>
                  </div>
                </motion.div>
              )}
            </>
          ))}
        </div>
      </div>
    </div>
  );
}
