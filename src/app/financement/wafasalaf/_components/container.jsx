"use client";

import SubmitFundingRequestSuccess from "@/app/financement/_components/submit-funding-request-success";
import { useState } from "react";
import Advantages from "./advantages";
import Guide from "./guide";
import Hero from "./hero";
import Step01 from "./step01";
import Step02 from "./step02";
import Step03 from "./step03";

export default function Container() {
  const [success, setSuccess] = useState(false);
  const [state, setState] = useState({
    product: null,
    fundingDetails: null,
    customerInfo: null,
  });

  if (success) {
    return (
      <div style={{ marginTop: "-1.5rem" }}>
        <SubmitFundingRequestSuccess />;
      </div>
    );
  }

  return (
    <div style={{ marginTop: "-1.5rem" }}>
      <Hero />
      <Guide />
      <div>
        <div className="container-lg pt-5 mt-5">
          <Step01 state={state} setState={setState} />
          <Step02 state={state} setState={setState} />
          <Step03
            state={state}
            setState={setState}
            onSuccess={() => setSuccess(true)}
          />
        </div>
        <Advantages />
      </div>
    </div>
  );
}
