import { getUrl } from "@/lib/helpers";
import "./style.css";
import Container from "./_components/container";

export default function Wafasalaf() {
  return <Container />;
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Financement Wafa Salaf",
  description:
    "Facilitez votre achat avec notre solution de financement en partenariat avec Wafa Salaf.",
  openGraph: {
    url: getUrl("/financement/wafasalaf"),
    type: "website",
    title: "Financement Wafa Salaf",
    description:
      "Facilitez votre achat avec notre solution de financement en partenariat avec Wafa Salaf.",
  },
  alternates: {
    canonical: "/financement/wafasalaf",
  },
};
