.ws-bg-theme {
  background: linear-gradient(to bottom, var(--theme-color) 37%, #4578e3 100%);
  padding-bottom: 300px;
}
.ws-hero {
  display: grid;
  grid-template-columns: 7fr 5fr;
  gap: 20px;
}
.ws-hero .ws-hero__left {
  display: flex;
  flex-direction: column;
  color: white;
}

.ws-hero .ws-hero__left h1 {
  font-size: calc(25px + 30 * (100vw - 320px) / 1600);
  line-height: 1.3;
  text-transform: none !important;
}

.ws-hero .ws-hero__left h2 {
  font-size: calc(20px + 30 * (100vw - 320px) / 1600);
  margin: 60px 0;
  line-height: 1.3;
}

.ws-hero .ws-hero__left h2 .text-shape {
  position: relative;
  z-index: 1;
  padding-bottom: 5px;
}

.ws-hero .ws-hero__left h2 .text-shape::before {
  position: absolute;
  content: "";
  left: 0;
  bottom: 0;
  height: 120%;
  transform: translateX(-10%);
  background-color: #ea0201;
  width: 100%;
  z-index: -1;
  border-top-left-radius: 50px;
  border-bottom-right-radius: 70px;
}

.ws-hero .ws-hero__left a {
  background-color: #ea0201;
  color: #fff;
  padding: 10px 40px;
  font-size: 25px;
  border-radius: 40px;
  font-weight: 600;
  cursor: pointer;
}

@media (max-width: 1024px) {
  .ws-hero {
    grid-template-columns: auto;
    gap: 60px;
  }
  .ws-hero .ws-hero__left {
    text-align: center;
  }
}

.ws-guide {
  margin-top: -150px;
}

.ws-guide .ws-guide__wrapper {
  /*padding-top: 120px;*/
}

.ws-guide .ws-guide__title {
  width: fit-content;
  position: relative;
  padding-bottom: 60px;
  text-align: center;
}

.ws-guide .ws-guide__title .line {
  margin: 20px auto 0;
  width: 50%;
  height: 5px;
  background-color: #ea0201;
}

.ws-guide .ws-guide_steps {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
  padding-bottom: 70px;
  /* border-bottom: 2px solid #ea0201; */
}
.ws-guide .ws-guide_steps .steps-divider {
  font-size: 3rem;
  color: #ea0201;
}

.ws-guide .ws-guide_steps .ws-guide__step {
  display: flex;
  flex-direction: column;
  background-color: white;
  flex: 1;
  border-radius: 10px;
  align-self: stretch;
  /* border-bottom: 3px solid #ea0201; */
}

.ws-guide .ws-guide_steps .ws-guide__step .step-number {
  background-color: #ea0201;
  width: fit-content;
  padding: 5px 30px;
  border-bottom-right-radius: 50px;
  border-top-left-radius: inherit;
  font-weight: 900;
  font-size: 1.5rem;
  color: white;
}

.ws-guide .ws-guide_steps .ws-guide__step .content {
  padding: 20px;
  font-size: 1.1rem;
  color: var(--theme-color);
  text-align: center;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 40px;
}

@media (max-width: 1024px) {
  .ws-guide .ws-guide_steps {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  .ws-guide .ws-guide_steps .steps-divider {
    transform: rotate(90deg);
  }
  .ws-guide .ws-guide_steps .ws-guide__step {
    align-self: center !important;
    max-width: 100%;
    width: 100%;
  }
}

/* ================ SIMULATOR ================== */
.ws-simulator__step {
  scroll-margin-top: 20px;
  max-width: 1024px;
  margin: 0 auto;
}

.ws-simulator__step-header {
  display: flex;
  justify-content: space-between;
  padding: 10px 40px;
  background-color: #ff0000;
  color: white;
  gap: 20px;
  align-items: center;
}
.ws-simulator__step-header h2 {
  text-align: center;
}

@media (max-width: 1024px) {
  .ws-simulator__step-header {
    padding: 10px 20px !important;
  }
}

.ws-simulator__step-number {
  display: grid;
  place-items: center;
  background-color: #fff;
  color: var(--theme-color);
  text-align: center;
  width: 45px;
  height: 45px;
  flex-shrink: 0;
  border-radius: 100%;
  font-weight: 900;
  font-size: 1.4rem;
}

.ws-simulator__step .ws-simulator__step-content {
  flex: 1;
  padding: 40px 0;
  padding-bottom: 80px;
}

@media (max-width: 1024px) {
  .ws-simulator__step-content {
    flex-direction: column;
    /* padding: 40px !important; */
  }
  .ws-simulator__step-number span {
    width: 60px;
    font-size: 1.5rem;
  }
  .ws-simulator__step-line {
    display: none !important;
  }
}

/* ===================================== */
/* ========== STEP 01 ================== */
/* ===================================== */
.ws-simulator__product-picker {
  display: flex;
  gap: 50px;
  /* border: 2px solid red; */
}
@media (max-width: 768px) {
  .ws-simulator__product-picker {
    flex-direction: column;
  }
}

.ws-simulator__product-preview,
.ws-simulator__product-settings {
  flex: 1;
  flex-shrink: 0;
}

.ws-simulator__product-preview {
  display: flex;
  gap: 10px;
  min-height: 400px;
}

.ws-simulator__product-title {
  background-color: var(--theme-color);
  margin: 5px 0;
  padding: 10px;
}
.ws-simulator__product-title h2 {
  font-size: 1.1rem;
  color: #fff;
  text-align: center;
}

.ws-simulator__product-saving {
  padding: 20px;
  margin-top: 40px;
  border: 2px solid var(--theme-color);
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.ws-simulator__product-saving h5 {
  font-weight: 600;
  font-size: 0.8rem;
}

.ws-simulator__product-saving svg {
  display: inline;
}

.ws-simulator__product-accessories {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.ws-simulator__product-accessories button {
  background-color: var(--theme-color);
  border-radius: 100%;
  aspect-ratio: 1;
  border: none;
  color: #fff;
}

.ws-simulator__product-accessories .swiper {
  /* height: 100%; */
  flex: 1;
  flex-shrink: 0 !important;
}
.ws-simulator__product-accessories .swiper-slide {
  width: 60px;
  height: 60px;
}

.ws-simulator__product-settings {
  display: flex;
  flex-direction: column;
  align-items: end;
  gap: 30px;
}

.ws-simulator__product-settings select {
  min-width: 0;
  max-width: 100% !important;
  background-color: #d9d9d9 !important;
  color: var(--theme-color);
  font-weight: 600;
  text-align: center;
}
.ws-simulator__product-settings select:focus {
  background-color: #d9d9d9 !important;
  color: var(--theme-color);
}

.ws-simulator__product-settings .qty-input {
  display: flex;
  align-items: center;
  background-color: #d9d9d9 !important;
  max-width: 100% !important;
  width: 100% !important;
  border-radius: 5px;
}

.ws-simulator__product-settings .qty-input button {
  background-color: var(--theme-color);
  height: 45px;
  width: 45px;
  aspect-ratio: 1;
  color: #ffff;
  border: none;
  border-radius: 5px;
  font-weight: 900;
}
.ws-simulator__product-settings .qty-input input {
  flex: 1;
  background-color: transparent;
  text-align: center;
  border: none;
  font-size: 1.1rem;
  color: var(--theme-color);
  min-width: 0 !important;
}

.ws-simulator__product-settings .product-price {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #d9d9d9 !important;
  max-width: 100% !important;
  width: 100% !important;
  border-radius: 5px;
  height: 45px;
  border: 2px solid var(--theme-color);
  font-weight: 900;
  font-size: 1.1rem;
}

.ws-simulator__product-settings .ws-simulator__action-button {
  height: 45px;
  width: 100%;
}

.ws-simulator__action-button {
  background-color: var(--theme-color);
  border-radius: 5px;
  border: none;
  font-weight: 600;
  color: #fff;
  height: 45px;
  padding: 0 40px;
}

/* ============================ */
/* === PICK SIMULATION STEP === */
/* ============================ */
.ws-simulator__step .ws-simulator__step-customization {
}
.ws-simulator__step .sw-simulator__customization-item {
  display: flex;
  align-items: center;
  gap: 40px;
}
.ws-simulator__step .sw-simulator__customization-item:last-child {
  margin-top: 40px;
}
.ws-simulator__step .sw-simulator__customization-item h4 {
  font-weight: 900;
  font-size: 0.9rem;
}

.ws-simulator__step .sw-simulator__customization-slider {
  position: relative;
  width: 100%;
}
.ws-simulator__step .sw-simulator__customization-slider span {
  position: absolute;
  bottom: 100%;
  right: 0;
  /* transform: translateX(-50%); */
  margin-bottom: 20px;
  padding: 5px 20px;
  background-color: var(--theme-color);
  color: #fff;
  padding: 5px 10px;
  border-radius: 10px;
  font-weight: 600;
}

@media (max-width: 767px) {
  .ws-simulator__step .sw-simulator__customization-item {
    flex-direction: column;
    align-items: start;
  }
}

.ws-simulator__step .ws-simulator__step-customization input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}
.ws-simulator__step
  .ws-simulator__step-customization
  input[type="range"]:disabled {
  cursor: auto;
}

/***** Chrome, Safari, Opera, and Edge Chromium *****/
.ws-simulator__step
  .ws-simulator__step-customization
  input[type="range"]::-webkit-slider-runnable-track {
  background: var(--theme-color);
  height: 4px;
}
/******** Firefox ********/
.ws-simulator__step
  .ws-simulator__step-customization
  input[type="range"]::-moz-range-track {
  background: var(--theme-color);
  height: 4px;
}
/***** Chrome, Safari, Opera, and Edge Chromium *****/
.ws-simulator__step
  .ws-simulator__step-customization
  input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none; /* Override default look */
  appearance: none;
  margin-top: -6px; /* Centers thumb on the track */
  background-color: #ff0000;
  height: 15px;
  width: 15px;
  border-radius: 100%;
  outline: thick double #ff0000;
}

/***** Firefox *****/
.ws-simulator__step
  .ws-simulator__step-customization
  input[type="range"]::-moz-range-thumb {
  border: none; /*Removes extra border that FF applies*/
  border-radius: 0; /*Removes default border-radius that FF applies*/
  background-color: #5cd5eb;
  height: 2rem;
  width: 1rem;
}

.ws-simulator__monthly-price {
  font-size: 3.5rem;
  color: var(--theme-color);
  font-weight: 900;
}

.ws-simulator__monthly-currency {
  color: #ff0000;
  line-height: 1.1;
}

.ws-simulator__monthly-currency span {
  font-size: 2rem;
  font-weight: 900;
}

@media (max-width: 1024px) {
  .ws-simulator__monthly-price {
    font-size: 3rem;
  }
  .ws-simulator__monthly-currency span {
    font-size: 1.5rem;
  }
}

/* ============================ */
/* === PICK FORM STEP === */
/* ============================ */
.ws-simulator-form {
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 8px;
  background-color: var(--theme-color);
  box-shadow: -8px -8px #d9d9d9;
  border-radius: 20px;
}

.ws-simulator-form .form-group {
  display: flex;
  padding-bottom: 10px;
  padding-left: 5px;
  border-bottom: 2px solid #fff;
  gap: 10px;
}

.ws-simulator-form .form-group input {
  border: none;
  background-color: transparent;
  color: #fff;
  flex: 1;
  font-weight: 600;
}

.ws-simulator-form .form-group input::placeholder {
  color: #e0e0e0;
}

.ws-simulator__footer {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}
@media (max-width: 1024px) {
  .ws-simulator-form {
    padding: 30px !important;
  }
  .ws-simulator__footer {
    grid-template-columns: repeat(2, 1fr);
    gap: 5px;
  }
}
@media (max-width: 768px) {
  .ws-simulator__footer {
    grid-template-columns: repeat(1, 1fr);
    gap: 5px;
  }
}

.ws-simulator__disabled-area {
  opacity: 0.5;
  cursor: not-allowed !important;
  pointer-events: none !important;
  user-select: none;
}
