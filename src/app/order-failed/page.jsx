import { ThumbsDown } from "react-feather";

export default function OrderFailedPage() {
  return (
    <section className="breadscrumb-section pt-0 mt-4 mb-5">
      <div className="container-lg">
        <div className="row">
          <div className="col-12">
            <div className="breadscrumb-contain breadscrumb-order">
              <div className="order-box failed-order">
                <div className="order-image">
                  <div className="checkmark">
                    <ThumbsDown />
                  </div>
                </div>

                <div className="order-contain">
                  <h3 className="theme-color">Commande échouée</h3>
                  <h5 className="text-content">
                    Le paiement a échoué et votre commande annulée
                  </h5>
                  {/* <h6>identifiant de transaction: 1708031724431131</h6> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

/** @type {import('next').Metadata} */
export const metadata = {
  title: "Commande échouée",
};
