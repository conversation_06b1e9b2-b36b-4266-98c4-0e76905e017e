"use client";

import { createContext, useContext } from "react";

const SettingsContext = createContext(null);

export const useSettingsContext = () => {
  const context = useContext(SettingsContext);

  if (!context) {
    throw new Error("useSettingsContext must be used within SettingsContext");
  }

  return context;
}

export function SettingsProvider({ children, settings }) {

  return (
    <SettingsContext.Provider value={settings}>
      {children}
    </SettingsContext.Provider>
  )
}