"use client";

import { useAuthContext } from "@/providers/auth-provider";
import MenuCategories from "@/shared/components/categories-menu";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import SearchProductsTablet from "./search-products-tablet";

export default function Menu() {
  const { authenticated } = useAuthContext();
  const pathname = usePathname();

  const [categories, setCategories] = useState(false);
  const [searchTablet, setSearchTablet] = useState(false);

  const toggleCategories = (value) => setCategories(value);
  const toggleSearchTablet = (value) => setSearchTablet(value);

  return (
    <>
      {
        <div className="d-md-none d-block">
          {searchTablet && (
            <div className={`search-mobile ${searchTablet ? "active" : ""}`}>
              <SearchProductsTablet
                toggleSearchTablet={toggleSearchTablet}
                status={searchTablet ? "active" : ""}
              />
            </div>
          )}
          <MenuCategories
            toggleCategories={toggleCategories}
            status={categories}
          />
        </div>
      }
      <div className="mobile-menu d-md-none d-block mobile-cart">
        <ul>
          <li className={`${pathname === "/" ? "active" : ""}`}>
            <Link href="/">
              <i className="iconly-Home icli"></i>
              <span>Accueil</span>
            </Link>
          </li>

          <li className={`${categories ? "active" : ""}`}>
            <button onClick={() => toggleCategories(true)}>
              <i className="iconly-Category icli js-link"></i>
              <span>Catégorie</span>
            </button>
          </li>
          <li className={`${searchTablet ? "active" : ""}`}>
            <button
              onClick={() => toggleSearchTablet(true)}
              className="search-box"
            >
              <i className="iconly-Search icli"></i>
              <span>Recherche</span>
            </button>
          </li>
          <li
            className={`${pathname === "/account/wishlist" || pathname === "/wishlist" ? "active" : ""}`}
          >
            <Link
              href={authenticated ? `/account/wishlist` : `/wishlist`}
              className="notifi-wishlist"
            >
              <i className="iconly-Heart icli"></i>
              <span>Wishlist</span>
            </Link>
          </li>
          <li className={`${pathname === "/cart" ? "active" : ""}`}>
            <Link href="/cart">
              <i className="iconly-Bag-2 icli fly-cate"></i>
              <span>Panier</span>
            </Link>
          </li>
        </ul>
      </div>
    </>
  );
}
