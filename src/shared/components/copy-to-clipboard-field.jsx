"use client";

import { useEffect, useState } from "react";
import CopyTo<PERSON>lipboard from "react-copy-to-clipboard";
import { Check, Clipboard } from "react-feather";

export default function CopyToClipboardField({ value }) {
  const [copySuccess, setCopySuccess] = useState(false);

  useEffect(() => {
    if (!copySuccess) return;
    const timer = setTimeout(() => {
      setCopySuccess(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, [copySuccess]);

  return (
    <div className="copy-to-clipboard">
      <input
        type="email"
        id="email"
        name="email"
        autoFocus={false}
        value={value}
        readOnly
      />
      <CopyToClipboard text={value} onCopy={() => setCopySuccess(true)}>
        <button className="d-flex align-items-center gap-1">
          {copySuccess ? (
            <>
              <Check size={14} /> Copié
            </>
          ) : (
            <>
              <Clipboard size={14} /> Copier
            </>
          )}
        </button>
      </CopyToClipboard>
    </div>
  );
}
