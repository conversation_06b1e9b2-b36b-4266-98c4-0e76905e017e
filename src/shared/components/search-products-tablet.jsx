"use client";

import { useDebounce } from "@/hooks/useDebounce";
import useOnClickOutside from "@/hooks/useOnClickOutside";
import { useSearchProducts } from "@/services/product";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useRef, useState } from "react";
import { Search, X } from "react-feather";
import { InfinitySpin } from "react-loader-spinner";

export default function SearchProductsTablet({ toggleSearchTablet, status }) {
  const router = useRouter();

  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const { products, isLoading } = useSearchProducts({
    title: debouncedSearchTerm,
  });

  const searchRef = useRef();
  useOnClickOutside(searchRef, () => toggleSearchTablet(false));

  const GoToSearch = (url) => {
    toggleSearchTablet(false);
    router.push(url);
  };

  return (
    <div className={`search-full ${status ? "active" : ""}`}>
      <div ref={searchRef} style={{ width: "100%" }}>
        <div className="input-group">
          <a
            className="input-group-text"
            onClick={() => GoToSearch(`/products?search=${searchTerm}`)}
          >
            <Search />
          </a>
          <input
            type="text"
            onChange={(e) => setSearchTerm(e.target.value)}
            value={searchTerm}
            tabIndex={0}
            className="form-control search-type"
            placeholder="Rechercher.."
          />
          <span
            className="input-group-text close-search"
            onClick={() => toggleSearchTablet(false)}
          >
            <X />
          </span>
        </div>
        {searchTerm.length >= 2 && (
          <div className="search-result">
            {isLoading ? (
              <div className="p-3 d-flex align-items-center justify-content-center">
                <InfinitySpin
                  type="ThreeDots"
                  color="#2A3466"
                  height={220}
                  width={220}
                  visible={isLoading}
                />
              </div>
            ) : products?.length ? (
              products.map((item) => (
                <div className="single-result" key={item?.slug}>
                  <div className="product-image">
                    <Link
                      href={`/products/${item?.slug}`}
                      onClick={() => toggleSearchTablet(false)}
                    >
                      <img
                        src={item?.full_image}
                        className="img-fluid lazyload"
                        alt={item?.title}
                      />
                    </Link>
                  </div>
                  <div className="product-info">
                    <h4>
                      <Link
                        href={`/products/${item?.slug}`}
                        onClick={() => toggleSearchTablet(false)}
                      >
                        {item?.title}
                      </Link>
                    </h4>
                  </div>
                </div>
              ))
            ) : (
              <h2 className="text-center py-5">Aucun produit trouvé</h2>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
