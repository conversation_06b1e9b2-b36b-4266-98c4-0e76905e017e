"use client";

import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";
import { useAuthContext } from "@/providers/auth-provider";
import Link from "next/link";
import { useContext } from "react";
import { Heart } from "react-feather";
import { TailSpin } from "react-loader-spinner";

export default function WishlistButton() {
  const { authenticated } = useAuthContext();
  const {
    wishlistItemsLength,
    wishlistItemsLoading,
    addWishlistLoading,
    removeWishlistLoading,
  } = useContext(CartAndWishlistProvider);

  return (
    <li className="right-side">
      <Link
        href={authenticated ? `/account/wishlist` : `/wishlist`}
        className="btn p-0 position-relative header-wishlist"
      >
        <Heart />
        <span className="position-absolute top-0 start-100 translate-middle badge">
          {wishlistItemsLoading ||
          addWishlistLoading ||
          removeWishlistLoading ? (
            <TailSpin
              color="#fff"
              height={10}
              width={10}
              visible={
                wishlistItemsLoading ||
                addWishlistLoading ||
                removeWishlistLoading
              }
            />
          ) : (
            wishlistItemsLength
          )}
        </span>
      </Link>
    </li>
  );
}
