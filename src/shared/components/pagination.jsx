import { DOTS, usePagination } from "@/hooks/usePagination";
import classnames from "classnames";
// import './pagination.scss';
const Pagination = (props) => {
  const {
    onPageChange,
    totalCount,
    siblingCount = 1,
    currentPage,
    pageSize,
    className,
  } = props;

  const paginationRange = usePagination({
    currentPage,
    totalCount,
    siblingCount,
    pageSize,
  });

  if (currentPage == 0 || !paginationRange || paginationRange.length < 2) {
    return null;
  }

  const onNext = () => {
    onPageChange(currentPage + 1);
  };

  const onPrevious = () => {
    onPageChange(currentPage - 1);
  };

  let lastPage = paginationRange[paginationRange.length - 1];
  return (
    <nav
      className={classnames("custome-pagination", { [className]: className })}
    >
      <ul
        className={classnames("pagination justify-content-center", {
          [className]: className,
        })}
      >
        <li
          className={classnames("page-item", {
            disabled: currentPage == 1,
          })}
          onClick={onPrevious}
        >
          <span className="page-link">
            <i className="fa-solid fa-angles-left"></i>
          </span>
        </li>
        {paginationRange.map((page, key) => {
          if (page === DOTS) {
            return (
              <li className="page-item dots" key={key}>
                &#8230;
              </li>
            );
          }

          return (
            <li
              key={key}
              className={classnames("page-item", {
                selected: page == currentPage,
              })}
              onClick={() => onPageChange(page)}
            >
              <span className={`page-link`}>{page}</span>
            </li>
          );
        })}
        <li
          className={classnames("page-item", {
            disabled: currentPage == lastPage,
          })}
          onClick={onNext}
        >
          <span className="page-link">
            <span className="fa-solid fa-angles-right"></span>
          </span>
        </li>
      </ul>
    </nav>
  );
};

export default Pagination;
