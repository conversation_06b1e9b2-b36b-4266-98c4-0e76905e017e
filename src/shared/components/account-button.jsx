import { useAuthContext } from "@/providers/auth-provider";
import { useLogoutMutation } from "@/services/auth";
import Link from "next/link";
import { User } from "react-feather";

export default function AccountButton() {
  const { currentUser, authenticated } = useAuthContext();
  const { logout, isLoading } = useLogoutMutation();

  return (
    <li className="right-side onhover-dropdown">
      <div className="delivery-login-box">
        <div className="delivery-icon">
          <User />
        </div>
        <div className="delivery-detail">
          <h6>{currentUser?.full_name ?? "Guest"}</h6>
        </div>
      </div>
      <div className="onhover-div onhover-div-login">
        <ul className="user-box-name">
          {authenticated ? (
            <>
              <li className="product-box-contain">
                <i></i>
                <Link href={`/account`}>Mon compte</Link>
              </li>
              <li className="product-box-contain">
                <button type="button" onClick={logout} disabled={isLoading}>
                  Se déconnecter
                </button>
              </li>
            </>
          ) : (
            <>
              <li className="product-box-contain">
                <i></i>
                <Link href={`/check-order`}>Mes commandes</Link>
              </li>
              <li className="product-box-contain">
                <i></i>
                <Link href={`/login`}>Se connecter</Link>
              </li>
              <li className="product-box-contain">
                <Link href={`/register`}>S'inscrire</Link>
              </li>
            </>
          )}
        </ul>
      </div>
    </li>
  );
}
