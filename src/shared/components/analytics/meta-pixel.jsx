"use client";
import useMounted from "@/hooks/useMounted";
import Script from "next/script";

const MetaPixel = () => {
  const mounted = useMounted();

  if (!mounted) return null;

  return (
    <>
      <Script
        id="facebook-pixel-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window,document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
         fbq('init', ${process.env.NEXT_PUBLIC_META_PIXEL_ID}); 
        fbq('track', 'PageView');
        `,
        }}
      />
      <noscript id="facebook-pixel-image">
        {`<img height="1" width="1" 
        src="https://www.facebook.com/tr?id=${process.env.NEXT_PUBLIC_META_PIXEL_ID}&ev=PageView
        &noscript=1"/>`}
      </noscript>
    </>
  );
};

export default MetaPixel;
