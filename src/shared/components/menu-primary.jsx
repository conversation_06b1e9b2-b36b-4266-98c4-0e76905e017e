"use client";

import Link from "next/link";

export default function MenuPrimary({ menu, toggleMenu }) {
  return (
    <div className="header-nav-middle">
      <div className="main-nav navbar navbar-expand-xl navbar-light navbar-sticky">
        <div
          className={`offcanvas offcanvas-collapse order-xl-2 ${
            menu ? "show" : ""
          }`}
          id="primaryMenu"
        >
          <div className="offcanvas-header navbar-shadow">
            <h5>Menu</h5>
            <button
              className="btn-close lead"
              type="button"
              onClick={() => toggleMenu(false)}
            ></button>
          </div>
          <div className="offcanvas-body">
            <ul className="navbar-nav">
              <li className="nav-item">
                <Link className="nav-link" href="/about">
                  À propos de nous
                </Link>
              </li>
              <li className="nav-item">
                <div className="dropdown">
                  <Link className="nav-link dropdown-toggle" href="">
                    Simulateurs
                  </Link>
                  <ul className="d-xl-none">
                    <li className="dropdown-item">
                      <Link
                        className="nav-link"
                        href="/simulators/calculation-notes"
                      >
                        Note de Calcul
                      </Link>
                    </li>
                    <li title="à venir" className="dropdown-item">
                      <Link className="nav-link" href="/simulators/injection">
                        Simulateur Injection
                      </Link>
                    </li>
                    <li title="à venir" className="dropdown-item">
                      <Link className="nav-link" href="/simulators/pumping">
                        Simulateur Pompage
                      </Link>
                    </li>
                  </ul>
                  <ul className="dropdown-menu">
                    <li>
                      <Link
                        className="dropdown-item"
                        href="/simulators/calculation-notes"
                      >
                        Note de Calcul
                      </Link>
                    </li>
                    <li title="à venir">
                      <Link
                        className="dropdown-item"
                        href="/simulators/injection"
                      >
                        Simulateur Injection
                      </Link>
                    </li>
                    <li title="à venir">
                      <Link
                        className="dropdown-item"
                        href="/simulators/pumping"
                      >
                        Simulateur Pompage
                      </Link>
                    </li>
                  </ul>
                </div>
              </li>

              <li className="nav-item">
                <Link className="nav-link" href="/gallery">
                  Galerie
                </Link>
              </li>

              <li className="nav-item">
                <div className="dropdown">
                  <Link
                    className="nav-link dropdown-toggle"
                    href="/financement/wafasalaf"
                  >
                    Financement
                  </Link>
                  <ul className="d-xl-none">
                    <li title="à venir" className="dropdown-item">
                      <Link className="nav-link" href="/financement/wafasalaf">
                        Wafasalaf
                      </Link>
                    </li>
                    <li title="à venir" className="dropdown-item">
                      <Link className="nav-link disabled" href="/">
                        Bank al yousr
                      </Link>
                    </li>
                  </ul>
                  <ul className="dropdown-menu">
                    <li title="à venir">
                      <Link
                        className="dropdown-item"
                        href="/financement/wafasalaf"
                      >
                        Wafasalaf
                      </Link>
                    </li>
                    <li title="à venir">
                      <Link className="dropdown-item disabled" href="/">
                        Bank al yousr
                      </Link>
                    </li>
                  </ul>
                </div>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/academy">
                  Académie
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/blogs">
                  Blogs
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/careers">
                  Carrières
                </Link>
              </li>
              <li className="nav-item">
                <Link className="nav-link" href="/contact">
                  Nos agences
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div
          className={`offcanvas-backdrop fade ${menu ? "show" : ""}`}
          style={{ visibility: menu ? "visible" : "hidden" }}
          onClick={() => toggleMenu(false)}
        ></div>
      </div>
    </div>
  );
}
