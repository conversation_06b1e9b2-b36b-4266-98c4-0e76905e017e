"use client";

import useMounted from "@/hooks/useMounted";
import { getUrl } from "@/lib/helpers";
import { createPortal } from "react-dom";
import CopyToClipboardField from "./copy-to-clipboard-field";

export default function ShareProduct({ modalRef, product, open, onClose }) {
  const mounted = useMounted();

  if (!mounted) return null;

  return createPortal(
    <div>
      {open && <div className={`modal-backdrop fade show pe-auto`}></div>}
      <div
        className={`modal fade theme-modal ${open ? "show d-block" : "d-none"}`}
        aria-labelledby="exampleModalLabel2"
        aria-modal={open}
        aria-hidden={!open}
      >
        <div className="modal-dialog share-product-content modal-md mt-5">
          <div
            ref={modalRef}
            className="modal-content"
            style={{ minHeight: "0" }}
          >
            <div className="modal-header">
              <h5 className="modal-title" id="exampleModalLabel2">
                Partager
              </h5>
              <button
                type="button"
                className="btn-close"
                onClick={() => onClose(false)}
              >
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
            <div className="p-3">
              <div>
                <CopyToClipboardField
                  value={getUrl(`/products/${product.slug}`)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
}
