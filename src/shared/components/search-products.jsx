"use client";

import { useDebounce } from "@/hooks/useDebounce";
import useOnClickOutside from "@/hooks/useOnClickOutside";
import { useSearchProducts } from "@/services/product";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useRef, useState } from "react";
import { Search } from "react-feather";
import { InfinitySpin } from "react-loader-spinner";

export default function SearchProducts() {
  const router = useRouter();

  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const { products, isLoading } = useSearchProducts({
    title: debouncedSearchTerm,
  });

  const searchRef = useRef(null);
  useOnClickOutside(searchRef, () => setSearchTerm(""));

  const GoToSearch = (url) => {
    setSearchTerm("");
    router.push(url);
  };

  return (
    <div className="middle-box" ref={searchRef}>
      <div className="search-box">
        <div className="input-group">
          <input
            tabIndex={0}
            type="text"
            value={searchTerm}
            className="form-control"
            placeholder="Rechercher.."
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <a
            className="btn sub-btn btn-animation"
            id="button-addon2"
            onClick={() => GoToSearch(`/products?search=${searchTerm}`)}
          >
            <Search />
          </a>
        </div>
      </div>
      {searchTerm.length >= 2 && (
        <div className="search-result">
          {isLoading ? (
            <div className="p-3 d-flex align-items-center justify-content-center">
              <InfinitySpin
                type="ThreeDots"
                color="#2A3466"
                height={220}
                width={220}
              />
            </div>
          ) : products?.length ? (
            products.map((item) => (
              <div className="single-result" key={item?.slug}>
                <div className="product-image">
                  <Link href={`/products/${item?.slug}`}>
                    <img
                      src={item?.full_image}
                      className="img-fluid lazyload"
                      alt={item?.title}
                    />
                  </Link>
                </div>
                <div className="product-info">
                  <h4>
                    <Link href={`/products/${item?.slug}`}>{item?.title}</Link>
                  </h4>
                </div>
              </div>
            ))
          ) : (
            <h2 className="text-center my-5">Aucun produit trouvé</h2>
          )}
        </div>
      )}
    </div>
  );
}
