"use client";

import useOnClickOutside from "@/hooks/useOnClickOutside";
import { useStructuredCategories } from "@/services/common";
import { LayoutGridIcon } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useRef } from "react";

export default function CategoriesMenu({ status, toggleCategories }) {
  const pathname = usePathname();
  const { structuredCategories } = useStructuredCategories();
  const ref = useRef();
  useOnClickOutside(ref, () => toggleCategories && toggleCategories(false));

  useEffect(() => {
    toggleCategories?.();
  }, [pathname]);

  return (
    <div className="header-nav-left nav-categories">
      <Link href="/categories" className="dropdown-category">
        <LayoutGridIcon />
        <span>Nos catégories</span>
      </Link>

      <div ref={ref} className={`category-dropdown ${status ? "active" : ""}`}>
        <div className="category-title">
          <h5>Nos catégories</h5>
          <button
            type="button"
            className="btn p-0 close-button text-content"
            onClick={() => toggleCategories(false)}
          >
            <i className="fa-solid fa-xmark"></i>
          </button>
        </div>

        {structuredCategories && (
          <ul className="category-list">
            {structuredCategories
              ?.sort((a, b) => (a.name > b.name ? 1 : -1))
              .map((item, key) => {
                const name =
                  item?.name?.[0]?.toUpperCase() +
                  item?.name?.slice(1)?.toLowerCase();
                return (
                  <li
                    className="onhover-category-list"
                    key={`menu-category-${key}`}
                  >
                    <Link
                      href={`/products?categories=${item?.slug}`}
                      className="category-name"
                    >
                      <h6>{name}</h6>
                      {item?.active_childrens &&
                      item?.active_childrens.length ? (
                        <i className="fa-solid fa-angle-right"></i>
                      ) : null}
                    </Link>
                    {item?.active_childrens && item?.active_childrens.length ? (
                      <div className="onhover-category-box">
                        <div className="list-1">
                          <ul>
                            {item?.active_childrens
                              .sort((a, b) => (a.name > b.name ? 1 : -1))
                              .map((sub, keysub) => {
                                const name =
                                  sub?.name?.[0]?.toUpperCase() +
                                  sub?.name?.slice(1)?.toLowerCase();

                                return (
                                  <li key={`menu-sub-category-${keysub}`}>
                                    <Link
                                      href={`/products?categories=${sub?.slug}`}
                                    >
                                      {name}
                                    </Link>
                                    {sub?.active_childrens &&
                                    sub?.active_childrens.length ? (
                                      <div className="onhover-category-box">
                                        <div className="list-1">
                                          <ul>
                                            {sub?.active_childrens
                                              .sort((a, b) =>
                                                a.name > b.name ? 1 : -1
                                              )
                                              .map((sub1, keysub1) => {
                                                const name =
                                                  sub1?.name[0]?.toUpperCase() +
                                                  sub1?.name
                                                    ?.slice(1)
                                                    ?.toLowerCase();
                                                return (
                                                  <li
                                                    key={`menu-sub-category-${keysub1}`}
                                                  >
                                                    <Link
                                                      href={`/products?categories=${sub1?.slug}`}
                                                    >
                                                      {name}
                                                    </Link>
                                                  </li>
                                                );
                                              })}
                                          </ul>
                                        </div>
                                      </div>
                                    ) : null}
                                  </li>
                                );
                              })}
                          </ul>
                        </div>
                      </div>
                    ) : null}
                  </li>
                );
              })}
          </ul>
        )}
      </div>
    </div>
  );
}
