"use client";

import CartAndWishlistContext from "@/contexts/CartAndWishlistContext";
import { queryClient } from "@/lib/query";
import AOS from "aos";
import "aos/dist/aos.css";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { useEffect } from "react";
import { QueryClientProvider } from "react-query";

export default function Providers({ children }) {
  useEffect(() => {
    AOS.init({
      duration: 800,
      once: true,
    });
  }, []);

  return (
    <NuqsAdapter>
      <QueryClientProvider client={queryClient}>
        <CartAndWishlistContext>{children}</CartAndWishlistContext>
      </QueryClientProvider>
    </NuqsAdapter>
  );
}
