import { formatPrice } from "@/lib/helpers";

// TODO: replace product price with the one from @/components/product-price
export default function ProductPrice({ product }) {
  return (
    <div>
      {product?.new_price && product?.new_price?.discount ? (
        <h6 className="price theme-color text-exo">
          {formatPrice(product?.new_price?.new_price)} DH
          <sup style={{ fontSize: "10px", fontWeight: "bold" }}> / TTC</sup>
          <br />
          <span>{formatPrice(product?.price_ttc)} DH</span>{" "}
          <span className="discount-badge">
            -{product?.new_price?.discount}%
          </span>
        </h6>
      ) : product?.is_best_offer ? (
        <h6 className="price theme-color text-exo">
          {formatPrice(product?.price_ttc)} DH
          <sup style={{ fontSize: "10px", fontWeight: "bold" }}> / TTC</sup>
          <br />
          <span>
            {formatPrice(Math.floor(product?.price_ttc * 1.05))} DH
          </span>{" "}
          <span className="discount-badge">-{5}%</span>
        </h6>
      ) : (
        <h6 className="price theme-color text-exo">
          {formatPrice(product?.price_ttc)} DH
          <sup style={{ fontSize: "10px", fontWeight: "bold" }}> / TTC</sup>
        </h6>
      )}
    </div>
  );
}
