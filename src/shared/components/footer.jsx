import { CommonService } from "@/services/common";
import Link from "next/link";
import { Mail, Phone } from "react-feather";
import NewsletterForm from "./newsletter-form";

export default async function Footer() {
  const settings = await CommonService.getSiteSettings();

  const featuredCategories = await CommonService.getFeaturedCategories();

  return (
    <footer className="section-t-space">
      <div className="container-lg">
        <div className="main-footer section-b-space section-t-space">
          <div className="row g-md-4 g-3">
            <div className="col-xl-4 col-lg-3 col-sm-6">
              <div className="footer-logo">
                {settings && settings?.store_white && (
                  <div className="theme-logo">
                    <Link href="/">
                      <img
                        src={settings?.store_white}
                        className="lazyload"
                        alt={settings?.store_name ?? ""}
                      />
                    </Link>
                  </div>
                )}

                {settings && settings?.store_description && (
                  <div
                    className="html__content"
                    dangerouslySetInnerHTML={{
                      __html: settings.store_description,
                    }}
                  />
                )}

                <div className="mt-3">
                  <NewsletterForm />
                </div>

                <div className="mt-5">
                  <div className="footer-contact">
                    <ul>
                      {settings && settings?.store_email && (
                        <li>
                          <a href={`mailto:${settings?.store_email}`}>
                            <div className="footer-number">
                              <Mail />
                              <div>
                                <h5>{settings?.store_email}</h5>
                              </div>
                            </div>
                          </a>
                        </li>
                      )}
                      {settings && settings?.store_phone && (
                        <li>
                          <a href={`tel:${settings?.store_phone}`}>
                            <div className="footer-number">
                              <Phone />
                              <div>
                                <h5>{settings?.store_phone}</h5>
                              </div>
                            </div>
                          </a>
                        </li>
                      )}

                      {settings &&
                        (settings?.android_link || settings?.apple_link) && (
                          <li className="social-app mb-0"></li>
                        )}
                    </ul>
                  </div>
                </div>

                {/* <div className="footer-logo-contain">
                  {(settings && settings?.store_description) && <p>{settings?.store_description}</p>}
                </div> */}
              </div>
            </div>

            <div className="col-xl-2 col-lg-0" />

            <div className="col-xl-2 col-lg-3 col-sm-6">
              <div className="footer-title">
                <h4>Catégories</h4>
              </div>
              <div className="footer-contain">
                <ul>
                  {featuredCategories?.map((category) => (
                    <li key={category.slug}>
                      <Link
                        href={`/products?categories=${category?.slug}`}
                        className="text-content"
                      >
                        {category.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="col-xl-2 col-lg-3 col-sm-6">
              <div className="footer-title">
                <h4>Explore</h4>
              </div>
              <div className="footer-contain">
                <ul>
                  <li>
                    <Link href="/about" className="text-content">
                      À propos de nous
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/simulators/calculation-notes"
                      className="text-content"
                    >
                      Simulateurs
                    </Link>
                  </li>
                  <li>
                    <Link href="/" className="text-content">
                      Financement
                    </Link>
                  </li>
                  <li>
                    <Link href="/academy" className="text-content">
                      Académie
                    </Link>
                  </li>
                  <li>
                    <Link href="/blogs" className="text-content">
                      Blogs
                    </Link>
                  </li>
                  <li>
                    <Link href="/careers" className="text-content">
                      Carrières
                    </Link>
                  </li>
                  <li>
                    <Link href="/contact" className="text-content">
                      Nos agences
                    </Link>
                  </li>
                </ul>
              </div>
            </div>

            <div className="col-xl-2 col-lg-3 col-sm-6">
              <div className="footer-title">
                <h4>Liens utiles</h4>
              </div>
              <div className="footer-contain">
                <ul>
                  <li>
                    <Link href="/customer-guide" className="text-content">
                      Guide d'utilisation
                    </Link>
                  </li>
                  <li>
                    <Link href="/order-guide" className="text-content">
                      Comment commander
                    </Link>
                  </li>
                  <li>
                    <Link href="/track-order" className="text-content">
                      Suivi de votre commande
                    </Link>
                  </li>
                  <li>
                    <Link href="/delivery" className="text-content">
                      Modes de livraison
                    </Link>
                  </li>
                  <li>
                    <Link href="/payment" className="text-content">
                      Modes de paiement
                    </Link>
                  </li>
                  <li>
                    <Link href="/became-seller" className="text-content">
                      Devenir revendeur
                    </Link>
                  </li>
                  <li>
                    <Link href="/cgv" className="text-content">
                      Conditions générales de vente (CGV)
                    </Link>
                  </li>
                  <li>
                    <Link href="/help-center" className="text-content">
                      Centre d'aide
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="sub-footer section-small-space">
          {settings && settings?.copyright && (
            <div className="reserve">
              <h6 className="text-content">{settings?.copyright}</h6>
            </div>
          )}

          {settings && settings?.payment_images && (
            <div className="payment">
              <img src={settings?.payment_images} className="lazyload" alt="" />
            </div>
          )}

          <div className="social-link">
            {/* <h6 className="text-content">Stay connected :</h6> */}
            <ul>
              {settings && settings?.sm_facebook && (
                <li>
                  <a
                    href={settings?.sm_facebook}
                    target="_blank"
                    rel="noreferrer"
                    style={{ backgroundColor: "#3b5998" }}
                  >
                    <i className="fa-brands fa-facebook-f"></i>
                  </a>
                </li>
              )}
              {settings && settings?.sm_instagram && (
                <li>
                  <a
                    href={settings?.sm_instagram}
                    target="_blank"
                    rel="noreferrer"
                    style={{ backgroundColor: "#bd32a2" }}
                  >
                    <i className="fa-brands fa-instagram"></i>
                  </a>
                </li>
              )}
              {settings && settings?.sm_linkedin && (
                <li>
                  <a
                    href={settings?.sm_linkedin}
                    target="_blank"
                    rel="noreferrer"
                    style={{ backgroundColor: "#0070ac" }}
                  >
                    <i className="fa-brands fa-linkedin"></i>
                  </a>
                </li>
              )}
              {settings && settings?.sm_whatsapp && (
                <li>
                  <a
                    href={settings?.sm_whatsapp}
                    target="_blank"
                    rel="noreferrer"
                    style={{ backgroundColor: "#25D366" }}
                  >
                    <i className="fa-brands fa-whatsapp"></i>
                  </a>
                </li>
              )}
            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
}
