"use client";

import { useRegisterNewsletterMutation } from "@/services/common";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { Formik } from "formik";
import { Tail<PERSON><PERSON> } from "react-loader-spinner";
import { object, string } from "yup";

const formSchema = object({
  email: string().email("Adresse e-mail non valide").required("Required"),
});

export default function NewsletterForm() {
  const { registerNewsletter, isLoading, isSuccess, error } =
    useRegisterNewsletterMutation();

  function onSubmit(data, actions) {
    registerNewsletter(data, {
      onSuccess() {
        actions.resetForm({
          values: {
            email: "",
          },
        });
      },
    });
  }

  return (
    <Formik
      initialValues={{ email: "" }}
      validationSchema={formSchema}
      onSubmit={onSubmit}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
      }) => (
        <form onSubmit={handleSubmit}>
          <div className="mb-1">
            <span className="text-secondary">
              <i className="fa-solid fa-envelope arrow"></i> Abonnez-vous à
              notre Newsletter
            </span>
          </div>
          <div className="d-flex gap-2">
            <div className="relative">
              <input
                type="email"
                name="email"
                className="form-control"
                placeholder="Votre adresse email"
                onChange={handleChange}
                onBlur={handleBlur}
                value={values.email}
              />
              {/* <i className="fa-solid fa-envelope arrow"></i> */}
            </div>
            <button
              type="submit"
              className="px-3 ml-2 sub-btn btn-animation"
              disabled={isLoading}
            >
              {isLoading ? (
                <TailSpin
                  type="ThreeDots"
                  color="#fff"
                  height={20}
                  width={20}
                />
              ) : (
                <span className="ml-2">S'abonner</span>
              )}
            </button>
          </div>
          <span className="error-form">
            {errors.email && touched.email && errors.email}
          </span>

          {isSuccess && <SuccessSnackbar message="Vous avez bien enregistré" />}

          {error && <ErrorSnackbar message={error.message} />}

          <span className="text-secondary mt-2">
            Restez en contact avec nous pour recevoir nos dernières actualités
          </span>
        </form>
      )}
    </Formik>
  );
}
