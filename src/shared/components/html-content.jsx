"use client";

import { useEffect, useRef } from "react";

export default function HtmlContent({ html, className = "" }) {
  const containerRef = useRef(null);

  useEffect(() => {
    // Create a shadow root if it doesn't exist
    if (containerRef.current && !containerRef.current.shadowRoot) {
      containerRef.current.attachShadow({ mode: "open" });
    }

    // Insert HTML content into the shadow root
    if (containerRef.current?.shadowRoot) {
      containerRef.current.shadowRoot.innerHTML = `
          <style>
            .html--content {
              font-family: var(--font-roboto);
              margin: 0;
              padding: 0;
              font-size: calc(16px + 1 * (100vw - 320px) / 1600);
              color: #222222;
            }
          </style>
          <div class=${`html--content ${className}`}>
            ${html}
          </div>
      `;
    }
  }, [html]);

  return <div ref={containerRef}></div>;
}
