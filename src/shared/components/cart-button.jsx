"use client";

import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";
import { formatPrice } from "@/lib/helpers";
import { useAuthContext } from "@/providers/auth-provider";
import Link from "next/link";
import { useContext, useState } from "react";
import { ShoppingCart } from "react-feather";
import { TailSpin } from "react-loader-spinner";
import CartHeaderItem from "./cart-header-item";

export default function CartButton() {
  const { authenticated, currentUser } = useAuthContext();
  const {
    cartItemsLength,
    cartCalculation,
    cartItems,
    removeFromCartMutation,
    removeGuestCartItem,
    cartItemsLoading,
    cartItemsFetching,
    addCartLoading,
    updateCartLoading,
    removeCartLoading,
    combineCartLoading,
  } = useContext(CartAndWishlistProvider);
  const [loading, setLoading] = useState(null);

  const RemoveCartItem = async (move) => {
    setLoading(move);
    try {
      if (authenticated) await removeFromCartMutation(move);
      else await removeGuestCartItem(move);
      setLoading(null);
      // setAddItemInWishlist(true);
    } catch (error) {
      // // console.log('addToWishListMutation error => ', error)
      // if (error.response.data.message === 'Item founded on the Wishlist') {
      //     setAddItemInWishlist(true);
      // }
      setLoading(null);
    }
  };

  return (
    <li className="right-side">
      <div className="onhover-dropdown header-badge">
        <Link
          href={`/cart`}
          className="btn p-0 position-relative header-wishlist"
        >
          <ShoppingCart />
          <span className="position-absolute top-0 start-100 translate-middle badge">
            {cartItemsLoading ||
            cartItemsFetching ||
            addCartLoading ||
            updateCartLoading ||
            removeCartLoading ||
            combineCartLoading ? (
              <TailSpin
                color="#fff"
                height={10}
                width={10}
                visible={
                  cartItemsLoading ||
                  cartItemsFetching ||
                  addCartLoading ||
                  updateCartLoading ||
                  removeCartLoading ||
                  combineCartLoading
                }
              />
            ) : (
              cartItemsLength
            )}
          </span>
        </Link>

        <div className="onhover-div cart-side">
          {cartItems && cartItems.length ? (
            <>
              <ul className="cart-list">
                {cartItemsLoading ||
                cartItemsFetching ||
                addCartLoading ||
                updateCartLoading ||
                removeCartLoading ||
                combineCartLoading ? (
                  <TailSpin
                    color="#fff"
                    height={10}
                    width={10}
                    visible={
                      cartItemsLoading ||
                      cartItemsFetching ||
                      addCartLoading ||
                      updateCartLoading ||
                      removeCartLoading ||
                      combineCartLoading
                    }
                  />
                ) : (
                  cartItems.map((item, key) => (
                    <CartHeaderItem
                      key={`cart-item-header-${key}`}
                      item={item}
                      loading={loading}
                      RemoveCartItem={RemoveCartItem}
                    />
                  ))
                )}
              </ul>
              {cartCalculation ? (
                <>
                  <div className="price-box">
                    <h5>Total :</h5>
                    <h4 className="theme-color fw-bold">
                      {formatPrice(cartCalculation?.total)} DH
                    </h4>
                  </div>

                  <div className="button-group">
                    <Link href={`/cart`} className="btn btn-sm cart-button">
                      Voir le panier
                    </Link>
                    {!authenticated ||
                    (authenticated &&
                      currentUser &&
                      currentUser?.status !== 1) ? (
                      <Link
                        href={"/checkout"}
                        className="btn btn-sm cart-button theme-bg-color text-white"
                      >
                        Commander
                      </Link>
                    ) : null}
                  </div>
                </>
              ) : (
                <div />
              )}
            </>
          ) : (
            <div>
              <h2 className="text-center my-5 no-data-found">
                Aucun produit trouvé
              </h2>
            </div>
          )}
        </div>
      </div>
    </li>
  );
}
