export default function Modal({ title, open = false, onClose, children }) {
  if (!open) return null;

  return (
    <>
      <div className={`modal-backdrop fade show`}></div>
      <div
        className="modal fade theme-modal d-block show"
        tabIndex="-1"
        aria-labelledby="add new adddress"
        aria-modal={open}
        aria-hidden={!open}
      >
        <div className="modal-dialog modal-lg modal-dialog-centered modal-fullscreen-sm-down">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title" id="exampleModalLabel2">
                {title}
              </h5>
              <button
                type="button"
                className="btn-close"
                onClick={() => onClose()}
              >
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
            {children}
          </div>
        </div>
      </div>
    </>
  );
}
