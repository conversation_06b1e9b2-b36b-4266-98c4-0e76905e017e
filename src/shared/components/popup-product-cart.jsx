"use client";

import { useContext } from "react";
import { CartAndWishlistProvider } from "@/contexts/CartAndWishlistContext";

export default function PopupProductCart() {
  const { showPopup, closePopup } = useContext(CartAndWishlistProvider);

  if (!showPopup) return null;

  return (
    <div className={`product-popup modal fade theme-modal show`}>
      <div className="modal-dialog modal-lg modal-dialog-centered modal-fullscreen-sm-down">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">Produit ajouté à votre panier</h5>
          </div>
          <div className="buttons">
            <button type="button" onClick={closePopup}>
              Continuer vos achats
            </button>
            <button
              type="button"
              onClick={() => (window.location.href = "/cart")}
            >
              Voir le panier
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
