"use client";

import { useAuthContext } from "@/providers/auth-provider";
import Link from "next/link";
import { TailSpin } from "react-loader-spinner";
import ProductPrice from "./product-price";

export default function CartHeaderItem({ item, loading, RemoveCartItem }) {
  const { authenticated } = useAuthContext();

  const RemoveItem = async () => {
    let id = authenticated ? item?.cart_id : item?.id;
    RemoveCartItem(id);
  };

  return (
    <li className="product-box-contain">
      <div className="drop-cart">
        <Link href={`/products/${item?.slug}`} className="drop-image">
          <img src={item?.image_link} className="lazyload" alt={item?.name} />
        </Link>

        <div className="drop-contain">
          <Link href={`/products/${item?.slug}`}>
            <h5>{item?.name}</h5>
          </Link>
          <ProductPrice
            product={{
              ...item,
              price_ttc: item.price,
            }}
          />
          <button onClick={RemoveItem} className="close-button close_button">
            <i className="fa-solid fa-xmark"></i>
          </button>
        </div>
      </div>
      {loading && (
        <div className="laoding-cart-item">
          <TailSpin type="ThreeDots" color="#2A3466" height={25} width={25} />
        </div>
      )}
    </li>
  );
}
