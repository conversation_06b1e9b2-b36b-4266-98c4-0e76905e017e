export function formatPrice(price) {
  return new Intl.NumberFormat("fr-FR", {
    style: "decimal",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price);
}

export function formatDate(date) {
  const formattedDate = new Date(date * 1000);
  return formattedDate.toLocaleDateString("fr-FR");
}

export function getUrl(segment = "") {
  return `${process.env.NEXT_PUBLIC_APP_URL}${segment}`;
}

export function isServer() {
  return typeof window === "undefined";
}

/**
 * create a form data instance from an object
 * @param source Object
 * @returns {FormData}
 */
export function toFormData(source) {
  const formData = new FormData();

  Object.entries(source).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      formData.append(key + "[]", value);
    } else {
      formData.append(key, value);
    }
  });

  return formData;
}

export function getProductStatus(status) {
  const productStatusColor = {
    1: "#027A40",
    2: "#fe7a00",
    3: "#fe0100",
  };

  const productStatusLabel = {
    1: "En stock",
    2: "En arrivage",
    3: "Sur commande",
  };

  if (![1, 2, 3].includes(status)) {
    return {
      color: productStatusColor[3],
      label: productStatusLabel[3],
    };
  }

  const color = productStatusColor[status] ?? "hsl(var(--brand-primary))";
  const label = productStatusLabel[status];

  return {
    color,
    label,
  };
}
