.ps-wrapper {
  max-width: 768px;
  margin: 20px auto;
  /* padding: 0 20px; */
}
.ps-container {
  display: flex;
}
.ps-container .sp-side {
  position: relative;
  flex: 1;
}
.ps-container .sp-side .sp-side-title {
  text-align: center;
  border-radius: 0;
  padding: 15px;
  background-color: var(--theme-color);
  color: #fff;
}
.ps-container .sp-side.sp-side-solution .sp-side-title {
  background-color: #ff6b6b;
}
/* Form */
.ps-container .sp-side .add-equipement-form .form-control {
  padding: 5px 10px !important;
  height: 45px !important;
}
.ps-container .sp-side .add-equipement-form .submit-button {
  height: 45px !important;
  font-size: 0.8rem;
}
/* List */
.ps-container .sp-side .equipements {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.ps-container .sp-side .equipements .equipements-item {
  padding: 10px;
  /* border: 1px solid red; */
  background-color: rgb(246, 246, 246);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
