.static-banner {
  margin-top: -1.5rem; /* deal with the header default bottom margin. see line 22965 */
}

.filter-blogs-by-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.filter-blogs-by-categories .checkbox-label {
  display: inline-block;
  padding: 5px 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 0.8rem;
  cursor: pointer;
  user-select: none;
}
.filter-blogs-by-categories label.active .checkbox-label {
  background-color: var(--theme-color);
  color: white;
  border-color: var(--theme-color);
}

.category-badge {
  background-color: #e0e0e0;
  color: #000;
  font-size: 0.8rem;
  padding: 0.2rem 0.5rem;
  border-radius: 0.2rem;
  margin-left: 2px;
  margin-top: 2px;
}
.article-img-all {
  width: 107%;
  height: 174px;
  margin-top: 0px;
  border-radius: 10px;
}

.popular-wriers-container {
  margin-top: 0% !important;
}

@media (min-width: 768px) {
  .category-blogs {
    margin-left: 16%;
  }
}
@media (max-width: 768px) {
  .article-img {
    width: 89%;
    height: 216px;
  }
  .article-img-back {
    width: 40%;
  }
  .filter-blogs .checkbox-label {
    width: 49%;
    margin-left: 0px;
    box-sizing: border-box;
  }
}
@media (max-width: 600px) {
  .filter-blogs {
    flex-direction: column;
    align-items: center;
  }

  .filter-blogs label {
    width: 100%;
    text-align: center;
  }

  .filter-blogs .checkbox-label {
    width: 90%;
    box-sizing: border-box;
  }
}
.popular-blogs span {
  background-color: rgb(255, 34, 34);
  color: white;
  padding: auto;
}

.blog-thumbnail {
  position: relative;
  display: block;
  width: 100%;
  aspect-ratio: 1.7 / 1;
  background-color: #f5f5f5;
  border-radius: 10px;
}
.blog-thumbnail.blog-thumbnail-shape::after {
  position: absolute;
  content: "";
  width: 50%;
  height: 70%;
  top: 0;
  left: 0;
  transform: translateX(-10px) translateY(-10px);
  background-color: var(--theme-color);
  z-index: -1;
  border-radius: inherit;
}

.blog-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: inherit;
}

.profile-blog {
  width: 31px;
  height: 31px;
  border-radius: 50%;
  object-fit: cover;
}
.user-info-blogs {
  margin-left: -9px;
}
.row-blogs {
  width: 25px;
  height: 19px;
  margin-top: 2px;
  margin-left: 5px;
}

.description-content-blog {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word; /**/
  overflow-wrap: break-word;
}

.title-writers-blogs span {
  background-color: rgb(255, 34, 34);
  color: white;
  padding: auto;
}
.blog-statistics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}
.blog-statistics-card {
  background-color: #f5f5f5;
  padding: 20px;
  text-align: center;
  border-radius: 10px;
}
.linkedin-posts {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}
.linkedin-posts img {
  width: 100%;
  aspect-ratio: 1 / 1;
  object-fit: cover;
  object-position: center;
  border-radius: 10px;
}

.image-wrapper {
  width: 100px;
  height: 100px !important;
  padding-top: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.image-wrapper img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.card-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word; /**/
  overflow-wrap: break-word;
}

.submit-blog-cta {
  padding: 60px;
  background-color: #1d33602b;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  text-align: center;
}

.submit-blog-cta h3 {
  color: #222222;
}

.submit-blog-cta a {
  display: flex;
  align-items: center;
  gap: 20px;
  color: var(--theme-color);
  border: 1px solid var(--theme-color);
  padding: 5px 15px;
  border-radius: 5px;
  font-size: 1rem;
}
.submit-blog-cta a:hover {
  background-color: var(--theme-color);
  color: #fff;
}
/* ================================ */
/* ======= Write blog ======= */
/* ================================ */
.submit-blog {
  margin-bottom: 100px;
}
.submit-blog .selected-categories {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}
.submit-blog .selected-categories span {
  padding: 2px 10px;
  border-radius: 99px;
  background-color: var(--theme-color);
  color: #fff;
}
.submit-blog .submit-blog-form-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.submit-blog .submit-blog-form-actions a {
  border: 1px solid #90979e;
  background-color: #90979e;
  color: #fff;
  border-radius: 5px;
  padding: 10px 30px 5px;
  font-size: 1rem;
}
.submit-blog .submit-blog-form-actions button {
  border: 1px solid var(--theme-color);
  background-color: var(--theme-color);
  color: #fff;
  border-radius: 5px;
  padding: 10px 30px 5px;
  font-size: 1rem;
}
.submit-blog .submit-blog-form-actions button:disabled {
  opacity: 0.8;
}
.submit-blog-success {
  color: green;
  padding: 60px;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  text-align: center;
}
.submit-blog-success p {
  max-width: 500px;
}
.submit-blog-success .submit-blog-success-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}
/* ================================ */
/* =======single page blogg ======= */
/* ================================ */

.blog-details-container {
  margin: 0 auto;
  max-width: 720px !important;
}
