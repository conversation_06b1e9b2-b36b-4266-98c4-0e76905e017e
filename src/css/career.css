.careers-banner {
  margin-top: -1.5rem;
  padding: 5rem;
  text-align: center;
  background-color: var(--theme-color);
  background: linear-gradient(
      to bottom right,
      rgba(0, 0, 0),
      rgba(0, 0, 0, 0.6),
      rgba(0, 0, 0, 0.4),
      rgba(0, 0, 0, 0.6),
      rgba(0, 0, 0)
    ),
    url("/assets/images/careers-banner.webp");
  background-size: cover;
  background-position: center;
  color: white;
}

@media (max-width: 768px) {
  .careers-banner {
    padding: 3rem;
  }
}

.career-search {
  position: relative;
  transform: translateY(-50%);
  width: 50%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  padding: 3px;
  border: 1px solid #b2b2b2;
  background-color: #f4f4f4;
  display: flex;
}
.career-search input {
  padding: 10px;
  flex: 1;
  background-color: transparent;
  border: none;
}
.career-search input::placeholder {
  color: #000;
}

.career-search button {
  background-color: var(--theme-color);
  color: #fff;
  border-radius: inherit;
  border: none;
  width: 40px;
  aspect-ratio: 1 / 1;
}

.career-filters h5 {
  font-weight: 600;
  color: #484848;
}
.career-filters .form-select {
  height: fit-content !important;
  padding-top: 15px;
  font-size: 0.9rem !important;
  border: none !important;
}
.career-filters .form-floating label {
  background-color: #f4f4f4 !important;
}
.career-filters select {
  padding-right: 30px;
  min-width: 180px;
  background-color: #f4f4f4 !important;
  border-radius: 3px;
  border: none;
}
.career-filters select:focus {
  background-color: #f4f4f4 !important;
  border: none;
}

.career-card {
  position: relative;
  padding: 30px;
  background-color: #f4f4f4;
  border-radius: 3px;
}

.career-card-content-list {
  display: flex;
  align-items: center;
  gap: 10px 20px;
  flex-wrap: wrap;
}

.career-card-title h2 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--theme-color) !important;
}
.career-card-title h2:hover {
  text-decoration: underline;
}
.career-card-title h2 span {
  font-size: 0.7rem;
  vertical-align: middle;
  color: #999999;
}

.career-card-content-list li {
  color: #484848;
  display: flex;
  align-items: center;
  gap: 5px;
}

.career-card-content-list li span {
  font-weight: 400;
  font-size: 0.9rem;
}

.career-card-closing-date {
  font-size: 0.8rem;
}

.career-card-link {
  position: absolute;
  top: 15px;
  right: 15px;
  border: none;
}

.career-load-more {
  border: none;
  padding: 5px 30px;
  display: flex;
  align-items: center;
  margin: 0 auto;
  gap: 5px;
}

.no-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  text-align: center;
}
.no-records p {
  font-size: 1.4rem;
  font-weight: 500;
}

.no-records a {
  background-color: #f4f4f4;
  padding: 2px 10px;
}
.no-records a:hover {
  color: currentColor;
}

/* Career Details */

.career-details-container {
  margin: 0 auto;
  /* max-width: 720px !important; */
}

.career-apply-now {
  transform: translateY(-50%);
  color: #000;
  display: flex;
  justify-content: center;
}

.career-apply-now button {
  background-color: #fff;
  font-weight: 600;
  padding: 10px 40px;
  border-radius: 99px;
  border: 1px solid var(--theme-color);
  color: var(--theme-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.career-apply-now button:hover {
  color: var(--theme-color);
}

/* Career Applty Form */
.career-apply-warning {
  position: relative;
  background-color: #ffea83;
}

.career-apply-warning .close-btn {
  position: absolute;
  top: 0;
  right: 0;
  border: none;
  background-color: transparent;
}

.career-apply-form-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.career-apply-form-actions a {
  border: 1px solid #90979e;
  background-color: #90979e;
  color: #fff;
  border-radius: 5px;
  padding: 10px 30px 5px;
  font-size: 1rem;
}
.career-apply-form-actions button {
  border: 1px solid var(--theme-color);
  background-color: var(--theme-color);
  color: #fff;
  border-radius: 5px;
  padding: 10px 30px 5px;
  font-size: 1rem;
}
.career-apply-form-actions button:disabled {
  opacity: 0.8;
}

#career-apply-form {
  scroll-padding: 50px;
}
