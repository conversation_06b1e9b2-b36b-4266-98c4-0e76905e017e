import Link from "next/link";

export default function CategoryBox({ category }) {
  return (
    <div>
      <Link href={`/products?categories=${category.slug}`}>
        <div className="group relative !bg-secondary overflow-hidden aspect-square rounded-lg">
          <img
            src={category.full_image ?? Icon}
            alt={category.name}
            className="object-cover object-center w-full h-full "
          />
          <div className="absolute flex left-0 bottom-0 w-full p-1.5 items-center justify-center text-center bg-brand-primary">
            <h6
              title={category.name}
              className="text-white text-md truncate leading-5 !font-medium"
            >
              {category.name}
            </h6>
          </div>
        </div>
      </Link>
    </div>
  );
}
