"use client";

import ProductBox from "@/components/product-box";
import SectionTitle from "@/components/section-title";
import { ChevronRightCircleIcon } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Autoplay, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import { Button } from "./ui/button";

export default function ProductsSlide({
  title,
  products,
  link = null,
  linkText = null,
  autoplay = false,
}) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => setMounted(true), []);

  if (!products?.length || !mounted) return null;

  return (
    <section>
      {title && <SectionTitle title={title} />}

      <div className="space-y-6">
        <Swiper
          modules={[Navigation, Autoplay]}
          slidesPerView={2}
          spaceBetween={10}
          loop
          autoplay={
            autoplay && {
              delay: 3000,
              pauseOnMouseEnter: true,
              disableOnInteraction: true,
            }
          }
          navigation
          breakpoints={{
            1448: { slidesPerView: 6 },
            1100: { slidesPerView: 5 },
            786: { slidesPerView: 4 },
            478: { slidesPerView: 3 },
            0: { navigation: { enabled: false } },
          }}
        >
          {products.map((product) => (
            <SwiperSlide key={product.id} className="!h-auto !self-stretch">
              <ProductBox product={product} />
            </SwiperSlide>
          ))}
        </Swiper>

        {link && linkText && (
          <div className="flex justify-center">
            <Button size="lg" className="px-14 rounded-full" asChild>
              <Link href={link}>
                {linkText} <ChevronRightCircleIcon />
              </Link>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}
