import { formatPrice } from "@/lib/helpers";

export default function ProductPrice({ product }) {
  if (product?.new_price && product?.new_price?.discount) {
    return (
      <div>
        <h6 className="!font-bold text-lg text-exo">
          {formatPrice(product.new_price?.new_price)} DH
          <sup className="text-[10px]"> /TTC</sup>
        </h6>
        <div className="flex items-center gap-1 text-brand-ternary">
          <h6 className="font-bold text-md line-through text-exo">
            {formatPrice(product?.price_ttc)} DH
            <sup className="text-[10px]"> /TTC</sup>
          </h6>
          <div className="relative pr-1.5 pl-2 text-white rounded-[2px] text-[0.65rem] font-medium bg-brand-ternary -rotate-12">
            <div className="absolute left-0.5 top-0.5 w-1 h-1 bg-white rounded-full" />
            <span>-{product.new_price?.discount}%</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <h6 className="font-bold text-lg text-exo">
        {formatPrice(product.price_ttc)} DH
        <sup className="text-[10px]"> /TTC</sup>
      </h6>
    </div>
  );
}
