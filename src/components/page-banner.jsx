export default function PageBanner({ title, background }) {
  const isVideo = background.endsWith(".mp4");

  return (
    <div className="relative h-[55vh] overflow-hidden">
      {isVideo ? (
        <video
          autoPlay
          muted
          loop
          className="absolute inset-0 h-full w-full object-cover"
        >
          <source src={background} type="video/mp4" />
        </video>
      ) : (
        <img
          src={background}
          alt="pag-banner"
          className="absolute inset-0 h-full w-full object-cover"
        />
      )}
      <div className="absolute inset-0 bg-[#1D3D71]/80 grid place-items-center p-6">
        <h1
          className="text-2xl sm:text-5xl text-white normal-case line-clamp-2 text-center"
          data-aos="fade-up"
        >
          {title}
        </h1>
      </div>
    </div>
  );
}
