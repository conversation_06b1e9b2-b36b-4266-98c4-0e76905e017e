import { Slot } from "@radix-ui/react-slot";
import { cva } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 border-2 border-transparent whitespace-nowrap rounded text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        "brand-primary":
          "bg-brand-primary text-white shadow hover:bg-brand-primary/90",
        "brand-secondary":
          "bg-brand-secondary text-white shadow hover:bg-brand-secondary/90",
        "brand-ternary":
          "bg-brand-ternary text-white shadow hover:bg-brand-ternary/90",
        danger: "bg-danger text-danger-foreground shadow-sm hover:bg-danger/90",
        outline:
          "border-2 !border-brand-primary bg-transparent shadow-sm hover:!bg-secondary",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-brand-primary hover:text-white",
        link: "text-brand-primary underline-offset-4 hover:underline",
        whatsapp: "bg-[#40c351] text-white shadow hover:bg-[#40c351]/90",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 px-3 text-xs",
        lg: "h-10 px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "brand-primary",
      size: "default",
    },
  }
);

const Button = React.forwardRef(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
