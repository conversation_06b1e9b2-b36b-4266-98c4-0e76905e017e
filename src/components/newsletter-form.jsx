"use client";

import { useRegisterNewsletterMutation } from "@/services/common";
import ErrorSnackbar from "@/shared/components/error-snackbar";
import SuccessSnackbar from "@/shared/components/success-snackbar";
import { SendIcon } from "lucide-react";
import { useState } from "react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";

export default function NewsletterForm() {
  const [email, setEmail] = useState("");

  const { registerNewsletter, isLoading, isSuccess, error } =
    useRegisterNewsletterMutation();

  function onSubmit(e) {
    e.preventDefault();

    registerNewsletter(
      { email },
      {
        onSuccess() {
          setEmail("");
        },
      }
    );
  }

  return (
    <div>
      <form onSubmit={onSubmit} className="flex items-center gap-2">
        <Input
          type="email"
          className="bg-white"
          placeholder="Entrez votre email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
        <Button variant="brand-secondary" disabled={isLoading}>
          <SendIcon />
          S'abonner
        </Button>
      </form>
      {isSuccess && <SuccessSnackbar message="Vous avez bien enregistré" />}

      {error && <ErrorSnackbar message={error.message} />}
    </div>
  );
}
