"use client";

import { useInView, useMotionValue, useSpring } from "framer-motion";
import { useEffect, useRef } from "react";

/**
 * @param value
 * @param direction
 * @param prefix
 * @param {import('react').HTMLProps<'span'> & {value: number, direction: 'up' | 'down', prefix?: string } } props
 */
export default function Count({
  value,
  direction = "up",
  prefix = "",
  ...props
}) {
  const ref = useRef(null);
  const motionValue = useMotionValue(direction === "down" ? value : 0);
  const springValue = useSpring(motionValue, {
    damping: 100,
    stiffness: 100,
  });
  const isInView = useInView(ref, { once: true, margin: "-50px" });

  useEffect(() => {
    if (isInView) {
      motionValue.set(direction === "down" ? 0 : value);
    }
  }, [motionValue, isInView]);

  useEffect(
    () =>
      springValue.on("change", (latest) => {
        if (ref.current) {
          ref.current.textContent =
            prefix + Intl.NumberFormat("en-US").format(latest.toFixed(0));
        }
      }),
    [springValue]
  );

  return (
    <span ref={ref} {...props}>
      {prefix} {direction === "down" ? value : 0}
    </span>
  );
}
