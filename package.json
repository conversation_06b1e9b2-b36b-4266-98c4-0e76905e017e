{"name": "ecowatt", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@tinymce/tinymce-react": "^5.1.1", "aos": "^2.3.4", "axios": "^1.4.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "formik": "^2.4.3", "framer-motion": "^10.16.3", "ky": "^1.5.0", "lucide-react": "^0.476.0", "moment": "^2.29.4", "next": "^14.2.5", "nextjs-toploader": "^1.6.12", "nprogress": "^0.2.0", "nuqs": "^2.4.1", "react": "^18.2.0", "react-confetti": "^6.1.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "react-ga4": "^2.1.0", "react-hook-form": "^7.55.0", "react-loader-spinner": "^5.4.5", "react-query": "^3.39.3", "schema-dts": "^1.1.2", "swiper": "^11.1.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tinymce": "^7.2.1", "yup": "^1.2.0", "zod": "^3.24.2"}, "devDependencies": {"autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "^14.2.7", "husky": "^9.0.11", "lint-staged": "^15.2.2", "postcss": "^8.5.1", "prettier": "^3.2.5", "prettier-plugin-organize-imports": "^3.2.4", "tailwindcss": "^3.4.17"}, "lint-staged": {"**/*.{js,jsx,css,md}": ["prettier --write"]}}